@echo off
echo Starting Medical Home Sampling Application...
echo.

echo Checking MySQL service...
net start mysql80 >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: MySQL service might not be running.
    echo Please make sure MySQL is running on localhost:3306
    echo You can run setup-mysql.bat to configure MySQL
    echo.
    pause
)

echo Starting Backend (Spring Boot)...
start "Backend" cmd /k "cd backend && mvn spring-boot:run"

echo Waiting for backend to initialize...
timeout /t 15 /nobreak > nul

echo Starting Frontend (Angular)...
start "Frontend" cmd /k "cd frontend && ng serve"

echo.
echo Both applications are starting...
echo.
echo === Access URLs ===
echo Backend API: http://localhost:8080/api
echo Frontend: http://localhost:4200
echo Swagger UI: http://localhost:8080/api/swagger-ui.html
echo.
echo === Default Login Credentials ===
echo Admin: admin / admin123
echo Nurse: nurse1 / nurse123
echo Patient: patient1 / patient123
echo.
pause
