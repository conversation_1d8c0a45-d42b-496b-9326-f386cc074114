#!/bin/bash

echo "🚀 Déploiement en mode PRODUCTION"
echo "=================================="

# Vérifier si le fichier .env existe
if [ ! -f .env ]; then
    echo "❌ Fichier .env manquant pour la production"
    echo "Créez un fichier .env avec les variables de production"
    exit 1
fi

# Arrêter les conteneurs existants
echo "🛑 Arrêt des conteneurs existants..."
docker-compose -f docker-compose.prod.yml down

# Démarrer en mode production
echo "🔨 Démarrage en mode production..."
docker-compose -f docker-compose.prod.yml up -d --build

echo ""
echo "✅ Déploiement production terminé!"
echo ""
echo "🌐 Accès à l'application:"
echo "   Frontend: http://[VOTRE-IP]:3000"
echo "   Backend API: http://[VOTRE-IP]:8080/api"
echo "   Swagger UI: http://[VOTRE-IP]:8080/api/swagger-ui.html"
echo ""
echo "📋 Configuration:"
echo "   Profil Spring: prod"
echo "   Base de données: PostgreSQL Docker"
echo "   Logs: INFO"
echo "   CORS: Détection automatique de l'IP"
echo ""
echo "💡 L'application détecte automatiquement votre IP et configure l'API en conséquence"
echo ""
