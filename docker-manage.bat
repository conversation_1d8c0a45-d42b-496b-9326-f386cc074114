@echo off
echo 🐳 Medical Home Sampling - Docker Management
echo.

:menu
echo ========================================
echo Docker Management Menu
echo ========================================
echo 1. Build Docker images
echo 2. Start development environment
echo 3. Start production environment
echo 4. Stop all services
echo 5. View logs
echo 6. View service status
echo 7. Clean up (remove containers and images)
echo 8. Database backup
echo 9. Database restore
echo 0. Exit
echo.
set /p choice="Select an option (0-9): "

if "%choice%"=="1" goto build
if "%choice%"=="2" goto start_dev
if "%choice%"=="3" goto start_prod
if "%choice%"=="4" goto stop
if "%choice%"=="5" goto logs
if "%choice%"=="6" goto status
if "%choice%"=="7" goto cleanup
if "%choice%"=="8" goto backup
if "%choice%"=="9" goto restore
if "%choice%"=="0" goto exit
echo Invalid choice. Please try again.
goto menu

:build
echo 🔨 Building Docker images...
call build-docker.bat
goto menu

:start_dev
echo 🚀 Starting development environment...
call deploy-docker.bat
goto menu

:start_prod
echo 🚀 Starting production environment...
call deploy-production.bat
goto menu

:stop
echo 🛑 Stopping all services...
docker-compose down
docker-compose -f docker-compose.prod.yml down
echo Services stopped.
pause
goto menu

:logs
echo 📋 Select log type:
echo 1. Development logs
echo 2. Production logs
echo 3. Specific service logs
set /p log_choice="Choice (1-3): "

if "%log_choice%"=="1" (
    docker-compose logs -f
) else if "%log_choice%"=="2" (
    docker-compose -f docker-compose.prod.yml logs -f
) else if "%log_choice%"=="3" (
    echo Available services: frontend, backend, postgres
    set /p service="Enter service name: "
    docker-compose logs -f %service%
)
goto menu

:status
echo 📊 Service Status:
echo.
echo Development services:
docker-compose ps
echo.
echo Production services:
docker-compose -f docker-compose.prod.yml ps
echo.
echo Docker resources:
docker stats --no-stream
pause
goto menu

:cleanup
echo ⚠️  This will remove all containers, images, and volumes!
set /p confirm="Are you sure? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo 🧹 Cleaning up...
docker-compose down -v
docker-compose -f docker-compose.prod.yml down -v
docker system prune -a -f
echo Cleanup complete.
pause
goto menu

:backup
echo 💾 Creating database backup...
set backup_file=backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.sql
docker exec medical-postgres pg_dump -U postgres medical_home_sampling > %backup_file%
echo Backup created: %backup_file%
pause
goto menu

:restore
echo 📥 Database restore
set /p backup_file="Enter backup file name: "
if not exist "%backup_file%" (
    echo File not found: %backup_file%
    pause
    goto menu
)
docker exec -i medical-postgres psql -U postgres medical_home_sampling < %backup_file%
echo Database restored from: %backup_file%
pause
goto menu

:exit
echo Goodbye!
exit /b 0
