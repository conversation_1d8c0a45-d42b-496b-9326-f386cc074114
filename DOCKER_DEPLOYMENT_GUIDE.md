# 🐳 Medical Home Sampling - Docker Deployment Guide

This guide provides comprehensive instructions for deploying the Medical Home Sampling application using Docker.

## 📋 Prerequisites

### Required Software
- **Docker Desktop** (Windows/Mac) or **Docker Engine** (Linux)
- **Docker Compose** (included with Docker Desktop)
- **Git** (for cloning the repository)

### System Requirements
- **RAM**: Minimum 4GB, Recommended 8GB
- **Storage**: At least 5GB free space
- **CPU**: 2+ cores recommended

## 🚀 Quick Start

### 1. Clone and Navigate
```bash
git clone <your-repository-url>
cd medicalProject
```

### 2. Simple Deployment
```bash
# Run the deployment script
deploy-docker.bat
```

This will:
- Create `.env` file from template
- Build all Docker images
- Start all services
- Display access URLs

## 📁 Docker Files Overview

### Core Files
- `docker-compose.yml` - Development environment
- `docker-compose.prod.yml` - Production environment
- `.env.example` - Environment variables template
- `backend/Dockerfile` - Spring Boot backend image
- `frontend/Dockerfile` - Angular frontend with Nginx
- `frontend/nginx.conf` - Nginx configuration

### Management Scripts
- `build-docker.bat` - Build Docker images only
- `deploy-docker.bat` - Deploy development environment
- `deploy-production.bat` - Deploy production environment
- `docker-manage.bat` - Interactive management menu

## ⚙️ Configuration

### Environment Variables (.env)

Copy `.env.example` to `.env` and configure:

```bash
# Database Configuration
DB_NAME=medical_home_sampling
DB_USERNAME=postgres
DB_PASSWORD=your_secure_password_here

# Security Configuration
JWT_SECRET=your_very_secure_jwt_secret_key_here_at_least_32_characters

# Application Configuration
DDL_AUTO=update          # Development: update, Production: validate
INIT_MODE=always         # Development: always, Production: never
LOG_LEVEL=INFO
```

### Development vs Production

| Setting | Development | Production |
|---------|-------------|------------|
| DDL_AUTO | update | validate |
| INIT_MODE | always | never |
| SHOW_SQL | true | false |
| LOG_LEVEL | DEBUG | INFO |

## 🔧 Deployment Options

### Option 1: Development Environment
```bash
# Quick start for development
deploy-docker.bat

# Or manually
docker-compose up -d --build
```

### Option 2: Production Environment
```bash
# Production deployment
deploy-production.bat

# Or manually
docker-compose -f docker-compose.prod.yml up -d --build
```

### Option 3: Interactive Management
```bash
# Use the management menu
docker-manage.bat
```

## 🌐 Access Points

After deployment, access the application at:

- **Frontend**: http://localhost
- **Backend API**: http://localhost:8080/api
- **Swagger Documentation**: http://localhost:8080/api/swagger-ui.html
- **Database**: localhost:5432 (PostgreSQL)

## 📊 Service Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   PostgreSQL    │
│   (Angular)     │    │  (Spring Boot)  │    │   Database      │
│   Port: 80      │────│   Port: 8080    │────│   Port: 5432    │
│   Nginx         │    │   Java 17       │    │   Version 15    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔍 Monitoring and Logs

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres

# Production logs
docker-compose -f docker-compose.prod.yml logs -f
```

### Health Checks
```bash
# Check service status
docker-compose ps

# Check health endpoints
curl http://localhost/health           # Frontend health
curl http://localhost:8080/api/actuator/health  # Backend health
```

### Resource Monitoring
```bash
# Monitor resource usage
docker stats

# View container details
docker inspect medical-backend
```

## 💾 Database Management

### Backup Database
```bash
# Create backup
docker exec medical-postgres pg_dump -U postgres medical_home_sampling > backup.sql

# Or use the management script
docker-manage.bat  # Option 8
```

### Restore Database
```bash
# Restore from backup
docker exec -i medical-postgres psql -U postgres medical_home_sampling < backup.sql

# Or use the management script
docker-manage.bat  # Option 9
```

### Access Database
```bash
# Connect to PostgreSQL
docker exec -it medical-postgres psql -U postgres -d medical_home_sampling
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the port
netstat -ano | findstr :80
netstat -ano | findstr :8080
netstat -ano | findstr :5432

# Stop conflicting services or change ports in docker-compose.yml
```

#### 2. Database Connection Issues
```bash
# Check PostgreSQL logs
docker-compose logs postgres

# Verify database is ready
docker exec medical-postgres pg_isready -U postgres
```

#### 3. Frontend Not Loading
```bash
# Check nginx logs
docker-compose logs frontend

# Verify backend is accessible
curl http://localhost:8080/api/actuator/health
```

#### 4. Build Failures
```bash
# Clean build
docker system prune -a
docker-compose build --no-cache
```

### Reset Everything
```bash
# Complete reset (WARNING: Deletes all data)
docker-compose down -v
docker system prune -a -f
docker volume prune -f
```

## 🔒 Security Considerations

### Production Security Checklist
- [ ] Change default passwords in `.env`
- [ ] Use strong JWT secret (32+ characters)
- [ ] Set `DDL_AUTO=validate` (not update)
- [ ] Set `INIT_MODE=never` (not always)
- [ ] Configure proper CORS origins
- [ ] Use HTTPS in production
- [ ] Regular security updates
- [ ] Monitor logs for suspicious activity

### Network Security
- Services communicate through internal Docker network
- Only necessary ports are exposed
- Database is not directly accessible from outside

## 📈 Scaling and Performance

### Resource Limits
The production compose file includes resource limits:
- **Frontend**: 256MB RAM limit
- **Backend**: 1GB RAM limit  
- **Database**: 512MB RAM limit

### Scaling Services
```bash
# Scale backend instances
docker-compose up -d --scale backend=3

# Note: You'll need a load balancer for multiple backend instances
```

## 🆘 Support

### Getting Help
1. Check logs: `docker-compose logs -f`
2. Verify service health: `docker-compose ps`
3. Check resource usage: `docker stats`
4. Review this guide
5. Check Docker documentation

### Useful Commands
```bash
# View all containers
docker ps -a

# View all images
docker images

# View all volumes
docker volume ls

# View all networks
docker network ls

# Clean up unused resources
docker system prune
```

## 🎉 Success!

Your Medical Home Sampling application is now running in Docker containers with:
- ✅ Scalable architecture
- ✅ Health monitoring
- ✅ Persistent data storage
- ✅ Production-ready configuration
- ✅ Easy management scripts
