@echo off
echo 🚀 Deploying Medical Home Sampling Application with Docker
echo.

REM Check if Dock<PERSON> is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

REM Check if .env file exists
if not exist .env (
    echo ⚠️  .env file not found. Creating from template...
    copy .env.example .env
    echo.
    echo 📝 Please edit .env file with your configuration before continuing.
    echo Press any key to open .env file...
    pause >nul
    notepad .env
    echo.
    echo Press any key when you've finished editing .env...
    pause >nul
)

echo 🔨 Building and starting services...
echo.

REM Stop existing containers
echo 🛑 Stopping existing containers...
docker-compose down

REM Build and start services
echo 🚀 Starting services...
docker-compose up -d --build

if %errorlevel% neq 0 (
    echo ❌ Failed to start services
    pause
    exit /b 1
)

echo.
echo ⏳ Waiting for services to be ready...
timeout /t 30 /nobreak >nul

echo.
echo 📊 Service status:
docker-compose ps

echo.
echo 🎉 Application deployed successfully!
echo.
echo 🌐 Access the application:
echo   Frontend: http://localhost
echo   Backend API: http://localhost:8080/api
echo   Swagger UI: http://localhost:8080/api/swagger-ui.html
echo.
echo 📋 Useful commands:
echo   View logs: docker-compose logs -f
echo   Stop services: docker-compose down
echo   Restart services: docker-compose restart
echo.
pause
