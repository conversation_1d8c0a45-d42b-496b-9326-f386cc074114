# Medical Home Sampling Application - PRODUCTION Environment Variables

# Database Configuration
DB_NAME=medical_home_sampling
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_POOL_SIZE=20

# Application Configuration
DDL_AUTO=validate
SHOW_SQL=false
LOG_LEVEL=INFO
SECURITY_LOG_LEVEL=WARN
INIT_MODE=never

# Security Configuration
JWT_SECRET=mySecretKey123456789012345678901234567890
JWT_EXPIRATION=86400000

# CORS Configuration - Patterns génériques pour auto-détection
# Accepte automatiquement toutes les adresses IP sans les spécifier
CORS_ORIGINS=http://localhost,http://localhost:*,http://127.0.0.1:*,http://192.168.*.*:*,http://10.*.*.*:*,http://172.*.*.*:*,https://prev.intellitech.pro

# Frontend Configuration
FRONTEND_PORT=80
