# Medical Home Sampling Application - Environment Variables
# Copiez ce fichier vers .env et ajustez les valeurs selon votre environnement

# Database Configuration
DB_NAME=medical_home_sampling
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_POOL_SIZE=20

# Application Configuration
SPRING_PROFILES_ACTIVE=dev
DDL_AUTO=update
SHOW_SQL=true
LOG_LEVEL=DEBUG
SECURITY_LOG_LEVEL=WARN
INIT_MODE=always

# Security Configuration (CHANGEZ CETTE CLÉ EN PRODUCTION !)
JWT_SECRET=mySecretKey123456789012345678901234567890
JWT_EXPIRATION=86400000

# CORS Configuration
# Ajustez selon votre environnement de déploiement
# Pour serveur local : http://localhost:3000
# Pour serveur distant : http://***************:3000 (remplacez par votre IP)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://192.168.*.*:*,http://10.*.*.*:*,http://172.*.*.*:*,http://***************:*,https://prev.intellitech.pro

# Frontend Configuration
# Port pour accéder à l'application
FRONTEND_PORT=3000

# Email Configuration (Optionnel - pour les notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
