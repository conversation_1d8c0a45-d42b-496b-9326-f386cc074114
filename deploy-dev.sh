#!/bin/bash

echo "🚀 Déploiement en mode DÉVELOPPEMENT"
echo "===================================="

# Arrêter les conteneurs existants
echo "🛑 Arrêt des conteneurs existants..."
docker-compose down

# D<PERSON><PERSON>rer en mode développement
echo "🔨 Démarrage en mode développement..."
docker-compose up -d --build

echo ""
echo "✅ Déploiement développement terminé!"
echo ""
echo "🌐 Accès à l'application:"
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:8080/api"
echo "   Swagger UI: http://localhost:8080/api/swagger-ui.html"
echo ""
echo "📋 Configuration:"
echo "   Profil Spring: dev"
echo "   Base de données: PostgreSQL local"
echo "   Logs: DEBUG"
echo "   CORS: localhost uniquement"
echo ""
