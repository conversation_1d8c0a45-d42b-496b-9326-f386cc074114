@echo off
echo 🐳 Building Medical Home Sampling Application with Docker
echo.

REM Check if <PERSON><PERSON> is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

echo 🔨 Building Docker images...
echo.

REM Build backend image
echo 📦 Building backend image...
docker build -t medical-backend:latest ./backend
if %errorlevel% neq 0 (
    echo ❌ Failed to build backend image
    pause
    exit /b 1
)
echo ✅ Backend image built successfully
echo.

REM Build frontend image
echo 🌐 Building frontend image...
docker build -t medical-frontend:latest ./frontend
if %errorlevel% neq 0 (
    echo ❌ Failed to build frontend image
    pause
    exit /b 1
)
echo ✅ Frontend image built successfully
echo.

echo 🎉 All Docker images built successfully!
echo.
echo Available images:
docker images | findstr medical
echo.
echo To start the application, run: docker-compose up -d
echo.
pause
