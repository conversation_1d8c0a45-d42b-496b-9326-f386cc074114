# 🚀 Guide des Profils de Déploiement

Ce projet utilise deux profils distincts pour séparer les environnements de développement et de production.

## 📋 Profils Disponibles

### 🔧 Profil DEV (Développement)
- **Usage :** Développement local sur n'importe quelle machine
- **Configuration :** Localhost uniquement
- **Base de données :** PostgreSQL local ou Docker
- **Logs :** DEBUG (verbeux)
- **CORS :** Localhost et 127.0.0.1 uniquement

### 🏭 Profil PROD (Production)
- **Usage :** Serveur de production avec détection automatique d'IP
- **Configuration :** Détection dynamique de l'IP/domaine
- **Base de données :** PostgreSQL Docker optimisé
- **Logs :** INFO (optimisé)
- **CORS :** Patterns génériques pour toutes IPs locales

## 🚀 Commandes de Déploiement

### Développement Local
```bash
# Déploiement développement (n'importe quelle machine)
./deploy-dev.sh

# Ou manuellement
docker-compose up -d --build
```

### Production/Serveur
```bash
# Déploiement production (détection automatique d'IP)
./deploy-prod.sh

# Ou manuellement
docker-compose -f docker-compose.prod.yml up -d --build
```

## 📁 Structure des Fichiers de Configuration

### Backend (Spring Boot)
- `application.properties` - Configuration de base
- `application-dev.properties` - Spécifique développement
- `application-prod.properties` - Spécifique production
- `application-docker.properties` - Configuration Docker

### Frontend (Angular)
- `environment.ts` - Environnement développement
- `environment.prod.ts` - Environnement production

### Docker
- `docker-compose.yml` - Configuration développement
- `docker-compose.prod.yml` - Configuration production
- `.env` - Variables d'environnement production

## 🔄 Avantages de cette Approche

✅ **Plus besoin de changer les IPs** à chaque déploiement
✅ **Configuration automatique** selon l'environnement
✅ **Séparation claire** dev/prod
✅ **Portabilité maximale** pour le développement
✅ **Optimisation** spécifique à chaque environnement

## 🌐 URLs d'Accès

### Développement
- Frontend: http://localhost:3000
- Backend: http://localhost:8080/api
- Swagger: http://localhost:8080/api/swagger-ui.html

### Production
- Frontend: http://[VOTRE-IP]:3000
- Backend: http://[VOTRE-IP]:8080/api
- Swagger: http://[VOTRE-IP]:8080/api/swagger-ui.html
- **Note :** L'IP est détectée automatiquement selon votre machine

## 🔧 Configuration Automatique

✅ **Aucune modification d'IP nécessaire !**
- La configuration s'adapte automatiquement à votre environnement
- Détection dynamique de l'IP en production
- Localhost par défaut en développement
- Fonctionne sur n'importe quel réseau sans modification
