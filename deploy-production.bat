@echo off
echo 🚀 Deploying Medical Home Sampling Application - PRODUCTION MODE
echo.

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

REM Check if .env file exists
if not exist .env (
    echo ❌ .env file is required for production deployment
    echo Creating from template...
    copy .env.example .env
    echo.
    echo 📝 Please edit .env file with PRODUCTION configuration:
    echo   - Set secure DB_PASSWORD
    echo   - Set secure JWT_SECRET (at least 32 characters)
    echo   - Set DDL_AUTO=validate (not update!)
    echo   - Set INIT_MODE=never (not always!)
    echo   - Set appropriate CORS_ORIGINS
    echo.
    echo Press any key to open .env file...
    pause >nul
    notepad .env
    echo.
    echo ⚠️  IMPORTANT: Verify your production settings before continuing!
    echo Press any key when ready...
    pause >nul
)

echo 🔒 Production deployment checklist:
echo   ✓ Secure database password
echo   ✓ Secure JWT secret
echo   ✓ DDL_AUTO=validate
echo   ✓ INIT_MODE=never
echo   ✓ Appropriate CORS origins
echo.
set /p confirm="Continue with production deployment? (y/N): "
if /i not "%confirm%"=="y" (
    echo Deployment cancelled.
    pause
    exit /b 0
)

echo.
echo 🔨 Building and starting production services...
echo.

REM Stop existing containers
echo 🛑 Stopping existing containers...
docker-compose -f docker-compose.prod.yml down

REM Build and start production services
echo 🚀 Starting production services...
docker-compose -f docker-compose.prod.yml up -d --build

if %errorlevel% neq 0 (
    echo ❌ Failed to start production services
    pause
    exit /b 1
)

echo.
echo ⏳ Waiting for services to be ready...
timeout /t 60 /nobreak >nul

echo.
echo 📊 Production service status:
docker-compose -f docker-compose.prod.yml ps

echo.
echo 🎉 Production application deployed successfully!
echo.
echo 🌐 Access the application:
echo   Frontend: http://localhost
echo   Backend API: http://localhost:8080/api
echo   Swagger UI: http://localhost:8080/api/swagger-ui.html
echo.
echo 📋 Production management commands:
echo   View logs: docker-compose -f docker-compose.prod.yml logs -f
echo   Stop services: docker-compose -f docker-compose.prod.yml down
echo   Restart services: docker-compose -f docker-compose.prod.yml restart
echo.
echo 🔍 Monitor resources: docker stats
echo.
pause
