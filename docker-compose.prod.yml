version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: medical-postgres-prod
    environment:
      POSTGRES_DB: ${DB_NAME:-medical_home_sampling}
      POSTGRES_USER: ${DB_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./init-sql:/docker-entrypoint-initdb.d
    networks:
      - medical-network-prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME:-postgres} -d ${DB_NAME:-medical_home_sampling}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Spring Boot Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medical-backend-prod
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_NAME: ${DB_NAME:-medical_home_sampling}
      DB_USERNAME: ${DB_USERNAME:-postgres}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_POOL_SIZE: ${DB_POOL_SIZE:-20}
      DDL_AUTO: ${DDL_AUTO:-validate}
      SHOW_SQL: ${SHOW_SQL:-false}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      SECURITY_LOG_LEVEL: ${SECURITY_LOG_LEVEL:-WARN}
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRATION: ${JWT_EXPIRATION:-86400000}
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000,http://localhost:8080}
      INIT_MODE: ${INIT_MODE:-never}
    networks:
      - medical-network-prod
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s
    restart: unless-stopped
    volumes:
      - backend_logs_prod:/app/logs
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Angular Frontend with Nginx
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: medical-frontend-prod
    ports:
      - "0.0.0.0:${FRONTEND_PORT:-3000}:80"
    networks:
      - medical-network-prod
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "test", "-f", "/usr/share/nginx/html/index.html"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

networks:
  medical-network-prod:
    driver: bridge
    name: medical-network-prod

volumes:
  postgres_data_prod:
    name: medical-postgres-data-prod
  backend_logs_prod:
    name: medical-backend-logs-prod
