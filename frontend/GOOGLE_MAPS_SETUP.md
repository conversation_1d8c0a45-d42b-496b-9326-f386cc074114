# 🗺️ Configuration Google Maps

## 📋 **Étapes pour Obtenir une Clé API Google Maps**

### **1. <PERSON><PERSON>er un Projet Google Cloud**
1. Allez sur [Google Cloud Console](https://console.cloud.google.com/)
2. Cliquez sur "Sélectionner un projet" → "Nouveau projet"
3. Donnez un nom à votre projet (ex: "Medical-App-Maps")
4. <PERSON><PERSON>z sur "Créer"

### **2. Activer les APIs Nécessaires**
1. Dans le menu de gauche, allez dans "APIs et services" → "Bibliothèque"
2. Recherchez et activez ces APIs :
   - **Maps JavaScript API**
   - **Geocoding API** (optionnel, pour la recherche d'adresses)
   - **Places API** (optionnel, pour l'autocomplétion)

### **3. Créer une Clé API**
1. Allez dans "APIs et services" → "Identifiants"
2. Cliquez sur "Créer des identifiants" → "Clé API"
3. Copiez votre clé API

### **4. Configurer les Restrictions (Recommandé)**
1. C<PERSON>z sur votre clé API nouvellement créée
2. Dans "Restrictions d'application" :
   - Sélectionnez "Référents HTTP (sites web)"
   - Ajoutez : `http://localhost:4200/*` et `https://localhost:4200/*`
3. Dans "Restrictions d'API" :
   - Sélectionnez "Restreindre la clé"
   - Choisissez "Maps JavaScript API"

## 🔧 **Configuration dans l'Application**

### **1. Remplacer la Clé API**
Remplacez `YOUR_GOOGLE_MAPS_API_KEY` dans ces fichiers :

**frontend/src/index.html :**
```html
<script async defer src="https://maps.googleapis.com/maps/api/js?key=VOTRE_VRAIE_CLE_API&libraries=places"></script>
```

**frontend/src/environments/environment.ts :**
```typescript
export const environment = {
  // ...
  googleMapsApiKey: 'VOTRE_VRAIE_CLE_API'
};
```

### **2. Exemple de Clé API**
```
AIzaSyBOti4mM-6x9WDnZIjIeyEU21OpBXqWBgw
```
*(Ceci est un exemple, utilisez votre vraie clé)*

## 🎯 **Fonctionnalités Disponibles**

### **✅ Géolocalisation Améliorée**
- Détection automatique de la position avec Google Maps
- Précision améliorée par rapport à Leaflet
- Messages de précision en temps réel

### **✅ Interface Google Maps**
- Carte interactive Google Maps
- Marqueurs cliquables
- Zoom et navigation fluides

### **✅ Sélection de Position**
- Clic sur la carte pour sélectionner
- Bouton "Ma position" avec géolocalisation
- Recherche d'adresses (si Places API activée)

## 🚀 **Test de l'Intégration**

1. **Remplacez la clé API** dans les fichiers mentionnés
2. **Redémarrez le serveur** : `ng serve`
3. **Allez sur** : `http://localhost:4200/dashboard/new-appointment`
4. **Testez** :
   - Cliquez sur "Ma position"
   - Cliquez sur la carte
   - Vérifiez la précision affichée

## 💰 **Coûts Google Maps**

- **Gratuit** : 28 000 chargements de carte par mois
- **Géolocalisation** : Incluse dans l'API JavaScript
- **Dépassement** : ~$7 pour 1000 chargements supplémentaires

## 🔒 **Sécurité**

- **Restrictions de domaine** : Limitez l'usage à vos domaines
- **Restrictions d'API** : Limitez aux APIs nécessaires
- **Monitoring** : Surveillez l'usage dans Google Cloud Console

## 🆘 **Dépannage**

### **Erreur : "This page can't load Google Maps correctly"**
- Vérifiez que la clé API est correcte
- Vérifiez que l'API Maps JavaScript est activée
- Vérifiez les restrictions de domaine

### **Géolocalisation ne fonctionne pas**
- Vérifiez les permissions du navigateur
- Testez en HTTPS (requis pour la géolocalisation précise)
- Vérifiez la console pour les erreurs

### **Carte ne s'affiche pas**
- Vérifiez la console du navigateur
- Vérifiez que le script Google Maps se charge
- Vérifiez les restrictions d'API
