// Configuration pour développement local
// Copiez ce fichier et modifiez l'IP selon votre réseau local

export const environment = {
  production: false,
  apiUrl: 'http://localhost:8080/api', // Modifiez cette URL selon votre configuration
  appName: 'Suivi des prélèvements médicaux',
  version: '1.0.0',
  googleMapsApiKey: 'AIzaSyBz0ujCIaqJN_KUT48cJEY4PYYPDlyYJyE'
};

// Exemples de configuration selon votre réseau :
// Pour accès local uniquement : 'http://localhost:8080/api'
// Pour accès réseau local : 'http://192.168.1.XXX:8080/api' (remplacez XXX par votre IP)
// Pour Docker : 'http://localhost:8080/api'
