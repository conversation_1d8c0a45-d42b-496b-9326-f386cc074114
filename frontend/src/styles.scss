/* You can add global styles to this file, and also import other style files */
@import '@angular/material/prebuilt-themes/indigo-pink.css';
@import 'leaflet/dist/leaflet.css';

/* Global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Roboto', sans-serif;
  overflow-x: hidden; /* Éviter la barre de défilement horizontale */
}

body {
  margin: 0;
  background-color: #fafafa;
}

/* Éviter les barres de défilement multiples */
app-root {
  display: block;
  min-height: 100vh;
}

/* Conteneurs principaux */
.main-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Éviter les hauteurs fixes qui causent des problèmes de défilement */
.dashboard-container,
.admin-dashboard-container,
.nurse-dashboard,
.page-content {
  flex: 1;
  overflow-y: auto;
  /* Suppression des min-height fixes */
}

/* Material Design overrides */
.mat-mdc-card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.mat-mdc-button.mat-primary {
  background-color: #3f51b5;
  color: white;
}

.mat-mdc-raised-button.mat-primary {
  background-color: #3f51b5;
  color: white;
}

/* Fix pour les form fields Angular Material */
.mat-mdc-form-field {
  width: 100% !important;

  .mat-mdc-form-field-flex {
    align-items: center !important;
    min-height: 56px !important;
    padding: 0 16px !important;
  }

  .mat-mdc-form-field-infix {
    padding-top: 16px !important;
    padding-bottom: 16px !important;
    min-height: auto !important;
    border-top: none !important;
  }

  .mat-mdc-floating-label {
    top: 50% !important;
    transform: translateY(-50%) !important;
    pointer-events: none !important;

    &.mdc-floating-label--float-above {
      transform: translateY(-106%) scale(0.75) !important;
    }
  }

  .mdc-notched-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: rgba(0, 0, 0, 0.38) !important;
      border-width: 1px !important;
    }
  }

  &.mat-focused .mdc-notched-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: #2563eb !important;
      border-width: 2px !important;
    }
  }

  &:hover:not(.mat-focused) .mdc-notched-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: rgba(0, 0, 0, 0.6) !important;
    }
  }

  .mat-mdc-form-field-icon-prefix {
    margin-right: 12px !important;
    color: #64748b !important;
  }

  &.mat-focused .mat-mdc-form-field-icon-prefix {
    color: #2563eb !important;
  }

  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px !important;
    min-height: 20px !important;
    padding: 0 16px !important;
  }

  .mat-mdc-form-field-hint-wrapper,
  .mat-mdc-form-field-error-wrapper {
    padding: 0 !important;
  }

  .mat-mdc-form-field-hint {
    color: #6b7280 !important;
    font-size: 12px !important;
  }

  .mat-mdc-form-field-error {
    color: #dc2626 !important;
    font-size: 12px !important;
  }
}

/* Utility classes */
.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.p-16 {
  padding: 16px;
}

/* Snackbar styles */
.success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

.error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

.warning-snackbar {
  background-color: #ff9800 !important;
  color: white !important;
}
