import { Pipe, PipeTransform } from '@angular/core';
import { DateUtilsService } from '../services/date-utils.service';

@Pipe({
  name: 'safeDate',
  standalone: true
})
export class SafeDatePipe implements PipeTransform {

  constructor(private dateUtils: DateUtilsService) {}

  transform(value: string | Date | null | undefined, format: 'full' | 'date' | 'time' = 'full'): string {
    if (!value) {
      return '';
    }

    switch (format) {
      case 'full':
        return this.dateUtils.formatDateTime(value);
      case 'date':
        return this.dateUtils.formatDate(value);
      case 'time':
        return this.dateUtils.formatTime(value);
      default:
        return this.dateUtils.formatDateTime(value);
    }
  }
}
