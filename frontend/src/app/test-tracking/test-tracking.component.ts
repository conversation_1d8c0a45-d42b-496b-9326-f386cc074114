import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-test-tracking',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div style="max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
      <h2>🧪 Test Géolocalisation Temps Réel</h2>
      
      <div style="margin: 20px 0;">
        <h3>📍 Position Actuelle</h3>
        <div *ngIf="currentPosition" style="background: #f0f8ff; padding: 15px; border-radius: 4px;">
          <p><strong>Latitude:</strong> {{ currentPosition.latitude.toFixed(6) }}</p>
          <p><strong>Longitude:</strong> {{ currentPosition.longitude.toFixed(6) }}</p>
          <p><strong>Précision:</strong> ±{{ currentPosition.accuracy.toFixed(0) }}m</p>
          <p><strong>Dernière MAJ:</strong> {{ lastUpdate | date:'HH:mm:ss' }}</p>
        </div>
        <div *ngIf="!currentPosition" style="background: #fff3cd; padding: 15px; border-radius: 4px;">
          <p>Aucune position détectée</p>
        </div>
      </div>

      <div style="margin: 20px 0;">
        <h3>⚙️ Configuration</h3>
        <div style="display: flex; flex-direction: column; gap: 10px;">
          <label>
            ID Infirmier:
            <input type="number" [(ngModel)]="nurseId" style="margin-left: 10px; padding: 5px;">
          </label>
          <label>
            ID Rendez-vous:
            <input type="number" [(ngModel)]="appointmentId" style="margin-left: 10px; padding: 5px;">
          </label>
          <label>
            Status:
            <select [(ngModel)]="status" style="margin-left: 10px; padding: 5px;">
              <option value="ON_WAY">En route</option>
              <option value="ARRIVED">Arrivé</option>
              <option value="DELAYED">En retard</option>
            </select>
          </label>
        </div>
      </div>

      <div style="margin: 20px 0;">
        <h3>🎮 Actions</h3>
        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
          <button (click)="getCurrentLocation()" 
                  style="padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
            📍 Obtenir Position GPS
          </button>
          
          <button (click)="startSimulation()" [disabled]="isSimulating"
                  style="padding: 10px 15px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
            🎮 {{ isSimulating ? 'Simulation Active...' : 'Démarrer Simulation' }}
          </button>
          
          <button (click)="stopSimulation()" [disabled]="!isSimulating"
                  style="padding: 10px 15px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
            ⏹️ Arrêter Simulation
          </button>
          
          <button (click)="sendPosition()" [disabled]="!currentPosition"
                  style="padding: 10px 15px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer;">
            📤 Envoyer Position
          </button>
        </div>
      </div>

      <div *ngIf="isSimulating" style="margin: 20px 0; background: #d4edda; padding: 15px; border-radius: 4px;">
        <h3>🚗 Simulation Active</h3>
        <p>Position mise à jour toutes les 3 secondes</p>
        <p>Direction: {{ simulationDirection }}</p>
      </div>

      <div style="margin: 20px 0;">
        <h3>📋 Instructions</h3>
        <ol>
          <li>Cliquez "Obtenir Position GPS" pour utiliser votre vraie position</li>
          <li>Ou cliquez "Démarrer Simulation" pour simuler un mouvement</li>
          <li>Configurez l'ID infirmier et rendez-vous</li>
          <li>Cliquez "Envoyer Position" pour envoyer au serveur</li>
          <li>Allez sur l'admin dashboard pour voir la position sur la carte</li>
        </ol>
      </div>

      <div *ngIf="messages.length > 0" style="margin: 20px 0;">
        <h3>📝 Messages</h3>
        <div style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px;">
          <div *ngFor="let msg of messages" [style.color]="msg.type === 'error' ? 'red' : 'green'">
            {{ msg.time | date:'HH:mm:ss' }} - {{ msg.text }}
          </div>
        </div>
      </div>
    </div>
  `
})
export class TestTrackingComponent {
  nurseId = 2;
  appointmentId = 1;
  status = 'ON_WAY';
  
  currentPosition: any = null;
  lastUpdate: Date | null = null;
  isSimulating = false;
  simulationDirection = 'Nord';
  
  messages: Array<{text: string, type: string, time: Date}> = [];
  
  // Position de simulation (Tunis)
  private simLat = 36.8065;
  private simLng = 10.1815;
  private simulationInterval: any;

  addMessage(text: string, type: 'success' | 'error' = 'success') {
    this.messages.unshift({text, type, time: new Date()});
    if (this.messages.length > 10) this.messages.pop();
  }

  async getCurrentLocation() {
    try {
      this.addMessage('🔍 Recherche de votre position GPS...');
      
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 0
        });
      });
      
      this.currentPosition = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        speed: position.coords.speed || 0,
        heading: position.coords.heading || 0
      };
      
      this.lastUpdate = new Date();
      this.addMessage(`✅ Position GPS obtenue: ${this.currentPosition.latitude.toFixed(4)}, ${this.currentPosition.longitude.toFixed(4)}`);
      
    } catch (error: any) {
      this.addMessage(`❌ Erreur GPS: ${error.message}`, 'error');
    }
  }

  startSimulation() {
    this.isSimulating = true;
    this.simLat = 36.8065; // Reset à Tunis
    this.simLng = 10.1815;
    
    this.addMessage('🎮 Simulation démarrée - Mouvement automatique');
    
    this.simulationInterval = setInterval(() => {
      // Mouvement aléatoire
      const directions = ['Nord', 'Sud', 'Est', 'Ouest'];
      this.simulationDirection = directions[Math.floor(Math.random() * directions.length)];
      
      // Déplacer la position
      switch (this.simulationDirection) {
        case 'Nord': this.simLat += 0.001; break;
        case 'Sud': this.simLat -= 0.001; break;
        case 'Est': this.simLng += 0.001; break;
        case 'Ouest': this.simLng -= 0.001; break;
      }
      
      this.currentPosition = {
        latitude: this.simLat,
        longitude: this.simLng,
        accuracy: 10,
        speed: 25,
        heading: Math.random() * 360
      };
      
      this.lastUpdate = new Date();
      this.sendPosition(); // Envoyer automatiquement
      
    }, 3000); // Toutes les 3 secondes
  }

  stopSimulation() {
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval);
      this.simulationInterval = null;
    }
    this.isSimulating = false;
    this.addMessage('⏹️ Simulation arrêtée');
  }

  async sendPosition() {
    if (!this.currentPosition) {
      this.addMessage('❌ Aucune position à envoyer', 'error');
      return;
    }
    
    try {
      const payload = {
        nurseId: this.nurseId,
        appointmentId: this.appointmentId,
        latitude: this.currentPosition.latitude,
        longitude: this.currentPosition.longitude,
        accuracy: this.currentPosition.accuracy,
        speed: this.currentPosition.speed,
        heading: this.currentPosition.heading,
        status: this.status,
        timestamp: new Date().toISOString()
      };

      const response = await fetch('/api/tracking/update-position', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        const result = await response.json();
        this.addMessage(`✅ Position envoyée: ${this.currentPosition.latitude.toFixed(4)}, ${this.currentPosition.longitude.toFixed(4)}`);
      } else {
        this.addMessage(`❌ Erreur serveur: ${response.status}`, 'error');
      }
    } catch (error: any) {
      this.addMessage(`❌ Erreur réseau: ${error.message}`, 'error');
    }
  }

  ngOnDestroy() {
    this.stopSimulation();
  }
}
