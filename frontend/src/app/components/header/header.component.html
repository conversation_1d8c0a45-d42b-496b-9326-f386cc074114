<!-- Header principal unique -->
<mat-toolbar class="main-header" color="primary">
  <!-- Logo et titre -->
  <div class="header-brand" routerLink="/home">
    <div class="brand-icon">🩺</div>
    <div class="brand-text">
      <div class="brand-title">MediSample</div>
      <div class="brand-subtitle">Prélèvements à domicile</div>
    </div>
  </div>

  <!-- Spacer -->
  <span class="spacer"></span>

  <!-- Actions utilisateur -->
  <div class="header-actions" *ngIf="isAuthenticated">
    <!-- Notifications -->
    <button mat-icon-button [matMenuTriggerFor]="notificationMenu" (menuOpened)="onNotificationMenuOpened()" class="custom-icon-btn">
      <span class="custom-icon" [matBadge]="unreadCount$ | async"
            [matBadgeHidden]="(unreadCount$ | async) === 0"
            matBadgeColor="warn">🔔</span>
    </button>

    <!-- Menu notifications -->
    <mat-menu #notificationMenu="matMenu" class="notification-menu">
      <div class="notification-header">
        <h3>Notifications</h3>
        <button mat-icon-button (click)="markAllNotificationsAsRead()" *ngIf="notifications.length > 0" class="custom-icon-btn">
          <span class="custom-icon">✅</span>
        </button>
      </div>

      <div class="notification-list" *ngIf="notifications.length > 0">
        <div *ngFor="let notification of notifications"
             class="notification-item"
             [class.unread]="!notification.isRead"
             (click)="markAsRead(notification)">
          <div class="notification-icon">
            <span class="custom-icon" [ngClass]="getNotificationIconClass(notification.type)">
              {{ getNotificationIcon(notification.type) }}
            </span>
          </div>
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ notification.createdAt | date:'dd/MM/yyyy HH:mm' }}</div>
          </div>
          <div class="notification-actions" *ngIf="notification.actionUrl">
            <button mat-icon-button (click)="navigateToNotification(notification)" class="custom-icon-btn">
              <span class="custom-icon">➡️</span>
            </button>
          </div>
        </div>
      </div>

      <div class="notification-empty" *ngIf="notifications.length === 0">
        <span class="custom-icon large">🔕</span>
        <p>Aucune notification</p>
      </div>

      <mat-divider></mat-divider>
      <button mat-menu-item routerLink="/dashboard/notifications">
        <span class="custom-icon">📋</span>
        <span>Voir toutes les notifications</span>
      </button>
    </mat-menu>

    <!-- Menu utilisateur -->
    <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-menu-btn">
      <div class="user-avatar">
        {{ currentUser?.firstName?.charAt(0) }}{{ currentUser?.lastName?.charAt(0) }}
      </div>
    </button>

    <!-- Menu utilisateur -->
    <mat-menu #userMenu="matMenu">
      <div class="user-info">
        <p><strong>{{currentUser?.firstName}} {{currentUser?.lastName}}</strong></p>
        <p class="user-role">{{getRoleLabel(currentUser?.role)}}</p>
      </div>

      <!-- Navigation selon le rôle -->
      <mat-divider></mat-divider>

      <!-- Menu Patient -->
      <ng-container *ngIf="isPatient">
        <button mat-menu-item routerLink="/dashboard/appointments">
          <span class="custom-icon">📅</span>
          <span>Mes Rendez-vous</span>
        </button>
        <button mat-menu-item routerLink="/dashboard/new-appointment">
          <span class="custom-icon">➕</span>
          <span>Nouveau Rendez-vous</span>
        </button>
      </ng-container>

      <!-- Menu Infirmier -->
      <ng-container *ngIf="isNurse">
        <button mat-menu-item routerLink="/dashboard/nurse-dashboard">
          <span class="custom-icon">👩‍⚕️</span>
          <span>Mes Missions</span>
        </button>
      </ng-container>

      <!-- Menu Admin -->
      <ng-container *ngIf="isAdmin">
        <button mat-menu-item routerLink="/dashboard/admin-appointments">
          <span class="custom-icon">👨‍💼</span>
          <span>Gestion RDV Admin</span>
        </button>
        <button mat-menu-item routerLink="/dashboard/admin-nurses">
          <span class="custom-icon">👩‍⚕️</span>
          <span>Gestion Infirmiers</span>
        </button>
      </ng-container>

      <mat-divider></mat-divider>


      <!-- Actions communes -->
      <button mat-menu-item routerLink="/dashboard/profile">
        <span class="custom-icon">👤</span>
        <span>Mon Profil</span>
      </button>

      <button mat-menu-item routerLink="/dashboard/notifications">
        <span class="custom-icon">🔔</span>
        <span>Notifications</span>
      </button>

      <mat-divider></mat-divider>

      <button mat-menu-item (click)="logout()">
        <span class="custom-icon">🚪</span>
        <span>Déconnexion</span>
      </button>
    </mat-menu>
  </div>

  <!-- Actions pour utilisateurs non connectés -->
  <div class="header-actions" *ngIf="!isAuthenticated">
    <a routerLink="/auth/login" mat-button class="auth-btn">Connexion</a>
    <a routerLink="/auth/register" mat-button class="auth-btn">Inscription</a>
  </div>
</mat-toolbar>


<!-- Contenu de la page -->
<div class="page-content">
  <ng-content></ng-content>
</div>
