import { Component, OnInit, ChangeDetectorRef, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatToolbarModule } from '@angular/material/toolbar';

import { MatMenuModule } from '@angular/material/menu';
import { MatBadgeModule } from '@angular/material/badge';
import { MatDividerModule } from '@angular/material/divider';
import { AuthService } from '../../services/auth.service';
import { NotificationService } from '../../services/notification.service';
import { User, Role } from '../../models/user.model';
import { Notification, NotificationType } from '../../models/notification.model';
import { Observable, Subscription } from 'rxjs';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatIconModule,
    MatButtonModule,
    MatToolbarModule,
    MatMenuModule,
    MatBadgeModule,
    MatDividerModule
  ],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']
})
export class HeaderComponent implements OnInit, OnDestroy {
  isAuthenticated = false;
  currentUser: User | null = null;
  notificationCount = 0;
  isMenuOpen = false;
  notifications: Notification[] = [];
  
  // Propriétés pour éviter les erreurs de changement d'expression
  isAdmin = false;
  isNurse = false;
  isPatient = false;
  
  private subscription = new Subscription();
  unreadCount$: Observable<number>;

  constructor(
    public authService: AuthService,
    private router: Router,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef
  ) {
    this.unreadCount$ = this.notificationService.unreadCount$;
  }

  ngOnInit(): void {
    // S'abonner aux changements d'authentification
    this.subscription.add(
      this.authService.currentUser$.subscribe(user => {
        setTimeout(() => {
          this.currentUser = user;
          this.isAuthenticated = !!user;
          
          // Mettre à jour les propriétés de rôle
          this.isAdmin = user ? this.authService.isAdmin() : false;
          this.isNurse = user ? this.authService.isNurse() : false;
          this.isPatient = user ? this.authService.isPatient() : false;

          // Charger les notifications et mettre à jour le compteur
          if (user) {
            this.loadNotificationCount();
            this.loadNotificationsFromService();
            this.notificationService.updateUnreadCount();
          } else {
            this.notificationCount = 0;
          }
          
          this.cdr.detectChanges();
        });
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  getRoleLabel(role?: Role): string {
    switch (role) {
      case Role.PATIENT: return 'Patient';
      case Role.NURSE: return 'Infirmier';
      case Role.ADMIN: return 'Administrateur';
      default: return '';
    }
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/home']); // Redirection vers la page d'accueil
  }

  onNotificationMenuOpened(): void {
    console.log('Notification menu opened');
    console.log('Current user:', this.currentUser);
    console.log('Is authenticated:', this.isAuthenticated);

    // Charger les notifications quand le menu s'ouvre
    this.loadNotifications();
    this.notificationService.updateUnreadCount();
  }

  markAllNotificationsAsRead(): void {
    this.notifications.forEach(n => n.isRead = true);
    this.notificationService.markAllAsRead().subscribe(() => {
      this.notificationService.updateUnreadCount();
    });
  }

  markAsRead(notification: Notification): void {
    notification.isRead = true;
    // TODO: Appeler le service pour marquer comme lu en base
  }

  navigateToNotification(notification: Notification): void {
    if (notification.actionUrl) {
      this.markAsRead(notification);
      this.router.navigate([notification.actionUrl]);
    }
  }

  getNotificationIcon(type: string | NotificationType): string {
    const iconMap: { [key: string]: string } = {
      [NotificationType.APPOINTMENT_CONFIRMED]: '✅',
      [NotificationType.NURSE_ASSIGNED]: '👩‍⚕️',
      [NotificationType.NURSE_ON_WAY]: '🚗',
      [NotificationType.SAMPLING_COMPLETED]: '✔️',
      [NotificationType.RESULTS_AVAILABLE]: '📊',
      [NotificationType.URGENT_REQUEST]: '🚨',
      [NotificationType.SYSTEM_ALERT]: '⚠️',
      [NotificationType.REMINDER]: '⏰'
    };
    return iconMap[type.toString()] || '🔔';
  }

  getNotificationIconClass(type: string | NotificationType): string {
    const classMap: { [key: string]: string } = {
      [NotificationType.APPOINTMENT_CONFIRMED]: 'icon-success',
      [NotificationType.NURSE_ASSIGNED]: 'icon-info',
      [NotificationType.NURSE_ON_WAY]: 'icon-warning',
      [NotificationType.SAMPLING_COMPLETED]: 'icon-success',
      [NotificationType.RESULTS_AVAILABLE]: 'icon-info',
      [NotificationType.URGENT_REQUEST]: 'icon-error',
      [NotificationType.SYSTEM_ALERT]: 'icon-error',
      [NotificationType.REMINDER]: 'icon-info'
    };
    return classMap[type.toString()] || 'icon-default';
  }

  private loadNotifications(): void {
    // Charger les vraies notifications depuis l'API
    this.loadNotificationsFromService();
  }

  private loadNotificationCount(): void {
    console.log('=== Loading notification count ===');
    console.log('Current user:', this.currentUser);
    console.log('Is authenticated:', this.isAuthenticated);

    if (this.isAuthenticated && this.currentUser) {
      this.notificationService.getUnreadCount().subscribe({
        next: (count) => {
          console.log('Notification count received:', count);
          this.notificationCount = count;
        },
        error: (error) => {
          console.error('Error loading notification count:', error);
          this.notificationCount = 0;
        }
      });
    } else {
      console.log('User not authenticated, setting count to 0');
      this.notificationCount = 0;
    }
  }

  private loadNotificationsFromService(): void {
    console.log('=== Loading notifications from service ===');
    console.log('Auth token exists:', !!this.authService.getToken());
    console.log('Token preview:', this.authService.getToken()?.substring(0, 20) + '...');
    console.log('Is authenticated:', this.authService.isAuthenticated());
    console.log('Current user:', this.currentUser);
    console.log('API URL will be:', `${this.notificationService['apiUrl']}/unread`);

    // Charger toutes les notifications (pas seulement les non lues)
    this.notificationService.getNotifications().subscribe({
      next: (notifications) => {
        console.log('✅ Notifications loaded successfully:', notifications);
        this.notifications = notifications;
        
        // Mettre à jour le compteur avec les notifications non lues
        const unreadCount = notifications.filter(n => !n.isRead).length;
        console.log('Unread notifications count:', unreadCount);
        this.notificationCount = unreadCount;
      },
      error: (error) => {
        console.error('❌ Error loading notifications:', error);
        console.error('Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          url: error.url
        });
        
        // En cas d'erreur, vider les notifications et remettre le compteur à 0
        this.notifications = [];
        this.notificationCount = 0;
      }
    });
  }
}
