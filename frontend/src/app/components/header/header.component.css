/* Variables CSS */
:host {
  --primary-blue: #1976d2;
  --primary-blue-dark: #1565c0;
  --accent-color: #ff4081;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #eeeeee;
  --neutral-300: #e0e0e0;
  --neutral-400: #bdbdbd;
  --neutral-500: #9e9e9e;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.12);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
  --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
  --transition: all 0.3s ease;
}

/* Header principal */
.main-header {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
  color: white;
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 1000;
  padding: 0 var(--spacing-4);
  min-height: 64px;
}

/* Brand */
.brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  cursor: pointer;
  transition: var(--transition);
}

.brand:hover {
  transform: scale(1.02);
}

.brand-icon {
  font-size: 2rem;
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-title {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.2;
}

.brand-subtitle {
  font-size: 0.75rem;
  opacity: 0.9;
  font-weight: 400;
}

/* Avatar utilisateur dans le bouton */
.user-menu-btn .user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  color: white;
  backdrop-filter: blur(10px);
}

/* Styles pour les icônes personnalisées */
.custom-icon {
  font-size: 1.2rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  min-width: 20px;
}

.custom-icon.large {
  font-size: 2.5rem;
  margin-bottom: 8px;
}

.custom-icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-icon-btn .custom-icon {
  margin-right: 0;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  color: inherit;
}

.header-brand:hover {
  transform: scale(1.02);
  opacity: 0.9;
}

.spacer {
  flex: 1;
}

/* Actions header */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.notification-btn,
.user-menu-btn {
  color: white;
  transition: var(--transition);
}

.notification-btn:hover,
.user-menu-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

/* Menu notifications */
.notification-menu {
  min-width: 350px;
  max-width: 400px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--neutral-200);
  background: var(--neutral-50);
}

.notification-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--neutral-800);
}

.notification-count {
  background: var(--error-color);
  color: white;
  padding: 2px 8px;
  border-radius: var(--radius-lg);
  font-size: 0.75rem;
  font-weight: 600;
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--neutral-100);
  transition: var(--transition);
  position: relative;
}

.notification-item:hover {
  background: var(--neutral-50);
}

.notification-item.unread {
  background: linear-gradient(90deg, rgba(25, 118, 210, 0.05) 0%, transparent 100%);
  border-left: 3px solid var(--primary-blue);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: var(--spacing-1);
  font-size: 0.9rem;
}

.notification-message {
  color: var(--neutral-600);
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: var(--spacing-1);
}

.notification-time {
  color: var(--neutral-500);
  font-size: 0.75rem;
}

.notification-indicator {
  width: 8px;
  height: 8px;
  background: var(--primary-blue);
  border-radius: 50%;
  margin-top: var(--spacing-1);
  margin-left: var(--spacing-2);
}

.no-notifications {
  text-align: center;
  padding: var(--spacing-6);
  color: var(--neutral-500);
}

.no-notifications .mat-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  margin-bottom: var(--spacing-2);
  opacity: 0.5;
}

/* Styles pour les notifications */
.notification-menu {
  width: 350px;
  max-height: 400px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.notification-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.notification-list {
  max-height: 320px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f8fafc;
}

.notification-item.unread {
  background-color: #eff6ff;
  border-left: 4px solid #3b82f6;
}

.notification-icon {
  margin-right: 12px;
  margin-top: 2px;
  font-size: 1.2rem;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: #1e293b;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 0.8rem;
  color: #64748b;
  line-height: 1.4;
  margin-bottom: 4px;
}

.notification-time {
  font-size: 0.75rem;
  color: #94a3b8;
}

.notification-actions {
  margin-left: 8px;
}

.notification-actions button {
  color: #64748b;
}

.notification-empty {
  text-align: center;
  padding: 32px 16px;
  color: #94a3b8;
}

.notification-empty .custom-icon.large {
  font-size: 3rem;
  display: block;
  margin-bottom: 8px;
  opacity: 0.5;
}

/* Styles pour les icônes de notification */
.icon-success { color: #10b981; }
.icon-info { color: #3b82f6; }
.icon-warning { color: #f59e0b; }
.icon-error { color: #ef4444; }
.icon-default { color: #6b7280; }

/* Menu utilisateur - Identique au dashboard */
.user-info {
  padding: 16px;
  text-align: center;
}

.user-info p {
  margin: 0;
}

.user-role {
  color: #666;
  font-size: 0.9em;
  margin: 0;
}

.auth-actions {
  display: flex;
  gap: var(--spacing-2);
  padding: var(--spacing-2);
}

.auth-btn {
  color: var(--primary-blue);
  font-weight: 500;
  text-decoration: none;
  transition: var(--transition);
}

.auth-btn:hover {
  background: var(--neutral-100);
}

/* Contenu page */
.page-content {
  padding: 0;
  background: var(--neutral-50);
  width: 100%;
  /* Suppression de min-height pour éviter les barres de défilement multiples */
}

/* Responsive */
@media (max-width: 768px) {
  .sidenav {
    width: 100%;
  }
  
  .brand-subtitle {
    display: none;
  }
  
  .notification-menu {
    min-width: 300px;
  }
  
  .user-menu {
    min-width: 250px;
  }
}

@media (max-width: 480px) {
  .brand-subtitle {
    display: none;
  }
  
  .logout-btn span {
    display: none;
  }
}
