import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ChangeDetectorRef, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

export interface SimpleLocation {
  latitude: number;
  longitude: number;
  address: string;
}

@Component({
  selector: 'app-simple-location-picker',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="location-picker">
      <h3 class="picker-title">📍 Sélection de l'emplacement</h3>

      <!-- Méthode principale: Géolocalisation automatique -->
      <div class="method-card geolocation-card">
        <div class="method-header">
          <div class="method-icon">🎯</div>
          <h4>Géolocalisation automatique</h4>
        </div>
        <p class="method-description">Détection précise de votre position actuelle</p>
        <button
          mat-raised-button
          color="primary"
          (click)="getCurrentLocation()"
          [disabled]="isLoadingLocation"
          class="method-btn">
          <span *ngIf="!isLoadingLocation" class="btn-icon">📍</span>
          <mat-spinner *ngIf="isLoadingLocation" diameter="20"></mat-spinner>
          {{ isLoadingLocation ? 'Localisation en cours...' : 'Détecter ma position' }}
        </button>

      <!-- Méthode 3: Carte interactive (largeur complète) -->
      <div class="method-card map-card full-width">
        <div class="method-header">
          <div class="method-icon">🗺️</div>
          <h4>Carte interactive</h4>
        </div>
        <p class="method-description">Cliquez directement sur la carte pour choisir votre position</p>
        <div class="map-container">
          <div #mapContainer class="leaflet-map"></div>
          <div class="map-overlay" *ngIf="!selectedLocation">
            <div class="map-instruction">
              <span class="touch-icon">👆</span>
              <span>Cliquez sur la carte</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Position sélectionnée (largeur complète) -->
      <div *ngIf="selectedLocation" class="selected-location full-width">
        <div class="location-card success-card compact">
          <div class="location-header">
            <div class="success-icon">📍</div>
            <div class="header-content">
              <h4>✅ Position confirmée</h4>
              <p class="selection-method" *ngIf="lastAccuracy">
                🎯 Géolocalisation (±{{ roundAccuracy(lastAccuracy) }}m)
              </p>
              <p class="selection-method" *ngIf="!lastAccuracy">
                ✏️ Sélection manuelle
              </p>
            </div>
            <button
              mat-stroked-button
              color="primary"
              (click)="clearSelection()"
              class="clear-btn compact-btn">
              <span class="btn-icon">🔄</span>
              Changer
            </button>
          </div>

          <div class="location-details compact-details">
            <div class="detail-row">
              <span class="detail-icon">🏠</span>
              <span class="address">{{ selectedLocation.address }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-icon">📌</span>
              <span class="coordinates">
                {{ selectedLocation.latitude.toFixed(6) }}, {{ selectedLocation.longitude.toFixed(6) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Ligne de séparation -->
      <div class="separator-line">
        <div class="line"></div>
        <span class="separator-text">OU</span>
        <div class="line"></div>
      </div>

      <!-- Méthode alternative: Saisie manuelle -->
      <div class="method-card manual-card full-width">
        <div class="method-header">
          <div class="method-icon">✏️</div>
          <h4>Saisie manuelle</h4>
        </div>
        <p class="method-description">Tapez votre adresse complète si la géolocalisation ne fonctionne pas</p>
        <mat-form-field appearance="outline" class="address-field">
          <mat-label>Adresse complète</mat-label>
          <input
            matInput
            [(ngModel)]="manualAddress"
            placeholder="Ex: 123 Avenue Habib Bourguiba, Tunis"
            (input)="onAddressChange()">
          <span class="input-icon" matSuffix>📍</span>
        </mat-form-field>
        <button
          mat-raised-button
          color="accent"
          (click)="useManualAddress()"
          [disabled]="!manualAddress.trim()"
          class="method-btn">
          <span class="btn-icon">✅</span>
          Confirmer l'adresse
        </button>
      </div>
    </div>
  `,
  styles: [`
    .location-picker {
      padding: 24px;
      max-width: 900px;
      margin: 0 auto;
    }

    .picker-title {
      text-align: center;
      color: #333;
      margin-bottom: 32px;
      font-size: 1.5rem;
      font-weight: 600;
    }

    .full-width {
      width: 100%;
      margin: 24px 0;
    }

    .separator-line {
      display: flex;
      align-items: center;
      margin: 32px 0;
      gap: 16px;
    }

    .line {
      flex: 1;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, #ddd 50%, transparent 100%);
    }

    .separator-text {
      background: white;
      padding: 8px 16px;
      color: #666;
      font-weight: 500;
      font-size: 0.9rem;
      border: 1px solid #ddd;
      border-radius: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .method-card {
      background: white;
      border: 2px solid #e9ecef;
      border-radius: 16px;
      padding: 24px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .method-card:hover {
      border-color: #667eea;
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15);
      transform: translateY(-2px);
    }

    .method-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
    }

    .method-icon {
      font-size: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .method-header h4 {
      margin: 0;
      color: #333;
      font-size: 1.2rem;
      font-weight: 600;
    }

    .method-description {
      color: #666;
      margin: 0 0 20px 0;
      font-size: 0.95rem;
      line-height: 1.4;
    }

    .method-btn {
      width: 100%;
      height: 48px;
      font-size: 1rem;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .test-btn {
      width: 100%;
      height: 40px;
      font-size: 0.9rem;
      font-weight: 500;
      border: 2px dashed #ff9800;
      color: #ff9800;
      background: rgba(255, 152, 0, 0.1);
    }

    .test-btn:hover {
      background: rgba(255, 152, 0, 0.2);
      border-color: #f57c00;
    }

    .debug-btn {
      width: 100%;
      height: 36px;
      font-size: 0.85rem;
      font-weight: 500;
      border: 2px dashed #f44336;
      color: #f44336;
      background: rgba(244, 67, 54, 0.1);
      margin-top: 8px;
    }

    .debug-btn:hover {
      background: rgba(244, 67, 54, 0.2);
      border-color: #d32f2f;
    }

    .manual-btn {
      width: 100%;
      height: 48px;
      font-size: 1rem;
      font-weight: 600;
      background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
      color: white;
      border: none;
      margin-top: 12px;
      box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
    }

    .manual-btn:hover {
      background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
    }

    .btn-icon {
      font-size: 16px;
      margin-right: 8px;
    }

    .input-icon {
      font-size: 18px;
      color: #666;
    }

    .touch-icon {
      font-size: 24px;
      margin-right: 8px;
    }

    /* Carte spécifique */
    .map-card .map-container {
      position: relative;
      height: 250px;
      width: 100%;
      border-radius: 12px;
      overflow: hidden;
      border: 2px solid #e9ecef;
      margin-top: 16px;
    }

    .leaflet-map {
      height: 100%;
      width: 100%;
      z-index: 1;
    }

    .map-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(102, 126, 234, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      backdrop-filter: blur(2px);
    }

    .map-instruction {
      background: white;
      padding: 16px 24px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      display: flex;
      align-items: center;
      gap: 8px;
      color: #667eea;
      font-weight: 500;
    }

    /* Champ d'adresse */
    .address-field {
      width: 100%;
      margin-bottom: 16px;
    }

    /* Position sélectionnée */
    .selected-location {
      margin-top: 32px;
    }

    .success-card {
      background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
      border-color: #22c55e;
      border-left: 4px solid #22c55e;
    }

    .location-header {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      margin-bottom: 16px;
    }

    .success-icon {
      font-size: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
    }

    .header-content {
      flex: 1;
    }

    .header-content h4 {
      margin: 0 0 4px 0;
      color: #166534;
      font-size: 1.2rem;
      font-weight: 700;
    }

    .selection-method {
      margin: 0;
      color: #16a34a;
      font-size: 0.85rem;
      font-weight: 500;
    }

    .location-details {
      margin-bottom: 0;
    }

    /* Version compacte */
    .compact {
      padding: 16px 20px;
    }

    .compact-details {
      margin-bottom: 0;
    }

    .compact-btn {
      font-size: 0.9rem;
      height: 36px;
      min-width: auto;
    }

    .detail-row {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;
    }

    .detail-icon {
      font-size: 18px;
      margin-right: 8px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
    }

    .address {
      font-size: 1.1rem;
      font-weight: 500;
      color: #166534;
      margin: 0;
    }

    .coordinates {
      font-family: monospace;
      color: #16a34a;
      margin: 0;
      font-size: 0.9rem;
    }



    /* Largeur complète pour tous les composants principaux */
    .full-width,
    .selected-location {
      width: 100%;
      max-width: 100%;
    }

    @media (max-width: 768px) {
      .location-picker {
        padding: 16px;
      }

      .method-card {
        padding: 20px;
      }

      .map-card .map-container {
        height: 200px;
      }

      .picker-title {
        font-size: 1.3rem;
        margin-bottom: 24px;
      }
    }
  `]
})
export class SimpleLocationPickerComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('mapContainer', { static: true }) mapContainer!: ElementRef;
  @Input() initialLocation?: SimpleLocation;
  @Output() locationSelected = new EventEmitter<SimpleLocation>();

  selectedLocation?: SimpleLocation;
  manualAddress = '';
  isLoadingLocation = false;
  lastAccuracy?: number;

  // Propriétés Leaflet
  private map!: any; // L.Map
  private marker?: any; // L.Marker

  // Position par défaut : Tunis
  private defaultLocation = {
    latitude: 36.8065,
    longitude: 10.1815
  };

  constructor(
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    if (this.initialLocation) {
      this.selectedLocation = this.initialLocation;
      this.manualAddress = this.initialLocation.address;
    }
    this.fixLeafletIcons();
    this.checkGeolocationSupport();
  }

  private checkGeolocationSupport() {
    console.log('🔍 Vérification du support de géolocalisation...');

    // Vérifier le support de base
    if (!navigator.geolocation) {
      console.error('❌ navigator.geolocation non disponible');
      return;
    }

    console.log('✅ navigator.geolocation disponible');

    // Vérifier le protocole (HTTPS requis pour la géolocalisation)
    if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
      console.warn('⚠️ Géolocalisation peut ne pas fonctionner sur HTTP (HTTPS recommandé)');
    }

    // Vérifier les permissions si disponible
    if (navigator.permissions) {
      navigator.permissions.query({ name: 'geolocation' }).then((result) => {
        console.log('🔐 État des permissions géolocalisation:', result.state);
        switch (result.state) {
          case 'granted':
            console.log('✅ Permission accordée');
            break;
          case 'denied':
            console.log('❌ Permission refusée');
            break;
          case 'prompt':
            console.log('❓ Permission sera demandée');
            break;
        }
      }).catch((error) => {
        console.log('⚠️ Impossible de vérifier les permissions:', error);
      });
    } else {
      console.log('⚠️ API Permissions non disponible');
    }
  }

  ngAfterViewInit() {
    console.log('🗺️ SimpleLocationPicker initialisé');

    // Petit délai pour s'assurer que le DOM est prêt
    setTimeout(() => {
      this.initializeMap();

      // Si une position initiale est fournie, l'afficher sur la carte
      if (this.initialLocation) {
        this.setLocationOnMap(this.initialLocation.latitude, this.initialLocation.longitude, this.initialLocation.address);
      }
    }, 100);
  }

  ngOnDestroy() {
    if (this.map) {
      this.map.remove();
    }
  }

  roundAccuracy(accuracy: number): number {
    return Math.round(accuracy);
  }



  clearSelection() {
    this.selectedLocation = undefined;
    this.manualAddress = '';
    this.lastAccuracy = undefined;

    // Supprimer le marqueur de la carte
    if (this.marker && this.map) {
      this.map.removeLayer(this.marker);
      this.marker = undefined;
    }

    // Remettre la vue par défaut sur la carte
    if (this.map) {
      this.map.setView([this.defaultLocation.latitude, this.defaultLocation.longitude], 13);
    }

    this.cdr.detectChanges();
  }

  // Méthode supprimée

  private fixLeafletIcons() {
    // Fix pour les icônes par défaut de Leaflet depuis CDN
    const iconRetinaUrl = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png';
    const iconUrl = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png';
    const shadowUrl = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png';

    if (typeof (window as any).L !== 'undefined') {
      const L = (window as any).L;
      const iconDefault = L.icon({
        iconRetinaUrl,
        iconUrl,
        shadowUrl,
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        tooltipAnchor: [16, -28],
        shadowSize: [41, 41]
      });
      L.Marker.prototype.options.icon = iconDefault;
    }
  }

  private initializeMap() {
    if (typeof (window as any).L === 'undefined') {
      console.error('Leaflet n\'est pas chargé');
      return;
    }

    const L = (window as any).L;

    this.map = L.map(this.mapContainer.nativeElement).setView(
      [this.defaultLocation.latitude, this.defaultLocation.longitude],
      13  // Zoom sur Tunis
    );

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(this.map);

    // Événement de clic sur la carte
    this.map.on('click', (e: any) => {
      this.onMapClick(e.latlng.lat, e.latlng.lng);
    });
  }

  private async onMapClick(lat: number, lng: number) {
    console.log('🗺️ Clic sur la carte:', { lat, lng });

    // Obtenir la vraie adresse via géocodage inverse
    console.log('🌍 Géocodage inverse pour clic carte...');
    const address = await this.getRealAddress(lat, lng);

    // Mettre à jour la position
    this.setLocationOnMap(lat, lng, address);

    // Mettre à jour les données
    this.selectedLocation = {
      latitude: lat,
      longitude: lng,
      address: address
    };

    this.manualAddress = address;
    this.lastAccuracy = undefined; // Pas de précision pour un clic manuel

    this.locationSelected.emit(this.selectedLocation);

    // Message de confirmation avec vraie adresse
    this.snackBar.open(`📍 Position sélectionnée: ${address}`, 'Fermer', { duration: 4000 });
    this.cdr.detectChanges();
  }

  private setLocationOnMap(lat: number, lng: number, address: string) {
    if (!this.map) return;

    // Mettre à jour la vue de la carte
    this.map.setView([lat, lng], 15);

    // Supprimer l'ancien marqueur
    if (this.marker) {
      this.map.removeLayer(this.marker);
    }

    // Ajouter un nouveau marqueur
    const L = (window as any).L;
    this.marker = L.marker([lat, lng]).addTo(this.map);

    // Ajouter un popup avec l'adresse
    this.marker.bindPopup(`
      <div style="text-align: center; padding: 8px;">
        <strong style="color: #22c55e;">📍 Position confirmée</strong><br>
        <div style="margin: 8px 0; font-weight: 500;">${address}</div>
        <small style="color: #666; font-family: monospace;">${lat.toFixed(6)}, ${lng.toFixed(6)}</small>
      </div>
    `).openPopup();

    // Déclencher la détection de changement pour masquer l'overlay
    this.cdr.detectChanges();
  }

  getCurrentLocation() {
    console.log('🎯 Début de la géolocalisation...');
    this.isLoadingLocation = true;
    this.cdr.detectChanges();

    // Vérifier le support de la géolocalisation
    if (!navigator.geolocation) {
      console.error('❌ Géolocalisation non supportée par ce navigateur');
      this.isLoadingLocation = false;
      this.snackBar.open('❌ Géolocalisation non supportée par ce navigateur', 'Fermer', { duration: 5000 });
      this.cdr.detectChanges();
      return;
    }

    // Vérifier si on est sur HTTPS ou localhost
    const isSecureContext = window.location.protocol === 'https:' ||
                           window.location.hostname === 'localhost' ||
                           window.location.hostname === '127.0.0.1';

    if (!isSecureContext) {
      console.warn('⚠️ Contexte non sécurisé détecté, utilisation de la position par défaut');
      this.snackBar.open('⚠️ Géolocalisation nécessite HTTPS. Position par défaut utilisée (Tunis centre).', 'Fermer', {
        duration: 6000,
        panelClass: ['warning-snackbar']
      });
      this.useDefaultPosition();
      return;
    }

    console.log('✅ Contexte sécurisé détecté, géolocalisation GPS disponible');

    // Vérifier les permissions
    if (navigator.permissions) {
      navigator.permissions.query({ name: 'geolocation' }).then((result) => {
        console.log('🔐 Permission géolocalisation:', result.state);
        if (result.state === 'denied') {
          this.isLoadingLocation = false;
          this.snackBar.open('❌ Permission géolocalisation refusée. Veuillez l\'autoriser dans les paramètres du navigateur.', 'Fermer', { duration: 8000 });
          this.cdr.detectChanges();
          return;
        }
      });
    }

    console.log('📡 Demande de position en cours...');
    console.log('🌐 User Agent:', navigator.userAgent);
    console.log('🔒 Protocol:', location.protocol);
    console.log('🏠 Hostname:', location.hostname);

    // Options optimisées pour une meilleure détection
    const options: PositionOptions = {
      enableHighAccuracy: true,     // Utiliser GPS si disponible
      timeout: 15000,              // 15 secondes pour GPS, puis fallback
      maximumAge: 0                // Toujours demander une nouvelle position
    };

    console.log('⚙️ Options géolocalisation:', options);

    // Essayer d'abord getCurrentPosition avec options optimisées
    navigator.geolocation.getCurrentPosition(
      (position) => {
        console.log('✅ Position reçue via getCurrentPosition:', position);
        this.handleLocationSuccess(position);
      },
      (error) => {
        console.warn('⚠️ getCurrentPosition échoué, essai avec watchPosition...', error);
        this.tryWatchPosition(options);
      },
      options
    );
  }

 
  // Méthode pour définir une position personnalisée
  setCustomPosition() {
    const lat = prompt('📍 Entrez la LATITUDE (ex: 36.8065) :');
    const lng = prompt('📍 Entrez la LONGITUDE (ex: 10.1815) :');

    if (!lat || !lng) return;

    const latitude = parseFloat(lat);
    const longitude = parseFloat(lng);

    // Validation des coordonnées
    if (isNaN(latitude) || isNaN(longitude)) {
      alert('❌ Coordonnées invalides. Veuillez entrer des nombres valides.');
      return;
    }

    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      alert('❌ Coordonnées hors limites géographiques.');
      return;
    }

    console.log('📍 Position personnalisée:', { latitude, longitude });

    // Simuler une position GeolocationPosition
    const mockPosition = {
      coords: {
        latitude: latitude,
        longitude: longitude,
        accuracy: 5,
        altitude: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    } as GeolocationPosition;

    this.handleLocationSuccess(mockPosition);
    this.snackBar.open(`✅ Position personnalisée définie : ${latitude}, ${longitude}`, 'Fermer', { duration: 4000 });
  }

  // Méthode pour gérer le succès de géolocalisation
  private async handleLocationSuccess(position: GeolocationPosition) {
    console.log('✅ Position reçue:', position);
    this.isLoadingLocation = false;

    const lat = position.coords.latitude;
    const lng = position.coords.longitude;
    const accuracy = position.coords.accuracy;
    const timestamp = new Date(position.timestamp);

    console.log('📍 Coordonnées détectées:', {
      lat,
      lng,
      accuracy,
      timestamp: timestamp.toLocaleString(),
      altitude: position.coords.altitude,
      heading: position.coords.heading,
      speed: position.coords.speed
    });

    // DIAGNOSTIC : Afficher les coordonnées exactes
    console.log('🔍 DIAGNOSTIC - Coordonnées exactes reçues:');
    console.log(`Latitude: ${lat} (${typeof lat})`);
    console.log(`Longitude: ${lng} (${typeof lng})`);
    console.log(`Précision: ${accuracy}m`);

    // Vérifier si c'est une position simulée DevTools
    if (accuracy === 10 || accuracy === 1 || accuracy < 50) {
      console.log('🧪 Position simulée DevTools détectée (précision parfaite)');
      console.log('✅ Utilisation de la position simulée - précision excellente !');
      // Continuer avec la position simulée car elle est précise
    }

    // Vérifier si la précision GPS est trop mauvaise (>50km)
    else if (accuracy > 50000) {
      console.warn('⚠️ Précision GPS très mauvaise (±' + Math.round(accuracy/1000) + 'km)');
      console.log('� Utilisation de la position fixe personnalisée...');

      // Utiliser la position par défaut à la place
      this.isLoadingLocation = false;
      this.useDefaultPosition();
      return;
    }

    // Vérifier si les coordonnées sont valides
    if (isNaN(lat) || isNaN(lng) || lat === 0 && lng === 0) {
      console.error('❌ Coordonnées invalides:', { lat, lng });
      this.snackBar.open('❌ Coordonnées invalides reçues', 'Fermer', { duration: 5000 });
      this.cdr.detectChanges();
      return;
    }

    // Vérifier si les coordonnées sont dans une plage raisonnable
    if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      console.error('❌ Coordonnées hors limites:', { lat, lng });
      this.snackBar.open('❌ Coordonnées hors limites géographiques', 'Fermer', { duration: 5000 });
      this.cdr.detectChanges();
      return;
    }

    this.lastAccuracy = accuracy;

    // Obtenir une vraie adresse via géocodage inverse
    console.log('🌍 Tentative de géocodage inverse pour:', { lat, lng });
    const address = await this.getRealAddress(lat, lng);
    console.log('🏠 Adresse obtenue:', address);

    this.selectedLocation = {
      latitude: lat,
      longitude: lng,
      address: address
    };

    this.manualAddress = address;

    // Afficher la position sur la carte
    this.setLocationOnMap(lat, lng, address);

    this.locationSelected.emit(this.selectedLocation);

    // Message de succès avec précision et timestamp
    let message = '📍 Position détectée avec succès';
    if (accuracy <= 10) {
      message += ' (🎯 Très précise: ±' + Math.round(accuracy) + 'm)';
    } else if (accuracy <= 50) {
      message += ' (✅ Précise: ±' + Math.round(accuracy) + 'm)';
    } else if (accuracy <= 1000) {
      message += ' (⚠️ Approximative: ±' + Math.round(accuracy) + 'm)';
    } else if (accuracy <= 10000) {
      message += ' (❌ Peu précise: ±' + Math.round(accuracy/1000) + 'km)';
    } else {
      message += ' (❌ Très imprécise: ±' + Math.round(accuracy/1000) + 'km)';
    }

    this.snackBar.open(message, 'Fermer', { duration: 4000 });
    this.cdr.detectChanges();
  }

  // Méthode fallback avec watchPosition
  private tryWatchPosition(options: PositionOptions) {
    console.log('🔄 Essai avec watchPosition...');

    let watchId: number;
    let positionReceived = false;

    // Timeout pour watchPosition
    const watchTimeout = setTimeout(() => {
      if (!positionReceived) {
        navigator.geolocation.clearWatch(watchId);
        console.log('⏱️ watchPosition timeout, utilisation position par défaut...');
        this.useDefaultPosition();
      }
    }, 30000); // 30 secondes pour watchPosition

    watchId = navigator.geolocation.watchPosition(
      (position) => {
        if (!positionReceived) {
          positionReceived = true;
          clearTimeout(watchTimeout);
          navigator.geolocation.clearWatch(watchId);
          console.log('✅ Position reçue via watchPosition:', position);
          this.handleLocationSuccess(position);
        }
      },
      (error) => {
        clearTimeout(watchTimeout);
        navigator.geolocation.clearWatch(watchId);
        console.error('❌ watchPosition aussi échoué:', error);
        console.log('🌐 Utilisation position par défaut...');
        this.useDefaultPosition();
      },
      {
        ...options,
        enableHighAccuracy: false, // Essayer sans haute précision
        timeout: 20000
      }
    );
  }

  // Méthode pour utiliser une position par défaut (Tunis centre)
  private async useDefaultPosition() {
    console.log('📍 Utilisation de la position par défaut (Tunis centre)');

    // Position par défaut pour la Tunisie (Tunis centre)
    const defaultPosition = {
      coords: {
        latitude: 36.8065,
        longitude: 10.1815,
        accuracy: 5, // Haute précision pour test
        altitude: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    } as GeolocationPosition;

    try {
      await this.handleLocationSuccess(defaultPosition);
      this.snackBar.open('📍 Position par défaut utilisée (Tunis centre)', 'Fermer', {
        duration: 5000,
        panelClass: ['warning-snackbar']
      });
    } catch (error) {
      console.error('❌ Erreur lors de l\'utilisation de la position par défaut:', error);
      this.isLoadingLocation = false;
      this.snackBar.open('❌ Erreur de géolocalisation', 'Fermer', { duration: 3000 });
    }
  }

  // Méthode de géolocalisation simplifiée (sans APIs externes)
  private async tryIPGeolocation() {
    console.log('🌐 Géolocalisation GPS non disponible, utilisation de position par défaut...');

    // Utiliser directement la position par défaut pour éviter les problèmes CORS
    await this.useDefaultPosition();
  }

  // Méthode de géolocalisation par fuseau horaire
  private getPositionFromTimezone(): GeolocationPosition | null {
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      console.log('🕐 Fuseau horaire détecté:', timezone);

      // Mapping des fuseaux horaires vers des positions approximatives
      const timezonePositions: { [key: string]: { lat: number, lng: number, name: string } } = {
        'Africa/Tunis': { lat: 36.8065, lng: 10.1815, name: 'Tunis, Tunisie' },
        'Europe/Paris': { lat: 48.8566, lng: 2.3522, name: 'Paris, France' },
        'Europe/London': { lat: 51.5074, lng: -0.1278, name: 'Londres, UK' },
        'America/New_York': { lat: 40.7128, lng: -74.0060, name: 'New York, USA' },
        'Asia/Tokyo': { lat: 35.6762, lng: 139.6503, name: 'Tokyo, Japon' },
        'Europe/Berlin': { lat: 52.5200, lng: 13.4050, name: 'Berlin, Allemagne' },
        'Europe/Rome': { lat: 41.9028, lng: 12.4964, name: 'Rome, Italie' },
        'Europe/Madrid': { lat: 40.4168, lng: -3.7038, name: 'Madrid, Espagne' }
      };

      const position = timezonePositions[timezone];
      if (position) {
        console.log('✅ Position trouvée pour timezone:', position);

        return {
          coords: {
            latitude: position.lat,
            longitude: position.lng,
            accuracy: 50000, // ±50km pour timezone
            altitude: null,
            altitudeAccuracy: null,
            heading: null,
            speed: null
          },
          timestamp: Date.now()
        } as GeolocationPosition;
      }

      console.log('⚠️ Fuseau horaire non reconnu:', timezone);
      return null;

    } catch (error) {
      console.error('❌ Erreur géolocalisation timezone:', error);
      return null;
    }
  }

  // Méthode de géolocalisation WiFi Mozilla (plus précise)
  private async tryMozillaGeolocation(): Promise<GeolocationPosition | null> {
    try {
      console.log('📡 Géolocalisation Mozilla WiFi...');

      // Utiliser l'API Mozilla Location Service
      const response = await fetch('https://location.services.mozilla.com/v1/geolocate?key=test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          considerIp: true,
          wifiAccessPoints: [] // Le navigateur peut fournir les points WiFi
        })
      });

      if (!response.ok) {
        throw new Error(`Mozilla API error: ${response.status}`);
      }

      const data = await response.json();
      console.log('📍 Réponse Mozilla:', data);

      if (data.location) {
        // Créer une position simulée
        const mozillaPosition = {
          coords: {
            latitude: data.location.lat,
            longitude: data.location.lng,
            accuracy: data.accuracy || 1000,
            altitude: null,
            altitudeAccuracy: null,
            heading: null,
            speed: null
          },
          timestamp: Date.now()
        } as GeolocationPosition;

        return mozillaPosition;
      }

      return null;
    } catch (error) {
      console.error('❌ Mozilla géolocalisation échouée:', error);
      return null;
    }
  }

  // Méthode de géolocalisation HTML5 avec options agressives
  private async tryAggressiveGeolocation(): Promise<GeolocationPosition | null> {
    return new Promise((resolve) => {
      console.log('🎯 Géolocalisation agressive...');

      // Options très agressives
      const aggressiveOptions: PositionOptions = {
        enableHighAccuracy: false,  // Désactiver haute précision pour plus de rapidité
        timeout: 5000,             // Timeout court
        maximumAge: 0              // Pas de cache
      };

      navigator.geolocation.getCurrentPosition(
        (position) => {
          console.log('✅ Position agressive obtenue:', position);
          resolve(position);
        },
        (error) => {
          console.log('❌ Géolocalisation agressive échouée:', error);
          resolve(null);
        },
        aggressiveOptions
      );
    });
  }

  // Méthode pour gérer les erreurs de géolocalisation
  private handleLocationError(error: GeolocationPositionError) {
    console.error('❌ Erreur de géolocalisation:', error);
    this.isLoadingLocation = false;

    let errorMessage = 'Erreur de géolocalisation';
    let suggestions = '';

    switch (error.code) {
      case error.PERMISSION_DENIED:
        errorMessage = '❌ Accès à la géolocalisation refusé';
        suggestions = 'Veuillez autoriser la géolocalisation dans les paramètres du navigateur';
        break;
      case error.POSITION_UNAVAILABLE:
        errorMessage = '❌ Position non disponible';
        suggestions = 'Vérifiez votre connexion internet et les services de localisation';
        break;
      case error.TIMEOUT:
        errorMessage = '⏱️ Délai de géolocalisation dépassé';
        suggestions = 'Réessayez ou utilisez la saisie manuelle';
        break;
      default:
        errorMessage = '❌ Erreur inconnue de géolocalisation';
        suggestions = 'Utilisez la saisie manuelle comme alternative';
    }

    console.log('💡 Suggestion:', suggestions);
    this.snackBar.open(`${errorMessage}. ${suggestions}`, 'Fermer', { duration: 8000 });
    this.cdr.detectChanges();
  }

  onAddressChange() {
    // Réinitialiser la position si l'adresse change
    if (this.selectedLocation && this.manualAddress !== this.selectedLocation.address) {
      this.selectedLocation = undefined;
      this.lastAccuracy = undefined;
    }
  }

  async useManualAddress() {
    if (!this.manualAddress.trim()) return;

    console.log('🔍 Géocodage de l\'adresse:', this.manualAddress);
    this.isLoadingLocation = true;
    this.cdr.detectChanges();

    try {
      // Utiliser le géocodage pour obtenir les vraies coordonnées
      const coords = await this.geocodeAddress(this.manualAddress.trim());

      if (coords && coords.lat && coords.lon) {
        console.log('✅ Coordonnées trouvées:', coords);

        this.selectedLocation = {
          latitude: parseFloat(coords.lat),
          longitude: parseFloat(coords.lon),
          address: coords.display_name || this.manualAddress.trim()
        };

        this.setLocationOnMap(this.selectedLocation.latitude, this.selectedLocation.longitude, this.selectedLocation.address);
        this.locationSelected.emit(this.selectedLocation);

        this.snackBar.open('✅ Adresse géolocalisée avec succès !', 'Fermer', { duration: 3000 });
      } else {
        throw new Error('Adresse non trouvée');
      }
    } catch (error) {
      console.warn('⚠️ Géocodage échoué:', error);

      // Fallback : utiliser une position par défaut à Tunis
      const lat = 36.8065;
      const lng = 10.1815;

      this.selectedLocation = {
        latitude: lat,
        longitude: lng,
        address: this.manualAddress.trim()
      };

      this.setLocationOnMap(lat, lng, this.manualAddress.trim());
      this.locationSelected.emit(this.selectedLocation);

      this.snackBar.open('⚠️ Adresse confirmée (position approximative)', 'Fermer', { duration: 4000 });
    }

    this.isLoadingLocation = false;
    this.cdr.detectChanges();
  }

  // Méthode de géocodage (adresse → coordonnées)
  private async geocodeAddress(address: string): Promise<any> {
    console.log('🌍 Géocodage adresse:', address);

    try {
      // Utiliser le proxy Nginx pour éviter CORS
      const url = `/api/tracking/geocode/search?q=${encodeURIComponent(address)}`;
      console.log('🌐 URL géocodage (proxy):', url);

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      console.log('📍 Réponse géocodage adresse:', data);

      if (data && data.length > 0) {
        return data[0]; // Premier résultat
      }

      throw new Error('Aucun résultat trouvé');

    } catch (error) {
      console.error('❌ Erreur géocodage adresse:', error);
      throw error;
    }
  }

  // Méthode pour obtenir une vraie adresse via géocodage inverse
  private async getRealAddress(lat: number, lng: number): Promise<string> {
    console.log('🌍 Géocodage inverse pour:', { lat, lng });

    try {
      // Utiliser l'environment pour déterminer l'URL
      const baseUrl = window.location.hostname === 'localhost' ? 'http://localhost:8080' : '';
      const url = `${baseUrl}/api/tracking/geocode/reverse?lat=${lat}&lon=${lng}`;
      console.log('🌐 URL géocodage:', url);

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      console.log('📍 Réponse géocodage:', data);

      if (data && data.display_name) {
        // Extraire les parties importantes de l'adresse
        const address = data.address || {};
        const parts = [];

        // Numéro et rue
        if (address.house_number) parts.push(address.house_number);
        if (address.road) parts.push(address.road);

        // Quartier/Zone
        if (address.neighbourhood) parts.push(address.neighbourhood);
        else if (address.suburb) parts.push(address.suburb);
        else if (address.city_district) parts.push(address.city_district);

        // Ville
        if (address.city) parts.push(address.city);
        else if (address.town) parts.push(address.town);
        else if (address.village) parts.push(address.village);

        // Pays
        if (address.country) parts.push(address.country);

        const formattedAddress = parts.join(', ');
        console.log('✅ Adresse formatée:', formattedAddress);

        return formattedAddress || data.display_name;
      }

      throw new Error('Aucune adresse trouvée');

    } catch (error) {
      console.error('❌ Géocodage inverse échoué:', error);
      console.error('🌐 URL tentée:', `/api/tracking/geocode/reverse?lat=${lat}&lon=${lng}`);
      console.log('🔄 Utilisation de l\'adresse approximative...');
      return this.generateApproximateAddress(lat, lng);
    }
  }

  private generateApproximateAddress(lat: number, lng: number): string {
    console.log('🏠 Génération d\'adresse pour:', { lat, lng });

    // Vérifier si on est en Tunisie (approximativement)
    const isInTunisia = lat >= 30 && lat <= 38 && lng >= 7 && lng <= 12;

    if (isInTunisia) {
      // Adresses tunisiennes basées sur la position
      const tunisianAreas = [
        'Centre Ville', 'Bab Bhar', 'Lafayette', 'Menzah', 'Ariana',
        'Carthage', 'Sidi Bou Said', 'La Marsa', 'Gammarth', 'Ennasr',
        'Manar', 'Lac', 'Berges du Lac', 'Mutuelleville', 'Montplaisir'
      ];

      const tunisianStreets = [
        'Avenue Habib Bourguiba', 'Rue de la Liberté', 'Avenue Mohamed V',
        'Rue Ibn Khaldoun', 'Avenue de la République', 'Rue du Lac',
        'Avenue de France', 'Rue de Marseille', 'Boulevard du 7 Novembre'
      ];

      // Utiliser les coordonnées pour influencer le choix
      const areaIndex = Math.floor((lat * lng * 1000) % tunisianAreas.length);
      const streetIndex = Math.floor((lat + lng) * 100 % tunisianStreets.length);
      const number = Math.floor((lat * 1000) % 200) + 1;

      const area = tunisianAreas[areaIndex];
      const street = tunisianStreets[streetIndex];

      return `${number} ${street}, ${area}, Tunis`;
    } else {
      // Adresse générique pour autres pays
      const genericAreas = [
        'Centre Ville', 'Quartier Nord', 'Quartier Sud', 'Zone Est', 'Zone Ouest'
      ];

      const genericStreets = [
        'Rue Principale', 'Avenue Centrale', 'Boulevard Principal',
        'Rue de la Paix', 'Avenue de la Liberté'
      ];

      const areaIndex = Math.floor((lat * lng * 1000) % genericAreas.length);
      const streetIndex = Math.floor((lat + lng) * 100 % genericStreets.length);
      const number = Math.floor((lat * 1000) % 200) + 1;

      const area = genericAreas[areaIndex];
      const street = genericStreets[streetIndex];

      // Déterminer le pays approximatif
      let country = 'Localisation détectée';
      if (lat >= 41 && lat <= 51 && lng >= -5 && lng <= 10) {
        country = 'France';
      } else if (lat >= 36 && lat <= 42 && lng >= -9 && lng <= 3) {
        country = 'Espagne';
      } else if (lat >= 35 && lat <= 47 && lng >= 6 && lng <= 19) {
        country = 'Italie';
      }

      return `${number} ${street}, ${area}, ${country}`;
    }
  }
}
