import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { OtpService } from '../../services/otp.service';

@Component({
  selector: 'app-otp-verification',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatSnackBarModule
  ],
  template: `
    <div class="otp-container">
      <div class="otp-wrapper">
        <div class="otp-header">
          <div class="otp-icon">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
          </div>
          <h1 class="otp-title">Vérification Email</h1>
          <p class="otp-subtitle">
            Nous avons envoyé un code de vérification à<br>
            <span class="otp-email">{{ email }}</span>
          </p>
        </div>

        <div class="otp-card">
          <form class="otp-form" (ngSubmit)="verify()">
            <div class="otp-inputs">
              <input
                class="otp-input"
                [class.error]="errorMessage"
                [class.success]="isValidCode() && !errorMessage"
                type="text"
                maxlength="1"
                pattern="[0-9]"
                [(ngModel)]="otpDigits[0]"
                name="digit0"
                (input)="onDigitInput($event, 0)"
                (keydown)="onKeyDown($event, 0)"
                (paste)="onPaste($event)"
                [disabled]="isLoading"
                autocomplete="off">

              <input
                class="otp-input"
                [class.error]="errorMessage"
                [class.success]="isValidCode() && !errorMessage"
                type="text"
                maxlength="1"
                pattern="[0-9]"
                [(ngModel)]="otpDigits[1]"
                name="digit1"
                (input)="onDigitInput($event, 1)"
                (keydown)="onKeyDown($event, 1)"
                (paste)="onPaste($event)"
                [disabled]="isLoading"
                autocomplete="off">

              <input
                class="otp-input"
                [class.error]="errorMessage"
                [class.success]="isValidCode() && !errorMessage"
                type="text"
                maxlength="1"
                pattern="[0-9]"
                [(ngModel)]="otpDigits[2]"
                name="digit2"
                (input)="onDigitInput($event, 2)"
                (keydown)="onKeyDown($event, 2)"
                (paste)="onPaste($event)"
                [disabled]="isLoading"
                autocomplete="off">

              <input
                class="otp-input"
                [class.error]="errorMessage"
                [class.success]="isValidCode() && !errorMessage"
                type="text"
                maxlength="1"
                pattern="[0-9]"
                [(ngModel)]="otpDigits[3]"
                name="digit3"
                (input)="onDigitInput($event, 3)"
                (keydown)="onKeyDown($event, 3)"
                (paste)="onPaste($event)"
                [disabled]="isLoading"
                autocomplete="off">

              <input
                class="otp-input"
                [class.error]="errorMessage"
                [class.success]="isValidCode() && !errorMessage"
                type="text"
                maxlength="1"
                pattern="[0-9]"
                [(ngModel)]="otpDigits[4]"
                name="digit4"
                (input)="onDigitInput($event, 4)"
                (keydown)="onKeyDown($event, 4)"
                (paste)="onPaste($event)"
                [disabled]="isLoading"
                autocomplete="off">

              <input
                class="otp-input"
                [class.error]="errorMessage"
                [class.success]="isValidCode() && !errorMessage"
                type="text"
                maxlength="1"
                pattern="[0-9]"
                [(ngModel)]="otpDigits[5]"
                name="digit5"
                (input)="onDigitInput($event, 5)"
                (keydown)="onKeyDown($event, 5)"
                (paste)="onPaste($event)"
                [disabled]="isLoading"
                autocomplete="off">
            </div>

            <div class="validation-message error" *ngIf="errorMessage">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              {{ errorMessage }}
            </div>

            <button
              type="submit"
              class="submit-button"
              [class.active]="isValidCode() && !isLoading"
              [class.disabled]="!isValidCode() || isLoading"
              [class.loading]="isLoading"
              [disabled]="!isValidCode() || isLoading">
              <span *ngIf="!isLoading">Vérifier le code</span>
              <div *ngIf="isLoading" class="loading-spinner">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                </svg>
                Vérification...
              </div>
            </button>
          </form>

          <div class="resend-section">
            <p class="resend-text">Vous n'avez pas reçu le code ?</p>
            <button
              class="resend-button"
              [class.active]="canResend && !isLoading"
              [class.disabled]="!canResend || isLoading"
              (click)="resend()"
              [disabled]="!canResend || isLoading">
              {{ canResend ? 'Renvoyer le code' : 'Attendre avant de renvoyer' }}
            </button>
          </div>

          <div class="back-section">
            <button class="back-button" (click)="goBack()">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
              </svg>
              Retour à l'inscription
            </button>
          </div>

          <div class="help-text">
            <p>Vérifiez également votre dossier spam/courrier indésirable</p>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    /* Reset et base */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    :host {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #1f2937;
    }

    /* Container principal */
    .otp-container {
      min-height: 100vh;
      background: linear-gradient(135deg, #f0f4ff 0%, #ffffff 50%, #faf5ff 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1rem;
    }

    .otp-wrapper {
      width: 100%;
      max-width: 28rem;
      animation: fadeInUp 0.6s ease-out;
    }

    /* Header */
    .otp-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .otp-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 4rem;
      height: 4rem;
      background: linear-gradient(135deg, #6366f1, #9333ea);
      border-radius: 1rem;
      margin-bottom: 1rem;
      box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
    }

    .otp-icon svg {
      width: 2rem;
      height: 2rem;
      color: white;
    }

    .otp-title {
      font-size: 1.875rem;
      font-weight: 700;
      color: #111827;
      margin-bottom: 0.5rem;
    }

    .otp-subtitle {
      color: #6b7280;
      line-height: 1.6;
    }

    .otp-email {
      font-weight: 600;
      color: #111827;
    }

    /* Carte principale */
    .otp-card {
      background: white;
      border-radius: 1.5rem;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
      padding: 2rem;
      border: 1px solid #f3f4f6;
      animation: slideIn 0.8s ease-out 0.2s both;
    }

    /* Formulaire */
    .otp-form {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    /* Champs OTP */
    .otp-inputs {
      display: flex;
      justify-content: center;
      gap: 0.75rem;
      margin-bottom: 1.5rem;
    }

    .otp-input {
      width: 3.5rem;
      height: 3.5rem;
      text-align: center;
      font-size: 1.5rem;
      font-weight: 700;
      border-radius: 0.75rem;
      border: 2px solid #e5e7eb;
      background-color: #f9fafb;
      transition: all 0.2s ease;
      outline: none;
    }

    .otp-input:hover {
      border-color: #d1d5db;
    }

    .otp-input:focus {
      border-color: #6366f1;
      background-color: white;
      box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
    }

    .otp-input.error {
      border-color: #ef4444;
      background-color: #fef2f2;
      color: #dc2626;
    }

    .otp-input.success {
      border-color: #10b981;
      background-color: #f0fdf4;
      color: #059669;
    }

    .otp-input:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    /* Messages de validation */
    .validation-message {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.75rem;
      border-radius: 0.75rem;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .validation-message.error {
      color: #dc2626;
      background-color: #fef2f2;
      animation: shake 0.5s ease-in-out;
    }

    .validation-message.success {
      color: #059669;
      background-color: #f0fdf4;
    }

    .validation-message svg {
      width: 1.25rem;
      height: 1.25rem;
    }

    /* Animation shake pour les erreurs */
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      25% { transform: translateX(-5px); }
      75% { transform: translateX(5px); }
    }

    /* Bouton de soumission */
    .submit-button {
      width: 100%;
      padding: 1rem;
      border-radius: 0.75rem;
      font-weight: 600;
      color: white;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 1rem;
    }

    .submit-button.active {
      background: linear-gradient(135deg, #6366f1, #9333ea);
      box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
    }

    .submit-button.active:hover {
      background: linear-gradient(135deg, #5856eb, #8b2fc7);
      box-shadow: 0 15px 35px rgba(99, 102, 241, 0.4);
      transform: translateY(-1px);
    }

    .submit-button.active:active {
      transform: translateY(0) scale(0.98);
    }

    .submit-button.disabled {
      background-color: #d1d5db;
      cursor: not-allowed;
      box-shadow: none;
    }

    .submit-button.loading {
      position: relative;
      color: transparent;
    }

    /* Loader */
    .loading-spinner {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: white;
    }

    .loading-spinner svg {
      width: 1.25rem;
      height: 1.25rem;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    /* Section de renvoi */
    .resend-section {
      margin-top: 1.5rem;
      text-align: center;
    }

    .resend-text {
      color: #6b7280;
      font-size: 0.875rem;
      margin-bottom: 0.75rem;
    }

    .resend-button {
      font-size: 0.875rem;
      font-weight: 600;
      background: none;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .resend-button.active {
      color: #6366f1;
    }

    .resend-button.active:hover {
      color: #5856eb;
      text-decoration: underline;
    }

    .resend-button.disabled {
      color: #9ca3af;
      cursor: not-allowed;
    }

    /* Bouton retour */
    .back-section {
      margin-top: 1.5rem;
      text-align: center;
    }

    .back-button {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      color: #6b7280;
      font-size: 0.875rem;
      font-weight: 500;
      background: none;
      border: none;
      cursor: pointer;
      transition: color 0.2s ease;
    }

    .back-button:hover {
      color: #4b5563;
    }

    .back-button svg {
      width: 1rem;
      height: 1rem;
    }

    /* Texte d'aide */
    .help-text {
      margin-top: 1.5rem;
      text-align: center;
    }

    .help-text p {
      color: #6b7280;
      font-size: 0.875rem;
    }

    /* Responsive */
    @media (max-width: 640px) {
      .otp-container {
        padding: 0.5rem;
      }

      .otp-card {
        padding: 1.5rem;
      }

      .otp-inputs {
        gap: 0.5rem;
      }

      .otp-input {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
      }

      .otp-title {
        font-size: 1.5rem;
      }
    }

    /* Animations d'entrée */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }
  `]
})
export class OtpVerificationComponent implements OnInit, OnDestroy {
  email: string = '';
  otpCode: string = '';
  isLoading: boolean = false;
  errorMessage: string = '';
  canResend: boolean = false;
  private timer: any;
  otpDigits: string[] = ['', '', '', '', '', ''];
  

  constructor(
    private otpService: OtpService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.email = this.route.snapshot.queryParams['email'] || '';
    console.log('📧 Email récupéré:', this.email);
    if (!this.email) {
      console.log('❌ Aucun email trouvé, redirection vers register');
      this.snackBar.open('Email manquant', 'OK', { duration: 3000 });
      this.router.navigate(['/register']);
      return;
    }
    this.startCooldown();
  }

  ngOnDestroy(): void {
    if (this.timer) clearTimeout(this.timer);
  }

  onInput(event: any): void {
    // Garder seulement les chiffres
    const value = event.target.value.replace(/[^0-9]/g, '');
    this.otpCode = value;
    event.target.value = value;
    this.errorMessage = '';
  }

  onDigitInput(event: any, index: number): void {
    const input = event.target;
    let value = input.value.replace(/[^0-9]/g, '');

    // Limiter à un seul chiffre
    if (value.length > 1) {
      value = value.slice(-1); // Garder seulement le dernier chiffre saisi
      input.value = value;
    }

    // Mettre à jour le tableau
    this.otpDigits[index] = value;
    this.updateOtpCode();
    this.errorMessage = '';

    console.log('Digit input:', index, value);
    console.log('OTP Digits:', this.otpDigits);
    console.log('OTP Code:', this.otpCode);

    // Auto-focus sur le champ suivant si un chiffre a été saisi
    if (value && index < 5) {
      const nextInput = input.parentElement.children[index + 1];
      if (nextInput) {
        nextInput.focus();
        nextInput.select(); // Sélectionner le contenu pour faciliter la saisie
      }
    }
  }

  onKeyDown(event: KeyboardEvent, index: number): void {
    const input = event.target as HTMLInputElement;

    // Gérer la touche Backspace
    if (event.key === 'Backspace') {
      if (this.otpDigits[index]) {
        // Si le champ actuel a une valeur, la supprimer
        this.otpDigits[index] = '';
        input.value = '';
        this.updateOtpCode();
      } else if (index > 0) {
        // Si le champ actuel est vide, aller au champ précédent et le vider
        const prevInput = input.parentElement?.children[index - 1] as HTMLInputElement;
        if (prevInput) {
          this.otpDigits[index - 1] = '';
          prevInput.value = '';
          prevInput.focus();
          this.updateOtpCode();
        }
      }
    }

    // Gérer les flèches
    if (event.key === 'ArrowLeft' && index > 0) {
      const prevInput = input.parentElement?.children[index - 1] as HTMLInputElement;
      if (prevInput) {
        prevInput.focus();
        prevInput.select();
      }
    }

    if (event.key === 'ArrowRight' && index < 5) {
      const nextInput = input.parentElement?.children[index + 1] as HTMLInputElement;
      if (nextInput) {
        nextInput.focus();
        nextInput.select();
      }
    }

    // Empêcher la saisie de caractères non numériques
    if (!/[0-9]/.test(event.key) && !['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
      event.preventDefault();
    }
  }

  onPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const pastedData = event.clipboardData?.getData('text') || '';
    const digits = pastedData.replace(/[^0-9]/g, '').slice(0, 6);

    // Remplir les champs avec les chiffres collés
    for (let i = 0; i < 6; i++) {
      this.otpDigits[i] = digits[i] || '';
    }

    this.updateOtpCode();
    this.errorMessage = '';

    // Mettre à jour visuellement tous les champs
    const inputs = document.querySelectorAll('.otp-input') as NodeListOf<HTMLInputElement>;
    inputs.forEach((input, index) => {
      input.value = this.otpDigits[index];
    });

    // Focus sur le dernier champ rempli ou le premier vide
    const lastFilledIndex = digits.length - 1;
    const targetIndex = Math.min(lastFilledIndex + 1, 5);
    if (inputs[targetIndex]) {
      inputs[targetIndex].focus();
      inputs[targetIndex].select();
    }
  }

  private updateOtpCode(): void {
    this.otpCode = this.otpDigits.join('');
    console.log('🔄 Update OTP Code:', this.otpCode, 'from digits:', this.otpDigits);
  }

  isValidCode(): boolean {
    return this.otpCode.length === 6;
  }

  verify(): void {
    console.log('🔍 VERIFICATION OTP:');
    console.log('   Email:', this.email);
    console.log('   OTP Digits:', this.otpDigits);
    console.log('   OTP Code:', this.otpCode);
    console.log('   Is Valid:', this.isValidCode());

    if (!this.isValidCode()) {
      this.errorMessage = 'Le code doit contenir 6 chiffres';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    console.log('📤 Envoi de la requête OTP...');
    this.otpService.verifyEmailOtp(this.email, this.otpCode).subscribe({
      next: (response) => {
        console.log('✅ Réponse OTP:', response);
        this.snackBar.open('Email vérifié avec succès !', 'OK', { duration: 3000 });
        this.router.navigate(['/login'], {
          queryParams: { verified: 'true', email: this.email }
        });
      },
      error: (error) => {
        console.error('❌ Erreur OTP:', error);
        this.isLoading = false;
        this.errorMessage = error.error?.message || 'Code invalide ou expiré';
        this.clearOtpFields();
      }
    });
  }

  resend(): void {
    if (!this.canResend) return;

    this.isLoading = true;
    this.errorMessage = '';

    this.otpService.resendEmailOtp(this.email).subscribe({
      next: () => {
        this.isLoading = false;
        this.snackBar.open('Code renvoyé !', 'OK', { duration: 3000 });
        this.clearOtpFields();
        this.startCooldown();
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.error?.message || 'Erreur lors du renvoi';
      }
    });
  }

  private clearOtpFields(): void {
    this.otpDigits = ['', '', '', '', '', ''];
    this.otpCode = '';

    // Vider visuellement tous les champs
    setTimeout(() => {
      const inputs = document.querySelectorAll('.otp-input') as NodeListOf<HTMLInputElement>;
      inputs.forEach(input => {
        input.value = '';
      });

      // Focus sur le premier champ
      if (inputs[0]) {
        inputs[0].focus();
      }
    }, 100);
  }

  startCooldown(): void {
    this.canResend = false;
    if (this.timer) clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.canResend = true;
    }, 60000); // 60 secondes
  }

  goBack(): void {
    this.router.navigate(['/register']);
  }
}
