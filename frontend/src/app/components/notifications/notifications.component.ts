import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { NotificationService } from '../../services/notification.service';
import { Notification, NotificationType, NotificationTypeLabels } from '../../models/notification.model';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatListModule,
    MatDividerModule
  ],
  template: `
    <div class="notifications-container">
      <div class="header">
        <h1>Notifications</h1>
        <button mat-raised-button color="primary" (click)="markAllAsRead()" 
                [disabled]="unreadNotifications.length === 0">
          <mat-icon>done_all</mat-icon>
          Tout marquer comme lu
        </button>
      </div>

      <!-- Notifications non lues -->
      <mat-card *ngIf="unreadNotifications.length > 0" class="unread-section">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>notifications_active</mat-icon>
            Notifications non lues ({{unreadNotifications.length}})
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <mat-list>
            <mat-list-item *ngFor="let notification of unreadNotifications; let last = last" 
                           class="notification-item unread" 
                           (click)="markAsRead(notification)">
              <mat-icon matListItemIcon [class]="getNotificationIconClass(notification)">
                {{getNotificationIcon(notification)}}
              </mat-icon>
              <div matListItemTitle>{{notification.title}}</div>
              <div matListItemLine>{{notification.message}}</div>
              <div matListItemMeta class="notification-meta">
                <mat-chip *ngIf="notification.isUrgent" class="urgent-chip">
                  URGENT
                </mat-chip>
                <span class="notification-time">{{notification.createdAt | date:'short'}}</span>
              </div>
              <mat-divider *ngIf="!last"></mat-divider>
            </mat-list-item>
          </mat-list>
        </mat-card-content>
      </mat-card>

      <!-- Toutes les notifications -->
      <mat-card class="all-notifications">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>notifications</mat-icon>
            Toutes les notifications
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div *ngIf="allNotifications.length === 0" class="empty-state">
            <mat-icon>notifications_none</mat-icon>
            <h3>Aucune notification</h3>
            <p>Vous n'avez pas encore reçu de notifications.</p>
          </div>

          <mat-list *ngIf="allNotifications.length > 0">
            <mat-list-item *ngFor="let notification of allNotifications; let last = last" 
                           class="notification-item"
                           [class.read]="notification.isRead"
                           (click)="!notification.isRead && markAsRead(notification)">
              <mat-icon matListItemIcon [class]="getNotificationIconClass(notification)">
                {{getNotificationIcon(notification)}}
              </mat-icon>
              <div matListItemTitle>{{notification.title}}</div>
              <div matListItemLine>{{notification.message}}</div>
              <div matListItemMeta class="notification-meta">
                <mat-chip *ngIf="notification.isUrgent" class="urgent-chip">
                  URGENT
                </mat-chip>
                <mat-chip [class]="getTypeClass(notification.type)">
                  {{getTypeLabel(notification.type)}}
                </mat-chip>
                <span class="notification-time">{{notification.createdAt | date:'short'}}</span>
              </div>
              <mat-divider *ngIf="!last"></mat-divider>
            </mat-list-item>
          </mat-list>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .notifications-container {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
    }

    .unread-section {
      margin-bottom: 20px;
      border-left: 4px solid #2196f3;
    }

    .notification-item {
      cursor: pointer;
      transition: background-color 0.2s;
      padding: 16px;
    }

    .notification-item:hover {
      background-color: #f5f5f5;
    }

    .notification-item.unread {
      background-color: #e3f2fd;
      font-weight: 500;
    }

    .notification-item.read {
      opacity: 0.7;
    }

    .notification-meta {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;
    }

    .notification-time {
      font-size: 0.8em;
      color: #666;
    }

    .urgent-chip {
      background-color: #f44336;
      color: white;
      font-size: 0.7em;
    }

    .type-appointment-confirmed {
      background-color: #4caf50;
      color: white;
    }

    .type-nurse-assigned {
      background-color: #9c27b0;
      color: white;
    }

    .type-nurse-on-way {
      background-color: #ff9800;
      color: white;
    }

    .type-sampling-completed {
      background-color: #2196f3;
      color: white;
    }

    .type-results-available {
      background-color: #4caf50;
      color: white;
    }

    .type-urgent-request {
      background-color: #f44336;
      color: white;
    }

    .type-system-alert {
      background-color: #ff5722;
      color: white;
    }

    .type-reminder {
      background-color: #607d8b;
      color: white;
    }

    .icon-urgent {
      color: #f44336;
    }

    .icon-success {
      color: #4caf50;
    }

    .icon-info {
      color: #2196f3;
    }

    .icon-warning {
      color: #ff9800;
    }

    .empty-state {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    .empty-state mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  `]
})
export class NotificationsComponent implements OnInit {
  allNotifications: Notification[] = [];
  unreadNotifications: Notification[] = [];

  constructor(private notificationService: NotificationService) {}

  ngOnInit(): void {
    this.loadNotifications();
  }

  private loadNotifications(): void {
    console.log('Loading notifications from service...');
    this.notificationService.getNotifications().subscribe({
      next: (notifications) => {
        console.log('Real notifications loaded:', notifications);
        this.allNotifications = notifications;
        this.unreadNotifications = notifications.filter(n => !n.isRead);
      },
      error: (error) => {
        console.error('Error loading notifications from service:', error);
        // En cas d'erreur, utiliser des tableaux vides
        this.allNotifications = [];
        this.unreadNotifications = [];
      }
    });
  }



  markAsRead(notification: Notification): void {
    if (!notification.isRead) {
      this.notificationService.markAsRead(notification.id).subscribe({
        next: () => {
          notification.isRead = true;
          this.unreadNotifications = this.unreadNotifications.filter(n => n.id !== notification.id);
          this.notificationService.updateUnreadCount();
        },
        error: (error) => {
          console.error('Error marking notification as read:', error);
        }
      });
    }
  }

  markAllAsRead(): void {
    this.notificationService.markAllAsRead().subscribe({
      next: () => {
        this.allNotifications.forEach(n => n.isRead = true);
        this.unreadNotifications = [];
        this.notificationService.updateUnreadCount();
      },
      error: (error) => {
        console.error('Error marking all notifications as read:', error);
      }
    });
  }

  getNotificationIcon(notification: Notification): string {
    switch (notification.type) {
      case 'APPOINTMENT_CONFIRMED': return 'event_available';
      case 'NURSE_ASSIGNED': return 'person_add';
      case 'NURSE_ON_WAY': return 'directions_car';
      case 'SAMPLING_COMPLETED': return 'done';
      case 'RESULTS_AVAILABLE': return 'assignment';
      case 'URGENT_REQUEST': return 'priority_high';
      case 'SYSTEM_ALERT': return 'warning';
      case 'REMINDER': return 'schedule';
      default: return 'notifications';
    }
  }

  getNotificationIconClass(notification: Notification): string {
    if (notification.isUrgent) return 'icon-urgent';

    switch (notification.type) {
      case 'APPOINTMENT_CONFIRMED':
      case 'SAMPLING_COMPLETED':
      case 'RESULTS_AVAILABLE':
        return 'icon-success';
      case 'NURSE_ASSIGNED':
      case 'ASSIGNED':
      case 'REMINDER':
        return 'icon-info';
      case 'NURSE_ON_WAY':
      case 'STATUS_CHANGED':
        return 'icon-warning';
      case 'URGENT_REQUEST':
      case 'SYSTEM_ALERT':
        return 'icon-urgent';
      default:
        return 'icon-info';
    }
  }

  getTypeLabel(type: string): string {
    return NotificationTypeLabels[type as keyof typeof NotificationTypeLabels] || type;
  }

  getTypeClass(type: string): string {
    return `type-${type.toLowerCase().replace('_', '-')}`;
  }
}
