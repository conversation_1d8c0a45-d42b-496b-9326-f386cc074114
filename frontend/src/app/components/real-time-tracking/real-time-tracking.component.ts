import { Component, OnInit, OnDestroy, Input, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subscription } from 'rxjs';
import { RealTimeTrackingService, NursePosition, TrackingUpdate } from '../../services/real-time-tracking.service';

@Component({
  selector: 'app-real-time-tracking',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="tracking-container">
      <!-- En-tête de suivi -->
      <div class="tracking-header">
        <div class="status-indicator" [class]="getStatusClass()">
          <span class="status-icon">{{ getStatusIcon() }}</span>
          <span class="status-text">{{ getStatusText() }}</span>
        </div>
        <div class="tracking-info" *ngIf="trackingUpdate">
          <span class="distance">📍 {{ trackingUpdate.distanceRemaining.toFixed(1) }} km</span>
          <span class="eta">⏰ {{ getFormattedETA() }}</span>
        </div>
      </div>

      <!-- Carte de suivi -->
      <div class="map-container">
        <div #mapContainer class="tracking-map"></div>
        
        <!-- Overlay de chargement -->
        <div class="loading-overlay" *ngIf="isLoading">
          <mat-spinner diameter="40"></mat-spinner>
          <p>Localisation de l'infirmier...</p>
        </div>

        <!-- Overlay pas de suivi -->
        <div class="no-tracking-overlay" *ngIf="!isTracking && !isLoading">
          <div class="no-tracking-message">
            <span class="icon">📍</span>
            <h3>Suivi non disponible</h3>
            <p>L'infirmier n'a pas encore activé le partage de position</p>
          </div>
        </div>
      </div>

      <!-- Informations détaillées -->
      <div class="tracking-details" *ngIf="nursePosition">
        <mat-card class="detail-card">
          <div class="detail-row">
            <span class="label">🎯 Précision:</span>
            <span class="value">±{{ Math.round(nursePosition.accuracy) }}m</span>
          </div>
          <div class="detail-row" *ngIf="nursePosition.speed">
            <span class="label">🚗 Vitesse:</span>
            <span class="value">{{ Math.round((nursePosition.speed || 0) * 3.6) }} km/h</span>
          </div>
          <div class="detail-row">
            <span class="label">⏰ Dernière mise à jour:</span>
            <span class="value">{{ getLastUpdateTime() }}</span>
          </div>
        </mat-card>
      </div>

      <!-- Actions -->
      <div class="tracking-actions" *ngIf="isNurse">
        <button 
          mat-raised-button 
          color="primary"
          (click)="startTracking()"
          [disabled]="isTracking"
          class="action-btn">
          <span class="btn-icon">🚀</span>
          {{ isTracking ? 'Suivi actif' : 'Démarrer le suivi' }}
        </button>
        
        <button 
          mat-raised-button 
          color="accent"
          (click)="markAsArrived()"
          [disabled]="!isTracking"
          class="action-btn">
          <span class="btn-icon">🏁</span>
          Je suis arrivé
        </button>
        
        <button 
          mat-stroked-button 
          color="warn"
          (click)="stopTracking()"
          [disabled]="!isTracking"
          class="action-btn">
          <span class="btn-icon">🛑</span>
          Arrêter le suivi
        </button>
      </div>
    </div>
  `,
  styles: [`
    .tracking-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 16px;
    }

    .tracking-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      color: white;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
    }

    .status-icon {
      font-size: 20px;
      animation: pulse 2s infinite;
    }

    .status-text {
      font-size: 1.1rem;
    }

    .tracking-info {
      display: flex;
      gap: 16px;
      font-size: 0.9rem;
    }

    .distance, .eta {
      background: rgba(255,255,255,0.2);
      padding: 4px 8px;
      border-radius: 8px;
    }

    .map-container {
      position: relative;
      height: 400px;
      border-radius: 12px;
      overflow: hidden;
      border: 2px solid #e9ecef;
      margin-bottom: 16px;
    }

    .tracking-map {
      height: 100%;
      width: 100%;
    }

    .loading-overlay,
    .no-tracking-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(255,255,255,0.9);
      backdrop-filter: blur(4px);
    }

    .no-tracking-message {
      text-align: center;
    }

    .no-tracking-message .icon {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    .no-tracking-message h3 {
      margin: 0 0 8px 0;
      color: #666;
    }

    .no-tracking-message p {
      margin: 0;
      color: #999;
    }

    .tracking-details {
      margin-bottom: 16px;
    }

    .detail-card {
      padding: 16px;
    }

    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .detail-row:last-child {
      margin-bottom: 0;
    }

    .label {
      font-weight: 500;
      color: #666;
    }

    .value {
      font-weight: 600;
      color: #333;
    }

    .tracking-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }

    .action-btn {
      flex: 1;
      min-width: 140px;
      height: 48px;
      font-weight: 600;
    }

    .btn-icon {
      margin-right: 8px;
      font-size: 16px;
    }

    .on-way {
      animation: pulse 2s infinite;
    }

    .arrived {
      background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%) !important;
    }

    .offline {
      background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    @media (max-width: 768px) {
      .tracking-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
      }

      .tracking-info {
        justify-content: center;
      }

      .map-container {
        height: 300px;
      }

      .action-btn {
        min-width: 120px;
      }
    }
  `]
})
export class RealTimeTrackingComponent implements OnInit, OnDestroy {
  @ViewChild('mapContainer', { static: true }) mapContainer!: ElementRef;
  @Input() appointmentId!: number;
  @Input() isNurse: boolean = false;
  @Input() patientLocation?: { latitude: number; longitude: number };

  nursePosition: NursePosition | null = null;
  trackingUpdate: TrackingUpdate | null = null;
  isTracking = false;
  isLoading = false;

  private map!: any; // L.Map
  private nurseMarker?: any; // L.Marker
  private patientMarker?: any; // L.Marker
  private routeLine?: any; // L.Polyline

  private subscriptions: Subscription[] = [];

  constructor(
    private trackingService: RealTimeTrackingService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.initializeMap();
    this.setupTrackingSubscriptions();
    
    if (!this.isNurse) {
      // Si c'est un patient, démarrer le suivi de l'infirmier
      this.startPatientTracking();
    }
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.trackingService.stopNurseTracking();
    
    if (this.map) {
      this.map.remove();
    }
  }

  // Méthodes publiques pour le template
  Math = Math;

  getStatusClass(): string {
    if (!this.nursePosition) return 'offline';
    return this.nursePosition.status.toLowerCase().replace('_', '-');
  }

  getStatusIcon(): string {
    if (!this.nursePosition) return '📍';
    switch (this.nursePosition.status) {
      case 'ON_WAY': return '🚗';
      case 'ARRIVED': return '🏁';
      default: return '📍';
    }
  }

  getStatusText(): string {
    if (!this.nursePosition) return 'Hors ligne';
    switch (this.nursePosition.status) {
      case 'ON_WAY': return 'En route';
      case 'ARRIVED': return 'Arrivé';
      default: return 'Hors ligne';
    }
  }

  getFormattedETA(): string {
    if (!this.trackingUpdate) return '--:--';
    const eta = new Date(this.trackingUpdate.estimatedArrival);
    return eta.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
  }

  getLastUpdateTime(): string {
    if (!this.nursePosition) return '--:--';
    const time = new Date(this.nursePosition.timestamp);
    return time.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  }

  // Actions pour les infirmiers
  async startTracking() {
    this.isLoading = true;
    try {
      await this.trackingService.startNurseTracking(this.appointmentId);
      this.isTracking = true;
      console.log('✅ Suivi démarré avec succès');
    } catch (error) {
      console.error('❌ Erreur démarrage suivi:', error);
      alert('Erreur lors du démarrage du suivi. Vérifiez vos paramètres de géolocalisation.');
    } finally {
      this.isLoading = false;
      this.cdr.detectChanges();
    }
  }

  stopTracking() {
    this.trackingService.stopNurseTracking();
    this.isTracking = false;
    this.nursePosition = null;
    this.trackingUpdate = null;
    this.cdr.detectChanges();
  }

  markAsArrived() {
    this.trackingService.markAsArrived(this.appointmentId).subscribe({
      next: () => {
        console.log('✅ Marqué comme arrivé');
        if (this.nursePosition) {
          this.nursePosition.status = 'ARRIVED';
        }
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('❌ Erreur marquage arrivée:', error);
      }
    });
  }

  // Méthodes privées
  private initializeMap() {
    if (typeof (window as any).L === 'undefined') {
      console.error('Leaflet non chargé');
      return;
    }

    const L = (window as any).L;
    
    // Position par défaut (Tunis)
    const defaultLat = this.patientLocation?.latitude || 36.8065;
    const defaultLng = this.patientLocation?.longitude || 10.1815;
    
    this.map = L.map(this.mapContainer.nativeElement).setView([defaultLat, defaultLng], 13);

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(this.map);

    // Ajouter le marqueur patient si position disponible
    if (this.patientLocation) {
      this.addPatientMarker(this.patientLocation.latitude, this.patientLocation.longitude);
    }
  }

  private setupTrackingSubscriptions() {
    // S'abonner aux mises à jour de position
    const positionSub = this.trackingService.getNursePosition().subscribe(position => {
      this.nursePosition = position;
      if (position) {
        this.updateNurseMarker(position);
        this.isTracking = true;
      }
      this.cdr.detectChanges();
    });

    // S'abonner aux mises à jour de suivi
    const trackingSub = this.trackingService.getTrackingUpdates().subscribe(update => {
      this.trackingUpdate = update;
      this.cdr.detectChanges();
    });

    this.subscriptions.push(positionSub, trackingSub);
  }

  private startPatientTracking() {
    this.isLoading = true;
    this.trackingService.startPatientTracking(this.appointmentId);
    
    // Arrêter le loading après 5 secondes max
    setTimeout(() => {
      this.isLoading = false;
      this.cdr.detectChanges();
    }, 5000);
  }

  private addPatientMarker(lat: number, lng: number) {
    const L = (window as any).L;
    
    const patientIcon = L.divIcon({
      html: '<div style="background: #22c55e; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
      iconSize: [26, 26],
      iconAnchor: [13, 13]
    });

    this.patientMarker = L.marker([lat, lng], { icon: patientIcon })
      .addTo(this.map)
      .bindPopup('📍 Patient');
  }

  private updateNurseMarker(position: NursePosition) {
    const L = (window as any).L;
    
    // Supprimer l'ancien marqueur
    if (this.nurseMarker) {
      this.map.removeLayer(this.nurseMarker);
    }

    // Créer l'icône infirmier avec animation
    const nurseIcon = L.divIcon({
      html: `<div style="
        background: #667eea; 
        width: 24px; 
        height: 24px; 
        border-radius: 50%; 
        border: 3px solid white; 
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
        animation: pulse 2s infinite;
      "></div>`,
      iconSize: [30, 30],
      iconAnchor: [15, 15]
    });

    // Ajouter le nouveau marqueur
    this.nurseMarker = L.marker([position.latitude, position.longitude], { icon: nurseIcon })
      .addTo(this.map)
      .bindPopup(`
        <div style="text-align: center;">
          <strong>🚗 Infirmier</strong><br>
          <small>Précision: ±${Math.round(position.accuracy)}m</small><br>
          <small>${new Date(position.timestamp).toLocaleTimeString()}</small>
        </div>
      `);

    // Centrer la carte sur la nouvelle position
    this.map.setView([position.latitude, position.longitude], 15);

    // Dessiner la route si patient et infirmier sont visibles
    this.drawRoute();
  }

  private drawRoute() {
    if (!this.nursePosition || !this.patientLocation) return;

    const L = (window as any).L;

    // Supprimer l'ancienne route
    if (this.routeLine) {
      this.map.removeLayer(this.routeLine);
    }

    // Dessiner une ligne simple entre infirmier et patient
    const latlngs = [
      [this.nursePosition.latitude, this.nursePosition.longitude],
      [this.patientLocation.latitude, this.patientLocation.longitude]
    ];

    this.routeLine = L.polyline(latlngs, {
      color: '#667eea',
      weight: 4,
      opacity: 0.7,
      dashArray: '10, 10'
    }).addTo(this.map);

    // Ajuster la vue pour inclure les deux marqueurs
    const group = L.featureGroup([this.nurseMarker, this.patientMarker]);
    this.map.fitBounds(group.getBounds().pad(0.1));
  }
}
