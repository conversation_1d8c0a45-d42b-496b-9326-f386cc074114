# Admin Dashboard - Gestion des Rendez-vous

## 🎯 Fonctionnalités

### 📊 Vue d'ensemble
- **Statistiques en temps réel** : Nombre de rendez-vous en attente, confirmés, infirmiers disponibles
- **Actions rapides** : Affectation automatique en lot, actualisation des données
- **Interface responsive** : Adaptée aux écrans desktop et mobile

### 🔧 Gestion des rendez-vous en attente
- **Liste détaillée** : Patient, date, adresse, analyses demandées
- **Affectation automatique** : Calcul de l'infirmier le plus proche
- **Affectation manuelle** : Sélection d'un infirmier spécifique
- **Actions en lot** : Traitement de tous les rendez-vous en attente

### 🏥 Gestion des infirmiers
- **Liste des infirmiers disponibles** : Avec informations de contact et localisation
- **Calcul de proximité** : Algorithme de distance géographique
- **Équilibrage de charge** : Évite la surcharge des infirmiers (max 5 rendez-vous actifs)

## 🚀 Utilisation

### Accès
1. Se connecter en tant qu'**Admin**
2. Aller dans **Dashboard** → **Gestion RDV Admin**

### Affectation automatique
1. **Individuelle** : Cliquer sur l'icône ⚡ à côté d'un rendez-vous
2. **En lot** : Cliquer sur "Affecter automatiquement tout"

### Affectation manuelle
1. Cliquer sur l'icône 👤 à côté d'un rendez-vous
2. Sélectionner un infirmier dans la liste
3. Confirmer l'affectation

## 🔧 API Endpoints utilisés

- `GET /api/appointments/admin/pending` - Rendez-vous en attente
- `GET /api/appointments/admin/all` - Tous les rendez-vous
- `GET /api/appointments/admin/nurses/available` - Infirmiers disponibles
- `POST /api/appointments/admin/{id}/auto-assign` - Affectation automatique
- `POST /api/appointments/admin/{id}/assign-nurse/{nurseId}` - Affectation manuelle
- `POST /api/appointments/admin/auto-assign-all` - Affectation en lot

## 🎨 Composants

### AdminDashboardComponent
- **Composant principal** avec tableau de bord et statistiques
- **Gestion des états** : Loading, erreurs, succès
- **Interface responsive** avec Material Design

### NurseSelectionDialogComponent
- **Dialog de sélection** d'infirmier
- **Informations détaillées** sur le rendez-vous et les infirmiers
- **Actions multiples** : Automatique, manuelle, annulation

### AdminService
- **Service dédié** aux fonctionnalités admin
- **Gestion des erreurs** et des réponses
- **Types TypeScript** pour la sécurité

## 🔔 Notifications

- **Succès** : Affectation réussie (vert)
- **Erreur** : Problème d'affectation (rouge)
- **Avertissement** : Affectation partielle (orange)
- **Information** : Actualisation des données (bleu)

## 📱 Responsive Design

- **Desktop** : Interface complète avec toutes les colonnes
- **Tablet** : Adaptation des colonnes et boutons
- **Mobile** : Interface simplifiée, boutons empilés

## 🔒 Sécurité

- **Authentification** : Seuls les admins peuvent accéder
- **Autorisation** : Vérification des rôles côté backend
- **Validation** : Contrôles de saisie et d'état

## 🚀 Prochaines améliorations

- [ ] Filtres avancés (date, statut, région)
- [ ] Export des données en CSV/PDF
- [ ] Notifications push en temps réel
- [ ] Carte interactive pour visualiser les affectations
- [ ] Historique des affectations
- [ ] Métriques de performance des infirmiers
