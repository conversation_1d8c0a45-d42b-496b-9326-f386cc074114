import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { interval, Subscription } from 'rxjs';

interface NursePosition {
  nurseName: string;
  patientName: string;
  latitude: number;
  longitude: number;
  accuracy: number;
  speed: number;
  heading: number;
  status: string;
  timestamp: string;
  appointmentId: number;
  nurseId: number;
}

@Component({
  selector: 'app-patient-tracking',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './patient-tracking.component.html',
  styleUrl: './patient-tracking.component.css'
})
export class PatientTrackingComponent implements OnInit, OnDestroy {
  appointmentId: number | null = null;
  nursePosition: NursePosition | null = null;
  isLoading: boolean = true;
  errorMessage: string = '';
  
  private refreshSubscription?: Subscription;
  // private map: any; // TODO: Ajouter la carte Leaflet plus tard

  constructor(private route: ActivatedRoute) {}

  ngOnInit(): void {
    // Récupérer l'ID du rendez-vous depuis l'URL
    this.route.params.subscribe(params => {
      this.appointmentId = +params['appointmentId'];
      if (this.appointmentId) {
        this.startTracking();
      }
    });
  }

  ngOnDestroy(): void {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }

  private startTracking(): void {
    console.log('🗺️ Starting patient tracking for appointment:', this.appointmentId);
    
    // Charger la position initiale
    this.loadNursePosition();
    
    // Actualiser toutes les 3 secondes pour un suivi plus temps réel
    this.refreshSubscription = interval(3000).subscribe(() => {
      this.loadNursePosition();
    });
  }

  private loadNursePosition(): void {
    if (!this.appointmentId) return;

    console.log(`🔍 Chargement position pour RDV ${this.appointmentId}...`);

    fetch(`/api/tracking/appointment/${this.appointmentId}/nurse-position`)
      .then(response => {
        console.log(`📡 Réponse API: ${response.status}`);
        if (response.ok) {
          return response.json();
        } else if (response.status === 404) {
          throw new Error('Aucune position temps réel disponible');
        } else {
          throw new Error(`Erreur API: ${response.status}`);
        }
      })
      .then((position: NursePosition) => {
        console.log('📍 Position de l\'infirmier reçue:', position);
        console.log('📍 Coordonnées:', position.latitude, position.longitude);
        console.log('📍 Timestamp:', position.timestamp);
        console.log('📍 Status:', position.status);

        this.nursePosition = position;
        this.isLoading = false;
        this.errorMessage = '';

        // Mettre à jour la carte si elle existe
        this.updateMap();
      })
      .catch(error => {
        console.error('❌ Erreur lors du chargement de la position:', error);
        this.isLoading = false;

        // Messages d'erreur plus spécifiques
        if (error.message.includes('Aucune position temps réel')) {
          this.errorMessage = 'L\'infirmier n\'a pas encore activé le partage de position temps réel.';
        } else if (error.message.includes('Position non disponible')) {
          this.errorMessage = 'Position de l\'infirmier temporairement indisponible.';
        } else {
          this.errorMessage = 'Impossible de récupérer la position de l\'infirmier.';
        }

        // Vérifier si le partage est activé dans la base de données
        this.checkLocationSharingStatus();
      });
  }

  // Vérifier l'état du partage dans la base de données
  private checkLocationSharingStatus(): void {
    if (!this.appointmentId) return;

    console.log(`🔍 Vérification état partage pour RDV ${this.appointmentId}...`);

    fetch(`/api/appointments/${this.appointmentId}`)
      .then(response => response.json())
      .then(appointment => {
        console.log('📋 Données RDV:', appointment);
        console.log('📍 LocationSharingEnabled:', appointment.locationSharingEnabled);
        console.log('📊 Status:', appointment.status);

        if (appointment.status === 'NURSE_ON_WAY' && !appointment.locationSharingEnabled) {
          this.errorMessage = 'L\'infirmier est en route mais n\'a pas activé le partage de position.';
        } else if (appointment.locationSharingEnabled && !this.nursePosition) {
          this.errorMessage = 'Le partage est activé mais aucune position temps réel n\'est disponible.';
        }
      })
      .catch(error => {
        console.error('❌ Erreur vérification état partage:', error);
      });
  }

  private updateMap(): void {
    if (!this.nursePosition) {
      console.log('🗺️ Aucune position à afficher sur la carte');
      return;
    }

    console.log('🗺️ Mise à jour de la carte avec position infirmier:');
    console.log(`📍 Latitude: ${this.nursePosition.latitude}`);
    console.log(`📍 Longitude: ${this.nursePosition.longitude}`);
    console.log(`📍 Précision: ${this.nursePosition.accuracy}m`);
    console.log(`📍 Vitesse: ${this.nursePosition.speed} km/h`);
    console.log(`📍 Dernière mise à jour: ${this.nursePosition.timestamp}`);

    // TODO: Intégrer avec Leaflet ou Google Maps
    // Pour l'instant, les coordonnées sont affichées dans l'interface
  }

  getStatusText(): string {
    if (!this.nursePosition) return '';
    
    switch (this.nursePosition.status) {
      case 'ON_WAY':
        return 'En route vers vous';
      case 'ARRIVED':
        return 'Arrivé chez vous';
      case 'IN_PROGRESS':
        return 'Prélèvement en cours';
      case 'COMPLETED':
        return 'Prélèvement terminé';
      default:
        return 'Statut inconnu';
    }
  }

  getStatusIcon(): string {
    if (!this.nursePosition) return '📍';
    
    switch (this.nursePosition.status) {
      case 'ON_WAY':
        return '🚗';
      case 'ARRIVED':
        return '🏠';
      case 'IN_PROGRESS':
        return '🩺';
      case 'COMPLETED':
        return '✅';
      default:
        return '📍';
    }
  }

  getEstimatedArrival(): string {
    if (!this.nursePosition || this.nursePosition.status !== 'ON_WAY') {
      return '';
    }
    
    // Calcul approximatif basé sur la vitesse
    const speed = this.nursePosition.speed || 30; // km/h par défaut
    const estimatedMinutes = Math.round((this.nursePosition.accuracy / 1000) / (speed / 60));
    
    if (estimatedMinutes < 1) {
      return 'Arrivée imminente';
    } else if (estimatedMinutes < 60) {
      return `Arrivée estimée dans ${estimatedMinutes} min`;
    } else {
      const hours = Math.floor(estimatedMinutes / 60);
      const minutes = estimatedMinutes % 60;
      return `Arrivée estimée dans ${hours}h ${minutes}min`;
    }
  }

  formatTimestamp(): string {
    if (!this.nursePosition) return '';
    
    const date = new Date(this.nursePosition.timestamp);
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }
}
