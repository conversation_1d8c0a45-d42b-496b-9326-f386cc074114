.patient-tracking-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.tracking-header {
  text-align: center;
  margin-bottom: 30px;
}

.tracking-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.subtitle {
  color: #7f8c8d;
  font-size: 16px;
}

/* États de chargement et d'erreur */
.loading-state, .error-state {
  text-align: center;
  padding: 40px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.error-note {
  color: #7f8c8d;
  font-style: italic;
}

/* Suivi actif */
.status-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.status-icon {
  font-size: 48px;
  margin-right: 20px;
}

.status-content h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
}

.status-text {
  font-size: 18px;
  margin: 0 0 10px 0;
  opacity: 0.9;
}

.estimated-arrival {
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
  font-weight: 500;
}

/* Grille de détails */
.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.detail-card {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  display: flex;
  align-items: center;
}

.detail-icon {
  font-size: 24px;
  margin-right: 15px;
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 12px;
  color: #7f8c8d;
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: 5px;
}

.detail-value {
  font-size: 16px;
  color: #2c3e50;
  font-weight: 500;
}

/* Carte */
.map-container {
  margin-bottom: 30px;
}

.map-placeholder {
  background: #ecf0f1;
  border: 2px dashed #bdc3c7;
  border-radius: 10px;
  padding: 40px;
  text-align: center;
  color: #7f8c8d;
}

.map-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.map-coordinates {
  font-family: monospace;
  font-size: 14px;
  margin-top: 10px;
}

/* Étapes de la mission */
.mission-steps h4 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.step {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 10px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.step.active {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.step.completed {
  background: #e8f5e8;
  border-left: 4px solid #4caf50;
}

.step-icon {
  font-size: 24px;
  margin-right: 15px;
  opacity: 0.6;
}

.step.active .step-icon,
.step.completed .step-icon {
  opacity: 1;
}

.step-content {
  display: flex;
  flex-direction: column;
}

.step-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.step-description {
  font-size: 14px;
  color: #7f8c8d;
}

/* Actualisation automatique */
.auto-refresh {
  text-align: center;
  margin-top: 30px;
}

.refresh-indicator {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  color: #7f8c8d;
  font-size: 14px;
}

.pulse {
  width: 12px;
  height: 12px;
  background: #27ae60;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(39, 174, 96, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .patient-tracking-container {
    padding: 15px;
  }
  
  .status-card {
    flex-direction: column;
    text-align: center;
  }
  
  .status-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
}
