<div class="patient-tracking-container">
  <div class="tracking-header">
    <h2>🩺 Suivi de votre infirmier</h2>
    <p class="subtitle">Su<PERSON>z l'avancement de votre infirmier en temps réel</p>
  </div>

  <!-- État de chargement -->
  <div *ngIf="isLoading" class="loading-state">
    <div class="spinner"></div>
    <p>Recherche de votre infirmier...</p>
  </div>

  <!-- Erreur -->
  <div *ngIf="errorMessage && !isLoading" class="error-state">
    <div class="error-icon">❌</div>
    <h3>Position non disponible</h3>
    <p>{{ errorMessage }}</p>
    <p class="error-note">L'infirmier n'a peut-être pas encore activé le partage de position.</p>
  </div>

  <!-- Suivi actif -->
  <div *ngIf="nursePosition && !isLoading" class="tracking-active">
    <!-- Statut principal -->
    <div class="status-card">
      <div class="status-icon">{{ getStatusIcon() }}</div>
      <div class="status-content">
        <h3>{{ nursePosition.nurseName }}</h3>
        <p class="status-text">{{ getStatusText() }}</p>
        <p class="estimated-arrival" *ngIf="getEstimatedArrival()">
          {{ getEstimatedArrival() }}
        </p>
      </div>
    </div>

    <!-- Informations détaillées -->
    <div class="details-grid">
      <div class="detail-card">
        <div class="detail-icon">🕒</div>
        <div class="detail-content">
          <span class="detail-label">Dernière mise à jour</span>
          <span class="detail-value">{{ formatTimestamp() }}</span>
        </div>
      </div>

      <div class="detail-card" *ngIf="nursePosition.speed > 0">
        <div class="detail-icon">🚗</div>
        <div class="detail-content">
          <span class="detail-label">Vitesse</span>
          <span class="detail-value">{{ nursePosition.speed | number:'1.0-0' }} km/h</span>
        </div>
      </div>

      <div class="detail-card">
        <div class="detail-icon">📍</div>
        <div class="detail-content">
          <span class="detail-label">Précision</span>
          <span class="detail-value">± {{ nursePosition.accuracy | number:'1.0-0' }}m</span>
        </div>
      </div>
    </div>

    <!-- Carte (placeholder) -->
    <div class="map-container">
      <div class="map-placeholder">
        <div class="map-icon">🗺️</div>
        <p>Carte en temps réel</p>
        <p class="map-coordinates">
          Position: {{ nursePosition.latitude | number:'1.4-4' }}, {{ nursePosition.longitude | number:'1.4-4' }}
        </p>
      </div>
    </div>

    <!-- Étapes de la mission -->
    <div class="mission-steps">
      <h4>Étapes de la mission</h4>
      <div class="steps-list">
        <div class="step" [class.active]="nursePosition.status === 'ON_WAY'" [class.completed]="nursePosition.status !== 'ON_WAY'">
          <div class="step-icon">🚗</div>
          <div class="step-content">
            <span class="step-title">En route</span>
            <span class="step-description">L'infirmier se dirige vers vous</span>
          </div>
        </div>

        <div class="step" [class.active]="nursePosition.status === 'ARRIVED'" [class.completed]="nursePosition.status === 'IN_PROGRESS' || nursePosition.status === 'COMPLETED'">
          <div class="step-icon">🏠</div>
          <div class="step-content">
            <span class="step-title">Arrivé</span>
            <span class="step-description">L'infirmier est arrivé chez vous</span>
          </div>
        </div>

        <div class="step" [class.active]="nursePosition.status === 'IN_PROGRESS'" [class.completed]="nursePosition.status === 'COMPLETED'">
          <div class="step-icon">🩺</div>
          <div class="step-content">
            <span class="step-title">Prélèvement</span>
            <span class="step-description">Prélèvement en cours</span>
          </div>
        </div>

        <div class="step" [class.active]="nursePosition.status === 'COMPLETED'">
          <div class="step-icon">✅</div>
          <div class="step-content">
            <span class="step-title">Terminé</span>
            <span class="step-description">Mission terminée avec succès</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Actualisation automatique -->
    <div class="auto-refresh">
      <div class="refresh-indicator">
        <div class="pulse"></div>
        <span>Actualisation automatique toutes les 3 secondes</span>
      </div>
    </div>
  </div>
</div>
