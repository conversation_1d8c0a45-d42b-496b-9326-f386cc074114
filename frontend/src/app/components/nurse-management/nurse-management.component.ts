import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatChipsModule } from '@angular/material/chips';
import { AdminService } from '../../services/admin.service';
import { AdminSecurityService } from '../../services/admin-security.service';
import { User, Role, AdminPermission } from '../../models/user.model';

@Component({
  selector: 'app-nurse-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatDialogModule,
    MatSnackBarModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatPaginatorModule,
    MatSortModule,
    MatChipsModule
  ],
  templateUrl: './nurse-management.component.html',
  styleUrls: ['./nurse-management.component.css']
})
export class NurseManagementComponent implements OnInit {
  nurses: User[] = [];
  filteredNurses: User[] = [];
  isLoading = false;
  searchTerm = '';
  selectedNurse: User | null = null;
  showForm = false;
  isEditing = false;

  // Form
  nurseForm: FormGroup;

  // Table columns
  displayedColumns: string[] = ['avatar', 'name', 'email', 'phone', 'status', 'availability', 'actions'];

  // Filters
  statusFilter = 'all';
  availabilityFilter = 'all';

  // Permissions
  AdminPermission = AdminPermission;

  constructor(
    private adminService: AdminService,
    private adminSecurityService: AdminSecurityService,
    private fb: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.nurseForm = this.createNurseForm();
  }

  ngOnInit(): void {
    this.loadNurses();
    this.logSecurityEvent('NURSE_MANAGEMENT_ACCESS', 'NURSE_MANAGEMENT');
  }

  private createNurseForm(): FormGroup {
    return this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^[0-9+\-\s()]+$/)]],
      username: ['', [Validators.required, Validators.minLength(3)]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      address: ['', [Validators.required]],
      enabled: [true],
      isAvailable: [true]
    }, { validators: this.passwordMatchValidator });
  }

  private passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    
    return null;
  }

  loadNurses(): void {
    this.isLoading = true;
    this.adminService.getAllNurses().subscribe({
      next: (nurses) => {
        this.nurses = nurses;
        this.applyFilters();
        this.isLoading = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading nurses:', error);
        this.snackBar.open('Erreur lors du chargement des infirmiers', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  applyFilters(): void {
    this.filteredNurses = this.nurses.filter(nurse => {
      const matchesSearch = !this.searchTerm || 
        nurse.firstName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        nurse.lastName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        nurse.email.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesStatus = this.statusFilter === 'all' || 
        (this.statusFilter === 'active' && nurse.enabled) ||
        (this.statusFilter === 'inactive' && !nurse.enabled);

      const matchesAvailability = this.availabilityFilter === 'all' ||
        (this.availabilityFilter === 'available' && nurse.isAvailable) ||
        (this.availabilityFilter === 'unavailable' && !nurse.isAvailable);

      return matchesSearch && matchesStatus && matchesAvailability;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onStatusFilterChange(): void {
    this.applyFilters();
  }

  onAvailabilityFilterChange(): void {
    this.applyFilters();
  }

  showAddForm(): void {
    if (!this.hasPermission(AdminPermission.CREATE_USERS)) {
      this.showPermissionError('créer des infirmiers');
      return;
    }

    this.isEditing = false;
    this.selectedNurse = null;
    this.nurseForm.reset();
    this.nurseForm.patchValue({
      enabled: true,
      isAvailable: true
    });
    this.showForm = true;
    this.logSecurityEvent('NURSE_ADD_FORM_OPENED', 'NURSE_MANAGEMENT');
  }

  editNurse(nurse: User): void {
    if (!this.hasPermission(AdminPermission.UPDATE_USERS)) {
      this.showPermissionError('modifier des infirmiers');
      return;
    }

    this.isEditing = true;
    this.selectedNurse = nurse;
    this.nurseForm.patchValue({
      firstName: nurse.firstName,
      lastName: nurse.lastName,
      email: nurse.email,
      phone: nurse.phone,
      username: nurse.username,
      address: nurse.address,
      enabled: nurse.enabled,
      isAvailable: nurse.isAvailable
    });
    
    // Remove password validation for editing
    this.nurseForm.get('password')?.clearValidators();
    this.nurseForm.get('confirmPassword')?.clearValidators();
    this.nurseForm.get('password')?.updateValueAndValidity();
    this.nurseForm.get('confirmPassword')?.updateValueAndValidity();
    
    this.showForm = true;
    this.logSecurityEvent('NURSE_EDIT_FORM_OPENED', 'NURSE_MANAGEMENT', { nurseId: nurse.id });
  }

  cancelForm(): void {
    this.showForm = false;
    this.selectedNurse = null;
    this.nurseForm.reset();
    this.isEditing = false;
  }

  saveNurse(): void {
    if (this.nurseForm.invalid) {
      this.markFormGroupTouched(this.nurseForm);
      return;
    }

    const formData = this.nurseForm.value;
    const nurseData = {
      ...formData,
      role: Role.NURSE
    };

    this.isLoading = true;

    if (this.isEditing && this.selectedNurse) {
      this.updateNurse(this.selectedNurse.id, nurseData);
    } else {
      this.createNurse(nurseData);
    }
  }

  private createNurse(nurseData: any): void {
    this.adminService.createNurse(nurseData).subscribe({
      next: (newNurse) => {
        this.nurses.push(newNurse);
        this.applyFilters();
        this.showForm = false;
        this.nurseForm.reset();
        this.isLoading = false;
        
        this.snackBar.open('Infirmier créé avec succès', 'Fermer', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        
        this.logSecurityEvent('NURSE_CREATED', 'NURSE_MANAGEMENT', { 
          nurseId: newNurse.id,
          nurseName: `${newNurse.firstName} ${newNurse.lastName}`
        });
        
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error creating nurse:', error);
        this.snackBar.open('Erreur lors de la création de l\'infirmier', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  private updateNurse(nurseId: number, nurseData: any): void {
    this.adminService.updateNurse(nurseId, nurseData).subscribe({
      next: (updatedNurse) => {
        const index = this.nurses.findIndex(n => n.id === nurseId);
        if (index !== -1) {
          this.nurses[index] = updatedNurse;
          this.applyFilters();
        }
        
        this.showForm = false;
        this.selectedNurse = null;
        this.isLoading = false;
        
        this.snackBar.open('Infirmier mis à jour avec succès', 'Fermer', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        
        this.logSecurityEvent('NURSE_UPDATED', 'NURSE_MANAGEMENT', { 
          nurseId: updatedNurse.id,
          nurseName: `${updatedNurse.firstName} ${updatedNurse.lastName}`
        });
        
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error updating nurse:', error);
        this.snackBar.open('Erreur lors de la mise à jour de l\'infirmier', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  deleteNurse(nurse: User): void {
    if (!this.hasPermission(AdminPermission.DELETE_USERS)) {
      this.showPermissionError('supprimer des infirmiers');
      return;
    }

    const confirmMessage = `Êtes-vous sûr de vouloir supprimer l'infirmier ${nurse.firstName} ${nurse.lastName} ?\n\nCette action est irréversible.`;
    
    if (confirm(confirmMessage)) {
      this.isLoading = true;
      
      this.adminService.deleteNurse(nurse.id).subscribe({
        next: () => {
          this.nurses = this.nurses.filter(n => n.id !== nurse.id);
          this.applyFilters();
          this.isLoading = false;
          
          this.snackBar.open('Infirmier supprimé avec succès', 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          
          this.logSecurityEvent('NURSE_DELETED', 'NURSE_MANAGEMENT', { 
            nurseId: nurse.id,
            nurseName: `${nurse.firstName} ${nurse.lastName}`
          });
          
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error deleting nurse:', error);
          this.snackBar.open('Erreur lors de la suppression de l\'infirmier', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
    }
  }

  toggleNurseStatus(nurse: User): void {
    if (!this.hasPermission(AdminPermission.UPDATE_USERS)) {
      this.showPermissionError('modifier le statut des infirmiers');
      return;
    }

    const newStatus = !nurse.enabled;
    const action = newStatus ? 'activer' : 'désactiver';
    
    if (confirm(`Voulez-vous ${action} l'infirmier ${nurse.firstName} ${nurse.lastName} ?`)) {
      this.adminService.updateNurse(nurse.id, { ...nurse, enabled: newStatus }).subscribe({
        next: (updatedNurse) => {
          const index = this.nurses.findIndex(n => n.id === nurse.id);
          if (index !== -1) {
            this.nurses[index] = updatedNurse;
            this.applyFilters();
          }
          
          this.snackBar.open(`Infirmier ${action} avec succès`, 'Fermer', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          
          this.logSecurityEvent('NURSE_STATUS_CHANGED', 'NURSE_MANAGEMENT', { 
            nurseId: nurse.id,
            nurseName: `${nurse.firstName} ${nurse.lastName}`,
            newStatus: newStatus
          });
          
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error updating nurse status:', error);
          this.snackBar.open(`Erreur lors de la modification du statut`, 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  // Utility methods
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  private hasPermission(permission: AdminPermission): boolean {
    return this.adminSecurityService.hasPermissionSync(permission);
  }

  private showPermissionError(action: string): void {
    this.snackBar.open(`Vous n'avez pas les permissions pour ${action}`, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  private logSecurityEvent(action: string, resource: string, details?: any): void {
    // Temporairement désactivé pour éviter les erreurs 404
    // this.adminSecurityService.logSecurityEvent(action, resource, details).subscribe();
    console.log('Security Event:', { action, resource, details });
  }

  // Getters for template
  get canCreateNurses(): boolean {
    return this.hasPermission(AdminPermission.CREATE_USERS);
  }

  get canUpdateNurses(): boolean {
    return this.hasPermission(AdminPermission.UPDATE_USERS);
  }

  get canDeleteNurses(): boolean {
    return this.hasPermission(AdminPermission.DELETE_USERS);
  }

  get canViewNurses(): boolean {
    return this.hasPermission(AdminPermission.VIEW_USERS);
  }

  getInitials(nurse: User): string {
    return `${nurse.firstName.charAt(0)}${nurse.lastName.charAt(0)}`.toUpperCase();
  }

  getStatusColor(nurse: User): string {
    return nurse.enabled ? 'success' : 'warn';
  }

  getAvailabilityColor(nurse: User): string {
    return nurse.isAvailable ? 'primary' : 'accent';
  }
}
