<div class="nurse-management-container">
  <div class="container">
    
    <!-- Header -->
    <div class="management-header">
      <div class="header-content">
        <h1 class="page-title">
          <span class="custom-icon">👩‍⚕️</span>
          Gestion des Infirmiers
        </h1>
        <p class="page-subtitle">
          Gérer l'équipe d'infirmiers : ajout, modification et suppression
        </p>
      </div>
      <div class="header-actions">
        <button 
          mat-raised-button 
          color="primary" 
          (click)="showAddForm()"
          [disabled]="!canCreateNurses"
          [title]="!canCreateNurses ? 'Permission requise: CREATE_USERS' : ''"
        >
          <mat-icon>add</mat-icon>
          Ajouter un infirmier
        </button>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="filters-section">
      <mat-card class="filters-card">
        <div class="filters-content">
          
          <!-- Search -->
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Rechercher un infirmier</mat-label>
            <input 
              matInput 
              [(ngModel)]="searchTerm" 
              (input)="onSearchChange()"
              placeholder="Nom, prénom ou email..."
            >
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <!-- Status Filter -->
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Statut</mat-label>
            <mat-select [(value)]="statusFilter" (selectionChange)="onStatusFilterChange()">
              <mat-option value="all">Tous</mat-option>
              <mat-option value="active">Actifs</mat-option>
              <mat-option value="inactive">Inactifs</mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Availability Filter -->
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Disponibilité</mat-label>
            <mat-select [(value)]="availabilityFilter" (selectionChange)="onAvailabilityFilterChange()">
              <mat-option value="all">Tous</mat-option>
              <mat-option value="available">Disponibles</mat-option>
              <mat-option value="unavailable">Indisponibles</mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Results count -->
          <div class="results-count">
            <span class="count-text">{{ filteredNurses.length }} infirmier(s) trouvé(s)</span>
          </div>

        </div>
      </mat-card>
    </div>

    <!-- Nurse Form -->
    <div *ngIf="showForm" class="form-section">
      <mat-card class="form-card">
        <mat-card-header>
          <mat-card-title>
            <span class="custom-icon">{{ isEditing ? '✏️' : '➕' }}</span>
            {{ isEditing ? 'Modifier l\'infirmier' : 'Ajouter un nouvel infirmier' }}
          </mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="nurseForm" (ngSubmit)="saveNurse()" class="nurse-form">
            
            <!-- Personal Information -->
            <div class="form-section-title">Informations personnelles</div>
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Prénom</mat-label>
                <input matInput formControlName="firstName" required>
                <mat-error *ngIf="nurseForm.get('firstName')?.hasError('required')">
                  Le prénom est requis
                </mat-error>
                <mat-error *ngIf="nurseForm.get('firstName')?.hasError('minlength')">
                  Le prénom doit contenir au moins 2 caractères
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Nom</mat-label>
                <input matInput formControlName="lastName" required>
                <mat-error *ngIf="nurseForm.get('lastName')?.hasError('required')">
                  Le nom est requis
                </mat-error>
                <mat-error *ngIf="nurseForm.get('lastName')?.hasError('minlength')">
                  Le nom doit contenir au moins 2 caractères
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Contact Information -->
            <div class="form-section-title">Informations de contact</div>
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Email</mat-label>
                <input matInput formControlName="email" type="email" required>
                <mat-error *ngIf="nurseForm.get('email')?.hasError('required')">
                  L'email est requis
                </mat-error>
                <mat-error *ngIf="nurseForm.get('email')?.hasError('email')">
                  Format d'email invalide
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Téléphone</mat-label>
                <input matInput formControlName="phone" required>
                <mat-error *ngIf="nurseForm.get('phone')?.hasError('required')">
                  Le téléphone est requis
                </mat-error>
                <mat-error *ngIf="nurseForm.get('phone')?.hasError('pattern')">
                  Format de téléphone invalide
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Address -->
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Adresse</mat-label>
                <textarea matInput formControlName="address" rows="2" required></textarea>
                <mat-error *ngIf="nurseForm.get('address')?.hasError('required')">
                  L'adresse est requise
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Account Information -->
            <div class="form-section-title">Informations de compte</div>
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Nom d'utilisateur</mat-label>
                <input matInput formControlName="username" required>
                <mat-error *ngIf="nurseForm.get('username')?.hasError('required')">
                  Le nom d'utilisateur est requis
                </mat-error>
                <mat-error *ngIf="nurseForm.get('username')?.hasError('minlength')">
                  Le nom d'utilisateur doit contenir au moins 3 caractères
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Password (only for new nurses) -->
            <div *ngIf="!isEditing" class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Mot de passe</mat-label>
                <input matInput formControlName="password" type="password" required>
                <mat-error *ngIf="nurseForm.get('password')?.hasError('required')">
                  Le mot de passe est requis
                </mat-error>
                <mat-error *ngIf="nurseForm.get('password')?.hasError('minlength')">
                  Le mot de passe doit contenir au moins 6 caractères
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Confirmer le mot de passe</mat-label>
                <input matInput formControlName="confirmPassword" type="password" required>
                <mat-error *ngIf="nurseForm.get('confirmPassword')?.hasError('required')">
                  La confirmation est requise
                </mat-error>
                <mat-error *ngIf="nurseForm.get('confirmPassword')?.hasError('passwordMismatch')">
                  Les mots de passe ne correspondent pas
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Status and Availability -->
            <div class="form-section-title">Statut</div>
            <div class="form-row checkboxes">
              <mat-checkbox formControlName="enabled">
                Compte actif
              </mat-checkbox>
              <mat-checkbox formControlName="isAvailable">
                Disponible pour les missions
              </mat-checkbox>
            </div>

          </form>
        </mat-card-content>

        <mat-card-actions class="form-actions">
          <button mat-button (click)="cancelForm()" [disabled]="isLoading">
            Annuler
          </button>
          <button 
            mat-raised-button 
            color="primary" 
            (click)="saveNurse()" 
            [disabled]="nurseForm.invalid || isLoading"
          >
            <span *ngIf="!isLoading">{{ isEditing ? 'Mettre à jour' : 'Créer' }}</span>
            <span *ngIf="isLoading">Enregistrement...</span>
          </button>
        </mat-card-actions>
      </mat-card>
    </div>

    <!-- Nurses List -->
    <div class="nurses-section">
      <mat-card class="nurses-card">
        <mat-card-header>
          <mat-card-title>Liste des infirmiers</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div *ngIf="isLoading" class="loading-state">
            <mat-icon class="loading-icon">hourglass_empty</mat-icon>
            <p>Chargement des infirmiers...</p>
          </div>

          <div *ngIf="!isLoading && filteredNurses.length === 0" class="empty-state">
            <mat-icon class="empty-icon">person_off</mat-icon>
            <h3>Aucun infirmier trouvé</h3>
            <p>{{ searchTerm ? 'Aucun infirmier ne correspond à votre recherche.' : 'Aucun infirmier enregistré.' }}</p>
            <button 
              *ngIf="!searchTerm && canCreateNurses" 
              mat-raised-button 
              color="primary" 
              (click)="showAddForm()"
            >
              Ajouter le premier infirmier
            </button>
          </div>

          <div *ngIf="!isLoading && filteredNurses.length > 0" class="nurses-grid">
            <div *ngFor="let nurse of filteredNurses" class="nurse-card">
              
              <!-- Nurse Avatar and Info -->
              <div class="nurse-header">
                <div class="nurse-avatar">
                  {{ getInitials(nurse) }}
                </div>
                <div class="nurse-info">
                  <h3 class="nurse-name">{{ nurse.firstName }} {{ nurse.lastName }}</h3>
                  <p class="nurse-email">{{ nurse.email }}</p>
                  <p class="nurse-phone">{{ nurse.phone }}</p>
                </div>
                <div class="nurse-badges">
                  <span 
                    class="status-badge" 
                    [class.active]="nurse.enabled"
                    [class.inactive]="!nurse.enabled"
                  >
                    {{ nurse.enabled ? 'Actif' : 'Inactif' }}
                  </span>
                  <span 
                    class="availability-badge"
                    [class.available]="nurse.isAvailable"
                    [class.unavailable]="!nurse.isAvailable"
                  >
                    {{ nurse.isAvailable ? 'Disponible' : 'Indisponible' }}
                  </span>
                </div>
              </div>

              <!-- Nurse Details -->
              <div class="nurse-details">
                <div class="detail-item">
                  <mat-icon>location_on</mat-icon>
                  <span>{{ nurse.address || 'Adresse non renseignée' }}</span>
                </div>
                <div class="detail-item">
                  <mat-icon>person</mat-icon>
                  <span>{{ nurse.username }}</span>
                </div>
              </div>

              <!-- Actions -->
              <div class="nurse-actions">
                <button 
                  mat-icon-button 
                  (click)="editNurse(nurse)"
                  [disabled]="!canUpdateNurses"
                  [title]="!canUpdateNurses ? 'Permission requise: UPDATE_USERS' : 'Modifier'"
                  color="primary"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                
                <button 
                  mat-icon-button 
                  (click)="toggleNurseStatus(nurse)"
                  [disabled]="!canUpdateNurses"
                  [title]="!canUpdateNurses ? 'Permission requise: UPDATE_USERS' : (nurse.enabled ? 'Désactiver' : 'Activer')"
                  [color]="nurse.enabled ? 'warn' : 'accent'"
                >
                  <mat-icon>{{ nurse.enabled ? 'block' : 'check_circle' }}</mat-icon>
                </button>
                
                <button 
                  mat-icon-button 
                  (click)="deleteNurse(nurse)"
                  [disabled]="!canDeleteNurses"
                  [title]="!canDeleteNurses ? 'Permission requise: DELETE_USERS' : 'Supprimer'"
                  color="warn"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </div>

            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

  </div>
</div>
