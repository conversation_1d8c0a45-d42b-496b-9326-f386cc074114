/* Variables CSS */
:host {
  --primary-blue: #2563eb;
  --secondary-green: #10b981;
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --error: #ef4444;
  --warning: #f59e0b;
  --success: #10b981;
  
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  
  --transition: 0.3s ease;
}

/* Container principal */
.nurse-management-container {
  min-height: calc(100vh - 80px);
  background-color: var(--neutral-50);
  padding: var(--spacing-6) 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
  background: white;
  padding: var(--spacing-6);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-blue);
  margin-bottom: var(--spacing-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.page-subtitle {
  font-size: 1.125rem;
  color: var(--neutral-600);
  margin: 0;
}

/* Filters Section */
.filters-section {
  margin-bottom: var(--spacing-6);
}

.filters-card {
  box-shadow: var(--shadow-sm);
}

.filters-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: var(--spacing-4);
  align-items: center;
  padding: var(--spacing-4);
}

.search-field {
  min-width: 300px;
}

.filter-field {
  min-width: 150px;
}

.results-count {
  display: flex;
  align-items: center;
  color: var(--neutral-600);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Form Section */
.form-section {
  margin-bottom: var(--spacing-8);
}

.form-card {
  box-shadow: var(--shadow-md);
}

.nurse-form {
  max-width: 800px;
}

.form-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--neutral-700);
  margin: var(--spacing-6) 0 var(--spacing-4) 0;
  padding-bottom: var(--spacing-2);
  border-bottom: 2px solid var(--neutral-200);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.form-row.checkboxes {
  display: flex;
  gap: var(--spacing-6);
  align-items: center;
}

.full-width {
  grid-column: 1 / -1;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding: var(--spacing-4) var(--spacing-6);
  background: var(--neutral-50);
  border-top: 1px solid var(--neutral-200);
}

/* Nurses Grid */
.nurses-section {
  margin-bottom: var(--spacing-6);
}

.nurses-card {
  box-shadow: var(--shadow-sm);
}

.nurses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--spacing-6);
  margin-top: var(--spacing-4);
}

/* Nurse Card */
.nurse-card {
  background: white;
  border: 2px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  transition: all var(--transition);
  position: relative;
}

.nurse-card:hover {
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.nurse-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.nurse-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-green));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.nurse-info {
  flex: 1;
}

.nurse-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--neutral-800);
  margin: 0 0 var(--spacing-2) 0;
}

.nurse-email,
.nurse-phone {
  font-size: 0.875rem;
  color: var(--neutral-600);
  margin: 0 0 var(--spacing-1) 0;
}

.nurse-badges {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  align-items: flex-end;
}

.status-badge,
.availability-badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background: #fee2e2;
  color: #991b1b;
}

.availability-badge.available {
  background: #dbeafe;
  color: #1e40af;
}

.availability-badge.unavailable {
  background: #fef3c7;
  color: #92400e;
}

/* Nurse Details */
.nurse-details {
  margin-bottom: var(--spacing-4);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
  font-size: 0.875rem;
  color: var(--neutral-600);
}

.detail-item mat-icon {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
  color: var(--neutral-500);
}

/* Actions */
.nurse-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-2);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--neutral-200);
}

/* States */
.loading-state,
.empty-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--neutral-500);
}

.loading-icon,
.empty-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--neutral-700);
  margin-bottom: var(--spacing-2);
}

.empty-state p {
  font-size: 1rem;
  margin-bottom: var(--spacing-6);
}

/* Responsive */
@media (max-width: 768px) {
  .management-header {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }

  .filters-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  .search-field,
  .filter-field {
    min-width: auto;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-row.checkboxes {
    flex-direction: column;
    align-items: flex-start;
  }

  .nurses-grid {
    grid-template-columns: 1fr;
  }

  .nurse-header {
    flex-direction: column;
    text-align: center;
  }

  .nurse-badges {
    align-items: center;
  }

  .nurse-actions {
    justify-content: center;
  }
}

/* Custom icon styles */
.custom-icon {
  font-size: 1.5rem;
  margin-right: var(--spacing-2);
}

/* Material overrides */
.mat-mdc-card-header {
  padding-bottom: var(--spacing-4);
}

.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-card-actions {
  padding: 0;
}

/* Animation for loading */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-icon {
  animation: spin 2s linear infinite;
}
