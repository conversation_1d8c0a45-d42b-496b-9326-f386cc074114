import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, ChangeDetectorRef, ChangeDetectionStrategy, Injectable } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule } from '@angular/forms';

import { Observable, from, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

export interface LocationResult {
  success: boolean;
  coordinates?: LocationCoordinates;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class GeolocationService {
  getCurrentPosition(): Observable<LocationResult> {
    if (!navigator.geolocation) {
      return of({
        success: false,
        error: 'La géolocalisation n\'est pas supportée par ce navigateur'
      });
    }

    const options: PositionOptions = {
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 60000
    };

    return from(
      new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, options);
      })
    ).pipe(
      map((position: GeolocationPosition) => ({
        success: true,
        coordinates: {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy
        }
      })),
      catchError((error: GeolocationPositionError) => {
        let errorMessage = 'Erreur de géolocalisation';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'L\'accès à la géolocalisation a été refusé';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Les informations de localisation ne sont pas disponibles';
            break;
          case error.TIMEOUT:
            errorMessage = 'La demande de géolocalisation a expiré';
            break;
        }

        return of({
          success: false,
          error: errorMessage
        });
      })
    );
  }
}

export interface MapLocation {
  latitude: number;
  longitude: number;
  address?: string;
}

@Component({
  selector: 'app-map-selector',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatFormFieldModule,
    MatInputModule
  ],
  template: `
    <div class="map-container">
      <div class="map-header">
        <h3>
          <span class="custom-icon">📍</span>
          Sélectionnez votre position
        </h3>
        <p *ngIf="!selectedLocation">Cliquez sur "Ma position" ou sur la carte pour définir l'emplacement du prélèvement</p>
        <p *ngIf="selectedLocation" class="position-confirmed">✅ Position sélectionnée avec succès</p>
      </div>

      <div class="map-controls">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Rechercher une adresse</mat-label>
          <input matInput [(ngModel)]="searchQuery" (keyup.enter)="searchAddress()" 
                 placeholder="Tapez une adresse...">
          <span class="custom-icon" matSuffix (click)="searchAddress()" style="cursor: pointer;">🔍</span>
        </mat-form-field>
        
        <button mat-raised-button color="primary" (click)="getCurrentLocation()" [disabled]="isLoadingLocation">
          <span class="custom-icon" *ngIf="!isLoadingLocation">📍</span>
          <mat-spinner *ngIf="isLoadingLocation" diameter="20"></mat-spinner>
          Ma position
        </button>
      </div>

      <div class="map-wrapper">
        <div #mapContainer class="leaflet-map"></div>

        <div class="location-info" *ngIf="selectedLocation">
          <div class="location-details">
            <span class="custom-icon">📌</span>
            <div class="location-text">
              <div class="coordinates">
                {{selectedLocation.latitude.toFixed(6)}}, {{selectedLocation.longitude.toFixed(6)}}
              </div>
              <div class="address" *ngIf="selectedLocation.address">
                {{selectedLocation.address}}
              </div>
            </div>
          </div>
          <button mat-button color="primary" (click)="confirmLocation()">
            <span class="custom-icon">✅</span>
            Confirmer cette position
          </button>
        </div>
      </div>


    </div>
  `,
  styles: [`
    .map-container {
      width: 100%;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .map-header {
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      text-align: center;
    }

    .map-header h3 {
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 1.3rem;
      font-weight: 600;
    }

    .map-header p {
      margin: 0;
      opacity: 0.9;
      font-size: 0.95rem;
    }

    .position-confirmed {
      color: #4caf50 !important;
      font-weight: 500 !important;
      opacity: 1 !important;
    }



    .map-controls {
      padding: 16px;
      display: flex;
      gap: 16px;
      align-items: center;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
    }

    .search-field {
      flex: 1;
      margin-bottom: 0;
    }

    .map-wrapper {
      position: relative;
    }

    .leaflet-map {
      height: 400px;
      width: 100%;
      border-radius: 8px;
      z-index: 1;
    }

    .map.loading::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255,255,255,0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .location-info {
      position: absolute;
      bottom: 16px;
      left: 16px;
      right: 16px;
      background: white;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 1000;
    }

    .location-details {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
    }

    .location-details .custom-icon {
      color: #667eea;
      font-size: 24px;
      margin-right: 8px;
    }

    .custom-icon {
      font-size: 1.2rem;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;
    }

    .location-text {
      flex: 1;
    }

    .coordinates {
      font-family: monospace;
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 4px;
    }

    .address {
      font-size: 0.95rem;
      color: #333;
      font-weight: 500;
    }

    @media (max-width: 768px) {
      .map-controls {
        flex-direction: column;
        align-items: stretch;
      }
      
      .search-field {
        margin-bottom: 16px;
      }
      
      .location-info {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
      }
      
      .location-details {
        justify-content: center;
      }
    }
  `]
})
export class MapSelectorComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('mapContainer', { static: true }) mapContainer!: ElementRef;
  @Input() initialLocation?: MapLocation;
  @Output() locationSelected = new EventEmitter<MapLocation>();

  // Propriétés Leaflet
  private map!: any; // L.Map
  private marker?: any; // L.Marker

  // Position par défaut : Tunis
  private defaultLocation = {
    latitude: 36.8065,
    longitude: 10.1815
  };

  selectedLocation?: MapLocation;
  searchQuery = '';
  isLoadingLocation = false;
  lastAccuracy?: number;

  constructor(
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.fixLeafletIcons();
  }

  ngAfterViewInit() {
    console.log('🗺️ Composant MapSelector initialisé');
    console.log('📦 Conteneur de carte:', this.mapContainer?.nativeElement);

    // Petit délai pour s'assurer que le DOM est prêt
    setTimeout(() => {
      this.initializeMap();

      // Si une position initiale est fournie, l'utiliser
      if (this.initialLocation) {
        console.log('📍 Position initiale fournie:', this.initialLocation);
        this.setLocation(this.initialLocation.latitude, this.initialLocation.longitude);
      } else {
        // Afficher la carte sans position sélectionnée
        console.log('🗺️ Carte initialisée - Cliquez sur "Ma position" ou sur la carte pour sélectionner votre emplacement');
      }
    }, 100);
  }

  ngOnDestroy() {
    if (this.map) {
      this.map.remove();
    }
  }

  private fixLeafletIcons() {
    // Fix pour les icônes par défaut de Leaflet depuis CDN
    const iconRetinaUrl = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png';
    const iconUrl = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png';
    const shadowUrl = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png';

    // Utilisation de L global depuis le CDN
    if (typeof (window as any).L !== 'undefined') {
      const L = (window as any).L;
      const iconDefault = L.icon({
        iconRetinaUrl,
        iconUrl,
        shadowUrl,
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        tooltipAnchor: [16, -28],
        shadowSize: [41, 41]
      });
      L.Marker.prototype.options.icon = iconDefault;
    }
  }

  private initializeMap() {
    if (typeof (window as any).L === 'undefined') {
      console.error('Leaflet n\'est pas chargé');
      return;
    }

    const L = (window as any).L;

    this.map = L.map(this.mapContainer.nativeElement).setView(
      [this.defaultLocation.latitude, this.defaultLocation.longitude],
      13  // Zoom sur Tunis
    );

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(this.map);

    // Événement de clic sur la carte
    this.map.on('click', (e: any) => {
      this.setLocation(e.latlng.lat, e.latlng.lng);
    });
  }

  getCurrentLocation() {
    this.isLoadingLocation = true;
    this.cdr.detectChanges();

    // Utiliser l'API de géolocalisation du navigateur avec Google Maps
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          this.isLoadingLocation = false;
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;
          const accuracy = position.coords.accuracy;

          console.log('📍 Position détectée avec Google Maps:', { lat, lng, accuracy });

          // Mettre à jour la position
          this.setLocation(lat, lng);
          this.lastAccuracy = accuracy;

          // Message de succès avec précision
          let message = '📍 Position détectée avec succès';
          if (accuracy <= 10) {
            message += ' (🎯 Très précise: ±' + Math.round(accuracy) + 'm)';
          } else if (accuracy <= 50) {
            message += ' (✅ Précise: ±' + Math.round(accuracy) + 'm)';
          } else if (accuracy <= 100) {
            message += ' (⚠️ Approximative: ±' + Math.round(accuracy) + 'm)';
          } else {
            message += ' (❌ Peu précise: ±' + Math.round(accuracy) + 'm)';
          }

          this.snackBar.open(message, 'Fermer', { duration: 3000 });
          this.cdr.detectChanges();
        },
        (error) => {
          this.isLoadingLocation = false;
          let errorMessage = 'Erreur de géolocalisation';

          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Accès à la géolocalisation refusé';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Position non disponible';
              break;
            case error.TIMEOUT:
              errorMessage = 'Délai de géolocalisation dépassé';
              break;
          }

          this.snackBar.open(errorMessage, 'Fermer', { duration: 5000 });
          this.cdr.detectChanges();
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 60000
        }
      );
    } else {
      this.isLoadingLocation = false;
      this.snackBar.open('Géolocalisation non supportée', 'Fermer', { duration: 5000 });
      this.cdr.detectChanges();
    }
  }



  async searchAddress() {
    if (!this.searchQuery.trim()) return;

    try {
      // Recherche locale simplifiée pour les villes tunisiennes
      const results = await this.searchAddressLocal(this.searchQuery);

      if (results.length > 0) {
        const result = results[0];
        this.setLocation(parseFloat(result.lat), parseFloat(result.lon));
        this.snackBar.open('Adresse trouvée', 'Fermer', { duration: 3000 });
      } else {
        this.snackBar.open('Aucune adresse trouvée', 'Fermer', { duration: 3000 });
      }
    } catch (error) {
      this.snackBar.open('Erreur lors de la recherche', 'Fermer', { duration: 3000 });
    }
  }

  // Méthode de recherche locale temporaire
  private async searchAddressLocal(query: string): Promise<any[]> {
    const tunisianCities = [
      { name: 'Tunis', lat: '36.8065', lon: '10.1815', display_name: 'Tunis, Tunisie' },
      { name: 'Sfax', lat: '34.7406', lon: '10.7603', display_name: 'Sfax, Tunisie' },
      { name: 'Sousse', lat: '35.8256', lon: '10.6369', display_name: 'Sousse, Tunisie' },
      { name: 'Kairouan', lat: '35.6781', lon: '10.0963', display_name: 'Kairouan, Tunisie' },
      { name: 'Bizerte', lat: '37.2744', lon: '9.8739', display_name: 'Bizerte, Tunisie' },
      { name: 'Gabès', lat: '33.8815', lon: '10.0982', display_name: 'Gabès, Tunisie' },
      { name: 'Ariana', lat: '36.8625', lon: '10.1956', display_name: 'Ariana, Tunisie' },
      { name: 'Monastir', lat: '35.7643', lon: '10.8113', display_name: 'Monastir, Tunisie' },
      { name: 'Nabeul', lat: '36.4561', lon: '10.7376', display_name: 'Nabeul, Tunisie' },
      { name: 'Kasserine', lat: '35.1674', lon: '8.8363', display_name: 'Kasserine, Tunisie' }
    ];

    return tunisianCities.filter(city =>
      city.name.toLowerCase().includes(query.toLowerCase()) ||
      query.toLowerCase().includes(city.name.toLowerCase())
    ).slice(0, 5);
  }

  // Méthode de géocodage inverse locale temporaire
  private async reverseGeocodeLocal(lat: number, lng: number): Promise<string> {
    // En développement local, retourner simplement les coordonnées
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    }

    // Essayer d'utiliser l'API Nominatim si possible
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'MediSample-App/1.0'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();
        return data.display_name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
      }
    } catch (error) {
      console.warn('Erreur lors du géocodage inverse:', error);
    }

    // Fallback : retourner les coordonnées
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
  }

  private async setLocation(lat: number, lng: number) {
    // Mettre à jour la carte
    this.map.setView([lat, lng], 15);

    // Supprimer l'ancien marqueur
    if (this.marker) {
      this.map.removeLayer(this.marker);
    }

    // Ajouter un nouveau marqueur
    const L = (window as any).L;
    this.marker = L.marker([lat, lng]).addTo(this.map);

    // Obtenir l'adresse (version simplifiée)
    const address = await this.reverseGeocodeLocal(lat, lng);

    // Mettre à jour la position sélectionnée
    this.selectedLocation = {
      latitude: lat,
      longitude: lng,
      address: address
    };

    console.log('📍 Position sélectionnée:', this.selectedLocation);
    this.cdr.detectChanges();
  }

  confirmLocation() {
    if (this.selectedLocation) {
      this.locationSelected.emit(this.selectedLocation);
      this.snackBar.open('Position confirmée', 'Fermer', { duration: 3000 });
    }
  }


}
