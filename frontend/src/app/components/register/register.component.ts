import { Component, ChangeDetectorRef } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../../services/auth.service';
import { UserRegistration, Role } from '../../models/user.model';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatSnackBarModule
  ],
  template: `
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h1 class="auth-title">Inscription</h1>
          <p class="auth-subtitle">Créez votre compte MediSample</p>
        </div>

        <form (ngSubmit)="onSubmit()" class="auth-form">
          <div class="form-row">
            <div class="form-group">
              <label for="firstName" class="form-label">Prénom</label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                [(ngModel)]="formData.firstName"
                class="form-input"
                placeholder="Jean"
                required
              >
            </div>
            <div class="form-group">
              <label for="lastName" class="form-label">Nom</label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                [(ngModel)]="formData.lastName"
                class="form-input"
                placeholder="Dupont"
                required
              >
            </div>
          </div>

          <div class="form-group">
            <label for="email" class="form-label">Adresse email</label>
            <input
              type="email"
              id="email"
              name="email"
              [(ngModel)]="formData.email"
              class="form-input"
              placeholder="<EMAIL>"
              required
            >
          </div>

          <div class="form-group">
            <label for="phone" class="form-label">Téléphone</label>
            <input
              type="tel"
              id="phone"
              name="phone"
              [(ngModel)]="formData.phone"
              class="form-input"
              placeholder="06 12 34 56 78"
            >
          </div>

          <div class="form-group">
            <label for="address" class="form-label">Adresse</label>
            <input
              type="text"
              id="address"
              name="address"
              [(ngModel)]="formData.address"
              class="form-input"
              placeholder="Votre adresse complète"
            >
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="password" class="form-label">Mot de passe</label>
              <input
                type="password"
                id="password"
                name="password"
                [(ngModel)]="formData.password"
                class="form-input"
                placeholder="••••••••"
                required
                minlength="6"
              >
            </div>
            <div class="form-group">
              <label for="confirmPassword" class="form-label">Confirmer</label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                [(ngModel)]="formData.confirmPassword"
                class="form-input"
                placeholder="••••••••"
                required
              >
            </div>
          </div>

          <div class="error-message" *ngIf="errorMessage">
            {{ errorMessage }}
          </div>

          <button type="submit" class="btn btn-primary btn-lg auth-submit" [disabled]="isLoading">
            <span *ngIf="!isLoading">Créer mon compte</span>
            <span *ngIf="isLoading">Création en cours...</span>
          </button>
        </form>

        <div class="auth-footer">
          <p>Déjà un compte ? 
            <a routerLink="/auth/login" class="auth-link">Se connecter</a>
          </p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .auth-container {
      min-height: calc(100vh - 80px);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px;
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    }

    .auth-card {
      background: white;
      border-radius: 16px;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      padding: 32px;
      width: 100%;
      max-width: 500px;
      margin: 16px 0;
    }

    .auth-header {
      text-align: center;
      margin-bottom: 32px;
    }

    .auth-title {
      font-size: 2rem;
      font-weight: 700;
      color: #1976d2;
      margin-bottom: 8px;
    }

    .auth-subtitle {
      color: #64748b;
      font-size: 1rem;
    }

    .auth-form {
      margin-bottom: 32px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-label {
      display: block;
      font-weight: 500;
      color: #374151;
      margin-bottom: 6px;
    }

    .form-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      font-size: 16px;
      transition: all 0.2s ease;
      box-sizing: border-box;
    }

    .form-input:focus {
      outline: none;
      border-color: #1976d2;
      box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
    }

    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.2s ease;
      border: 2px solid transparent;
      cursor: pointer;
      font-size: 14px;
    }

    .btn-lg {
      padding: 16px 32px;
      font-size: 16px;
    }

    .btn-primary {
      background: #1976d2;
      color: white;
      border-color: #1976d2;
    }

    .btn-primary:hover:not(:disabled) {
      background: #1565c0;
      transform: translateY(-1px);
    }

    .btn-primary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .auth-submit {
      width: 100%;
      margin-top: 16px;
    }

    .error-message {
      background-color: #fee2e2;
      color: #991b1b;
      padding: 12px;
      border-radius: 8px;
      font-size: 14px;
      margin-bottom: 16px;
      text-align: center;
    }

    .auth-footer {
      text-align: center;
      padding-top: 24px;
      border-top: 1px solid #e5e7eb;
    }

    .auth-link {
      color: #1976d2;
      text-decoration: none;
      font-weight: 500;
    }

    .auth-link:hover {
      text-decoration: underline;
    }

    @media (max-width: 768px) {
      .form-row {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .auth-card {
        padding: 24px;
      }
    }
  `]
})
export class RegisterComponent {
  formData = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    address: '',
    latitude: null as number | null,
    longitude: null as number | null
  };
  
  isLoading = false;
  errorMessage = '';

  constructor(
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {}

  onSubmit(): void {
    if (!this.isLoading && this.validateForm()) {
      this.isLoading = true;
      this.errorMessage = '';

      // Utiliser setTimeout pour éviter ExpressionChangedAfterItHasBeenCheckedError
      setTimeout(() => {
        this.cdr.detectChanges();
      });
      
      // Créer un username unique basé sur l'email (partie avant @)
      const emailPrefix = this.formData.email.split('@')[0];
      const timestamp = Date.now().toString().slice(-4); // 4 derniers chiffres du timestamp
      const uniqueUsername = `${emailPrefix}_${timestamp}`;

      // Créer l'objet de données pour l'inscription
      const registerData: UserRegistration = {
        username: uniqueUsername, // Username unique généré
        email: this.formData.email, // Email séparé
        password: this.formData.password,
        firstName: this.formData.firstName,
        lastName: this.formData.lastName,
        phone: this.formData.phone,
        role: Role.PATIENT, // Toujours PATIENT pour l'inscription publique
        address: this.formData.address || '',
        latitude: this.formData.latitude || 36.8065, // Tunis par défaut
        longitude: this.formData.longitude || 10.1815 // Tunis par défaut
      };

      // Debug: afficher les données envoyées
      console.log('Données envoyées au backend:', registerData);

      this.authService.register(registerData).subscribe({
        next: (response: any) => {
          // Utiliser setTimeout pour éviter l'erreur de détection des changements
          setTimeout(() => {
            this.isLoading = false;
            this.cdr.detectChanges();

            this.snackBar.open(response.message || 'Inscription réussie ! Vérifiez votre email.', 'Fermer', {
              duration: 5000,
              panelClass: ['success-snackbar']
            });

            // Rediriger vers la page de vérification OTP
            setTimeout(() => {
              this.router.navigate(['/verify-email'], {
                queryParams: { email: this.formData.email }
              });
            }, 2000);
          });
        },
        error: (error) => {
          setTimeout(() => {
            this.isLoading = false;
            console.error('Register error:', error);

            // Gestion spécifique des erreurs
            if (error.status === 400 && error.error?.message?.includes('duplicate key')) {
              this.errorMessage = 'Cette adresse email est déjà utilisée. Veuillez en choisir une autre.';
            } else if (error.status === 400) {
              this.errorMessage = error.error?.message || 'Données invalides. Veuillez vérifier vos informations.';
            } else {
              this.errorMessage = 'Erreur lors de la création du compte. Veuillez réessayer.';
            }

            this.cdr.detectChanges();
          });
        }
      });
    }
  }

  private validateForm(): boolean {
    // Validation des champs requis
    if (!this.formData.firstName || !this.formData.lastName || !this.formData.email || !this.formData.password) {
      setTimeout(() => {
        this.errorMessage = 'Veuillez remplir tous les champs obligatoires.';
        this.cdr.detectChanges();
      });
      return false;
    }

    // Validation de l'email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(this.formData.email)) {
      setTimeout(() => {
        this.errorMessage = 'Veuillez saisir une adresse email valide.';
        this.cdr.detectChanges();
      });
      return false;
    }

    // Validation du téléphone (optionnel mais si fourni, doit être valide)
    if (this.formData.phone && this.formData.phone.trim()) {
      const phoneRegex = /^[0-9+\-\s()]{8,15}$/;
      if (!phoneRegex.test(this.formData.phone.trim())) {
        setTimeout(() => {
          this.errorMessage = 'Veuillez saisir un numéro de téléphone valide.';
          this.cdr.detectChanges();
        });
        return false;
      }
    }

    // Validation du mot de passe
    if (this.formData.password.length < 6) {
      setTimeout(() => {
        this.errorMessage = 'Le mot de passe doit contenir au moins 6 caractères.';
        this.cdr.detectChanges();
      });
      return false;
    }

    // Validation de la confirmation du mot de passe
    if (this.formData.password !== this.formData.confirmPassword) {
      setTimeout(() => {
        this.errorMessage = 'Les mots de passe ne correspondent pas.';
        this.cdr.detectChanges();
      });
      return false;
    }

    return true;
  }
}
