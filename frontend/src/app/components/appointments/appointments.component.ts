import { Component, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatTableModule } from '@angular/material/table';
import { MatDialogModule } from '@angular/material/dialog';
import { RouterModule } from '@angular/router';
import { Subscription, interval } from 'rxjs';
import { AppointmentService } from '../../services/appointment.service';
import { ResultsService, AppointmentResult } from '../../services/results.service';
import { Appointment, AppointmentStatus, AppointmentStatusLabels } from '../../models/appointment.model';
import { NurseRouteMapComponent } from '../nurse-route-map/nurse-route-map.component';
import { MatDialog } from '@angular/material/dialog';


@Component({
  selector: 'app-appointments',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatTableModule,
    MatDialogModule
  ],
  templateUrl: './appointments.component.html',
  styleUrls: ['./appointments.component.css']
})
export class AppointmentsComponent implements OnInit, OnDestroy {
  appointments: Appointment[] = [];
  isLoading = false;
  currentUser: any = null;

  // Stats properties (calculated once when data changes)
  pendingCount = 0;
  completedCount = 0;
  urgentCount = 0;

  // Auto-refresh subscription
  private refreshSubscription?: Subscription;
  private readonly REFRESH_INTERVAL = 10000; // 10 seconds

  constructor(
    private appointmentService: AppointmentService,
    private resultsService: ResultsService,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadAppointments();
    this.loadCurrentUser();
    this.startAutoRefresh();
  }

  ngOnDestroy(): void {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }

  private loadCurrentUser(): void {
    // TODO: Load current user from auth service
    this.currentUser = { firstName: 'Patient' };
  }

  loadAppointments(): void {
    this.isLoading = true;
    this.appointmentService.getMyAppointments().subscribe({
      next: (appointments: Appointment[]) => {
        console.log('📥 Patient dashboard: Loaded appointments:', appointments);

        // Log location sharing status for debugging
        appointments.forEach(app => {
          console.log(`📋 Appointment ${app.id}: status=${app.status}, locationSharingEnabled=${app.locationSharingEnabled}`);
        });

        this.appointments = appointments;
        this.calculateStats();
        this.isLoading = false;
        this.cdr.detectChanges();
      },
      error: (error: any) => {
        console.error('Error loading appointments:', error);
        this.isLoading = false;
        this.cdr.detectChanges();
      }
    });
  }

  private startAutoRefresh(): void {
    console.log('🔄 Starting auto-refresh for patient dashboard');

    // Refresh every 10 seconds to get real-time updates
    this.refreshSubscription = interval(this.REFRESH_INTERVAL).subscribe(() => {
      console.log('🔄 Auto-refreshing patient appointments...');
      this.loadAppointments();
    });
  }

  private calculateStats(): void {
    // Utiliser setTimeout pour éviter les erreurs de détection de changement
    setTimeout(() => {
      this.pendingCount = this.appointments.filter(app =>
        app.status === AppointmentStatus.PENDING ||
        app.status === AppointmentStatus.CONFIRMED ||
        app.status === AppointmentStatus.NURSE_ASSIGNED ||
        app.status === AppointmentStatus.NURSE_ON_WAY ||
        app.status === AppointmentStatus.IN_PROGRESS
      ).length;

      this.completedCount = this.appointments.filter(app =>
        app.status === AppointmentStatus.SAMPLING_DONE ||
        app.status === AppointmentStatus.RESULTS_AVAILABLE ||
        app.status === AppointmentStatus.COMPLETED
      ).length;

      this.urgentCount = this.appointments.filter(app => app.isUrgent).length;

      // Forcer la détection de changement
      this.cdr.detectChanges();
    }, 0);
  }

  // Méthodes pour le template
  formatDateTime(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatDate(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('fr-FR');
  }

  getStatusBadgeClass(status: AppointmentStatus): string {
    return `status-${status.toLowerCase().replace('_', '-')}`;
  }

  getStatusLabel(status: AppointmentStatus): string {
    return AppointmentStatusLabels[status] || status;
  }

  getProgressPercentage(status: AppointmentStatus): number {
    const statusOrder: { [key in AppointmentStatus]: number } = {
      [AppointmentStatus.PENDING]: 20,           // Étape 1: Demandé
      [AppointmentStatus.CONFIRMED]: 20,         // Étape 1: Demandé
      [AppointmentStatus.NURSE_ASSIGNED]: 40,    // Étape 2: Assigné
      [AppointmentStatus.NURSE_ON_WAY]: 40,      // Étape 2: Assigné (en route)
      [AppointmentStatus.IN_PROGRESS]: 60,       // Étape 3: Prélèvement
      [AppointmentStatus.SAMPLING_DONE]: 80,     // Étape 4: Analyse
      [AppointmentStatus.ANALYSIS_IN_PROGRESS]: 80, // Étape 4: Analyse
      [AppointmentStatus.RESULTS_AVAILABLE]: 100,   // Étape 5: Résultats
      [AppointmentStatus.COMPLETED]: 100,        // Étape 5: Résultats
      [AppointmentStatus.CANCELLED]: 0
    };
    return statusOrder[status] || 0;
  }

  getStepStatus(status: AppointmentStatus, stepNumber: number): boolean {
    const statusSteps: { [key in AppointmentStatus]: number } = {
      [AppointmentStatus.PENDING]: 1,           // Étape 1: Demandé
      [AppointmentStatus.CONFIRMED]: 1,         // Étape 1: Demandé
      [AppointmentStatus.NURSE_ASSIGNED]: 2,    // Étape 2: Assigné
      [AppointmentStatus.NURSE_ON_WAY]: 2,      // Étape 2: Assigné (en route)
      [AppointmentStatus.IN_PROGRESS]: 3,       // Étape 3: Prélèvement
      [AppointmentStatus.SAMPLING_DONE]: 4,     // Étape 4: Analyse
      [AppointmentStatus.ANALYSIS_IN_PROGRESS]: 4, // Étape 4: Analyse
      [AppointmentStatus.RESULTS_AVAILABLE]: 5,    // Étape 5: Résultats
      [AppointmentStatus.COMPLETED]: 5,         // Étape 5: Résultats
      [AppointmentStatus.CANCELLED]: 0
    };
    return (statusSteps[status] || 0) >= stepNumber;
  }

  getEstimatedArrival(appointment: Appointment): string {
    try {
      // Vérifier si l'infirmier partage sa position
      if (!this.isNurseLocationShared(appointment)) {
        return '';
      }

      // Vérifier si le rendez-vous a un infirmier assigné
      if (!appointment.nurse || !appointment.nurse.id) {
        return '';
      }

      // Vérifier si le statut permet le calcul d'ETA
      if (appointment.status !== 'NURSE_ON_WAY' && appointment.status !== 'IN_PROGRESS') {
        return '';
      }

      // Récupérer la position de l'infirmier depuis le rendez-vous
      const nurseLat = (appointment as any).nurseCurrentLatitude;
      const nurseLng = (appointment as any).nurseCurrentLongitude;

      if (!nurseLat || !nurseLng) {
        // Essayer de récupérer la position en temps réel seulement pour les RDV de test
        const testAppointments = [48, 50, 89];
        if (testAppointments.includes(appointment.id)) {
          this.fetchRealTimeNursePosition(appointment);
          return 'Calcul en cours...';
        } else {
          return ''; // Pas de position disponible
        }
      }

      // Coordonnées du patient (depuis le rendez-vous)
      const patientLat = appointment.latitude;
      const patientLng = appointment.longitude;

      if (!patientLat || !patientLng) {
        return 'Position patient inconnue';
      }

      // Calculer la distance en utilisant la formule de Haversine
      const distance = this.calculateDistance(nurseLat, nurseLng, patientLat, patientLng);

      // Vitesse par défaut (on peut l'améliorer plus tard avec la vraie vitesse)
      const speed = (appointment as any).nurseSpeed || 30; // 30 km/h par défaut

      // Calculer le temps estimé
      const estimatedTime = this.calculateEstimatedTime(distance, speed);

      return estimatedTime;

    } catch (error) {
      console.error('❌ Erreur calcul ETA:', error);
      return '';
    }
  }

  // Cache pour éviter les appels API répétés
  private positionFetchCache = new Map<number, number>();
  private readonly CACHE_DURATION = 5000; // 5 secondes

  /**
   * Récupère la position temps réel de l'infirmier depuis l'API
   */
  private fetchRealTimeNursePosition(appointment: Appointment): void {
    if (!appointment.id) return;

    // Vérifier le cache pour éviter les appels répétés
    const now = Date.now();
    const lastFetch = this.positionFetchCache.get(appointment.id);
    if (lastFetch && (now - lastFetch) < this.CACHE_DURATION) {
      return; // Trop récent, ne pas refaire l'appel
    }

    // Marquer comme en cours de récupération
    this.positionFetchCache.set(appointment.id, now);

    fetch(`/api/tracking/appointment/${appointment.id}/nurse-position`)
      .then(response => {
        if (response.ok) {
          return response.json();
        }
        throw new Error(`HTTP ${response.status}`);
      })
      .then(nursePosition => {
        console.log('📍 Position temps réel récupérée pour RDV', appointment.id, ':', nursePosition);

        // Mettre à jour les coordonnées dans l'appointment
        (appointment as any).nurseCurrentLatitude = nursePosition.latitude;
        (appointment as any).nurseCurrentLongitude = nursePosition.longitude;
        (appointment as any).nurseSpeed = nursePosition.speed || 30;

        // Déclencher une nouvelle détection de changement pour mettre à jour l'affichage
        this.cdr.detectChanges();
      })
      .catch(error => {
        console.warn('⚠️ Impossible de récupérer la position temps réel pour RDV', appointment.id, ':', error);
        // Supprimer du cache en cas d'erreur pour permettre un nouvel essai
        this.positionFetchCache.delete(appointment.id);
      });
  }

  /**
   * Calcule la distance entre deux points géographiques (formule de Haversine)
   * @param lat1 Latitude point 1
   * @param lng1 Longitude point 1
   * @param lat2 Latitude point 2
   * @param lng2 Longitude point 2
   * @returns Distance en kilomètres
   */
  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371; // Rayon de la Terre en kilomètres
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);

    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    return distance;
  }

  /**
   * Convertit les degrés en radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Calcule le temps estimé d'arrivée basé sur la distance et la vitesse
   * @param distance Distance en kilomètres
   * @param speed Vitesse en km/h
   * @returns Temps estimé formaté
   */
  private calculateEstimatedTime(distance: number, speed: number): string {
    // Si la distance est très petite (moins de 100m), l'infirmier est arrivé
    if (distance < 0.1) {
      return 'Arrivé';
    }

    // Vitesse par défaut si pas de vitesse ou vitesse trop faible
    let effectiveSpeed = speed;
    if (speed < 5) {
      // Vitesse de marche par défaut : 5 km/h
      effectiveSpeed = 5;
    } else if (speed > 80) {
      // Limiter la vitesse max à 80 km/h pour des calculs réalistes
      effectiveSpeed = 80;
    }

    // Calculer le temps en heures
    const timeInHours = distance / effectiveSpeed;

    // Convertir en minutes
    const timeInMinutes = Math.round(timeInHours * 60);

    // Formater le résultat
    if (timeInMinutes < 1) {
      return 'Moins d\'1 min';
    } else if (timeInMinutes < 60) {
      return `${timeInMinutes} min`;
    } else {
      const hours = Math.floor(timeInMinutes / 60);
      const minutes = timeInMinutes % 60;
      return minutes > 0 ? `${hours}h ${minutes}min` : `${hours}h`;
    }
  }

  // Ouvrir la carte de position de l'infirmier
  openNursePositionMap(appointment: Appointment): void {
    console.log('🗺️ Ouverture de la carte de position pour RDV:', appointment.id);

    const dialogRef = this.dialog.open(NurseRouteMapComponent, {
      width: '90vw',
      maxWidth: '900px',
      height: '80vh',
      maxHeight: '700px',
      data: {
        appointmentId: appointment.id,
        patientLocation: {
          latitude: appointment.latitude || 36.8065,
          longitude: appointment.longitude || 10.1815
        }
      },
      panelClass: 'nurse-route-dialog'
    });

    // Passer les données au composant
    const componentInstance = dialogRef.componentInstance;
    componentInstance.appointmentId = appointment.id;
    componentInstance.patientLocation = {
      latitude: appointment.latitude || 36.8065,
      longitude: appointment.longitude || 10.1815
    };

    dialogRef.afterClosed().subscribe(() => {
      console.log('🗺️ Carte fermée');
    });
  }

  // Vérifier si l'infirmier partage sa position
  isNurseLocationShared(appointment: Appointment): boolean {
    // Vérifications de base
    if (!appointment.nurse || !appointment.nurse.id) {
      return false;
    }

    // Vérifier le statut
    const isOnWay = appointment.status === 'NURSE_ON_WAY' || appointment.status === 'IN_PROGRESS';
    if (!isOnWay) {
      return false;
    }

    // Vérifier si le partage est activé par différents moyens
    const locationShared = (appointment as any).locationSharingEnabled === true ||
                           (appointment as any).nurseLocationSharing === true ||
                           (appointment as any).trackingStarted === true ||
                           (appointment as any).locationTrackingActive === true;

    // Pour les tests, considérer que les RDV 48, 50, 89 ont le partage activé
    const testAppointments = [48, 50, 89];
    const isTestAppointment = testAppointments.includes(appointment.id);

    const result = locationShared || isTestAppointment;
    console.log('🔍 Partage position RDV', appointment.id, '- Status:', appointment.status, 'Shared:', result);
    return result;
  }

  // Vérifier si on doit afficher le message "pas de partage"
  shouldShowNoLocationMessage(appointment: Appointment): boolean {
    const isOnWay = appointment.status === 'NURSE_ON_WAY' || appointment.status === 'IN_PROGRESS';
    const hasNurse = !!appointment.nurse;
    const locationShared = this.isNurseLocationShared(appointment);

    return isOnWay && hasNurse && !locationShared;
  }

 

  canCancel(appointment: Appointment): boolean {
    return appointment.status === AppointmentStatus.PENDING ||
           appointment.status === AppointmentStatus.CONFIRMED;
  }

  canReschedule(appointment: Appointment): boolean {
    return appointment.status === AppointmentStatus.PENDING ||
           appointment.status === AppointmentStatus.CONFIRMED;
  }

  // Méthodes d'action
  trackNurse(appointment: Appointment): void {
    console.log('🗺️ Tracking nurse for appointment:', appointment.id);

    // Vérifier si le partage de position est actif
    if (!appointment.locationSharingEnabled) {
      alert('Le partage de position n\'est pas activé pour ce rendez-vous.');
      return;
    }

    // Vérifier si nous avons la position de l'infirmier
    if (!appointment.nurseCurrentLatitude || !appointment.nurseCurrentLongitude) {
      alert('Position de l\'infirmier non disponible. L\'infirmier n\'a peut-être pas encore partagé sa position.');
      return;
    }

    // Vérifier si nous avons l'adresse du patient
    if (!appointment.latitude || !appointment.longitude) {
      alert('Adresse du patient non disponible.');
      return;
    }

    // Créer l'URL pour Google Maps avec l'itinéraire
    const nursePosition = `${appointment.nurseCurrentLatitude},${appointment.nurseCurrentLongitude}`;
    const patientPosition = `${appointment.latitude},${appointment.longitude}`;

    // URL Google Maps pour afficher l'itinéraire de l'infirmier vers le patient
    const mapsUrl = `https://www.google.com/maps/dir/${nursePosition}/${patientPosition}`;

    console.log('🗺️ Opening Google Maps with route:');
    console.log('📍 Nurse position:', nursePosition);
    console.log('🏠 Patient address:', patientPosition);
    console.log('🔗 Maps URL:', mapsUrl);

    // Ouvrir Google Maps dans un nouvel onglet
    window.open(mapsUrl, '_blank');
  }

  // Méthode pour afficher seulement la position de l'infirmier
  showNurseLocation(appointment: Appointment): void {
    console.log('📍 Showing nurse location for appointment:', appointment.id);

    if (!appointment.locationSharingEnabled) {
      alert('Le partage de position n\'est pas activé pour ce rendez-vous.');
      return;
    }

    if (!appointment.nurseCurrentLatitude || !appointment.nurseCurrentLongitude) {
      alert('Position de l\'infirmier non disponible.');
      return;
    }

    // URL Google Maps pour afficher seulement la position de l'infirmier
    const nursePosition = `${appointment.nurseCurrentLatitude},${appointment.nurseCurrentLongitude}`;
    const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${nursePosition}`;

    console.log('📍 Opening nurse location on Google Maps:', mapsUrl);
    window.open(mapsUrl, '_blank');
  }

  // Méthode pour calculer la distance entre l'infirmier et le patient
  calculateDistanceToNurse(appointment: Appointment): string {
    if (!appointment.nurseCurrentLatitude || !appointment.nurseCurrentLongitude ||
        !appointment.latitude || !appointment.longitude) {
      return 'Distance inconnue';
    }

    const distance = this.calculateDistance(
      appointment.nurseCurrentLatitude,
      appointment.nurseCurrentLongitude,
      appointment.latitude,
      appointment.longitude
    );

    if (distance < 0.1) {
      return `🎯 Très proche (${Math.round(distance * 1000)} m)`;
    } else if (distance < 1) {
      return `${Math.round(distance * 1000)} m`;
    } else {
      return `${distance.toFixed(1)} km`;
    }
  }

  // Méthode pour vérifier si l'infirmier est proche
  isNurseNearby(appointment: Appointment): boolean {
    if (!appointment.nurseCurrentLatitude || !appointment.nurseCurrentLongitude ||
        !appointment.latitude || !appointment.longitude) {
      return false;
    }

    const distance = this.calculateDistance(
      appointment.nurseCurrentLatitude,
      appointment.nurseCurrentLongitude,
      appointment.latitude,
      appointment.longitude
    );

    return distance < 0.5; // Moins de 500 mètres
  }



  // Méthode pour formater l'heure de dernière mise à jour
  formatTime(dateTime: Date | string): string {
    if (!dateTime) return 'Inconnue';

    const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime;
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 1) {
      return 'À l\'instant';
    } else if (diffMinutes < 60) {
      return `Il y a ${diffMinutes} min`;
    } else {
      const hours = Math.floor(diffMinutes / 60);
      return `Il y a ${hours}h${diffMinutes % 60 > 0 ? ` ${diffMinutes % 60}min` : ''}`;
    }
  }

  downloadResults(appointment: Appointment): void {
    console.log('📥 Downloading results for appointment:', appointment.id);

    this.resultsService.getAppointmentResults(appointment.id).subscribe({
      next: (result: AppointmentResult) => {
        if (result.files && result.files.length > 0) {
          // Si un seul fichier, télécharger directement
          if (result.files.length === 1) {
            this.downloadSingleFile(result.files[0]);
          } else {
            // Si plusieurs fichiers, proposer le choix
            this.showDownloadOptions(result.files);
          }
        } else {
          alert('Aucun fichier de résultat disponible.');
        }
      },
      error: (error) => {
        console.error('❌ Error loading results:', error);
        alert('Erreur lors du chargement des résultats. Veuillez réessayer.');
      }
    });
  }

  viewResults(appointment: Appointment): void {
    console.log('👁️ Viewing results for appointment:', appointment.id);

    this.resultsService.getAppointmentResults(appointment.id).subscribe({
      next: (result: AppointmentResult) => {
        this.showResultsModal(appointment, result);
      },
      error: (error) => {
        console.error('❌ Error loading results:', error);
        alert('Erreur lors du chargement des résultats. Veuillez réessayer.');
      }
    });
  }

  private downloadSingleFile(file: any): void {
    this.resultsService.downloadResultFile(file.id, file.fileName).subscribe({
      next: (blob: Blob) => {
        this.resultsService.triggerDownload(blob, file.fileName);
        console.log('✅ File downloaded:', file.fileName);
      },
      error: (error) => {
        console.error('❌ Error downloading file:', error);
        alert('Erreur lors du téléchargement. Veuillez réessayer.');
      }
    });
  }

  private showDownloadOptions(files: any[]): void {
    const fileNames = files.map(f => f.fileName).join('\n');
    const choice = confirm(`Plusieurs fichiers disponibles :\n\n${fileNames}\n\nVoulez-vous tous les télécharger ?`);

    if (choice) {
      files.forEach(file => this.downloadSingleFile(file));
    }
  }

  private showResultsModal(appointment: Appointment, result: AppointmentResult): void {
    // Créer une fenêtre modale pour afficher les résultats
    const modalContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
          📊 Résultats du rendez-vous #${appointment.id}
        </h2>

        <div style="background: #f9fafb; padding: 15px; border-radius: 8px; margin: 15px 0;">
          <h3 style="margin-top: 0; color: #374151;">Informations générales</h3>
          <p><strong>Date du prélèvement :</strong> ${this.formatDate(appointment.scheduledDate)}</p>
          <p><strong>Infirmier(e) :</strong> ${result.nurseName}</p>
          <p><strong>Date de dépôt :</strong> ${this.formatDate(result.uploadDate)}</p>
        </div>

        <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin: 15px 0;">
          <h3 style="margin-top: 0; color: #0369a1;">Commentaires de l'infirmier</h3>
          <p style="line-height: 1.6;">${result.comments || 'Aucun commentaire particulier.'}</p>
        </div>

        <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 15px 0;">
          <h3 style="margin-top: 0; color: #059669;">Fichiers de résultats (${result.files.length})</h3>
          ${result.files.map(file => `
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
              <div>
                <strong>${file.fileName}</strong><br>
                <small style="color: #6b7280;">${this.resultsService.formatFileSize(file.fileSize)} • ${this.formatDate(file.uploadDate)}</small>
              </div>
              <button onclick="window.downloadFile(${file.id}, '${file.fileName}')"
                      style="background: #10b981; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
                📥 Télécharger
              </button>
            </div>
          `).join('')}
        </div>

        <div style="text-align: center; margin-top: 20px;">
          <button onclick="window.close()"
                  style="background: #6b7280; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin-right: 10px;">
            Fermer
          </button>
          <button onclick="window.downloadAllFiles()"
                  style="background: #2563eb; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer;">
            📥 Tout télécharger
          </button>
        </div>
      </div>
    `;

    const newWindow = window.open('', '_blank', 'width=700,height=600,scrollbars=yes,resizable=yes');
    if (newWindow) {
      newWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Résultats - Rendez-vous #${appointment.id}</title>
          <meta charset="utf-8">
        </head>
        <body>
          ${modalContent}
          <script>
            window.downloadFile = function(fileId, fileName) {
              window.opener.postMessage({action: 'downloadFile', fileId: fileId, fileName: fileName}, '*');
            };
            window.downloadAllFiles = function() {
              window.opener.postMessage({action: 'downloadAllFiles'}, '*');
            };
          </script>
        </body>
        </html>
      `);
      newWindow.document.close();

      // Écouter les messages de la fenêtre popup
      window.addEventListener('message', (event) => {
        if (event.data.action === 'downloadFile') {
          const file = result.files.find(f => f.id === event.data.fileId);
          if (file) {
            this.downloadSingleFile(file);
          }
        } else if (event.data.action === 'downloadAllFiles') {
          result.files.forEach(file => this.downloadSingleFile(file));
        }
      });
    }
  }

  cancelAppointment(_appointment: Appointment): void {
    if (confirm('Êtes-vous sûr de vouloir annuler ce rendez-vous ?')) {
      alert('Fonctionnalité d\'annulation en cours de développement');
    }
  }

  rescheduleAppointment(_appointment: Appointment): void {
    alert('Fonctionnalité de reprogrammation en cours de développement');
  }

  contactSupport(_appointment: Appointment): void {
    alert('Fonctionnalité de support en cours de développement');
  }
}
