<div class="patient-dashboard-container">
  <div class="container">
    <!-- Header -->
    <div class="dashboard-header">
      <div class="welcome-section">
        <h1 class="dashboard-title">Tableau de bord</h1>
        <p class="dashboard-subtitle">
          Bonjour {{ currentUser?.firstName }}, suivez vos prélèvements en temps réel
        </p>
      </div>
      <div class="quick-actions">
        <button class="btn btn-outline btn-sm" (click)="loadAppointments()" [disabled]="isLoading">
          🔄 {{ isLoading ? 'Actualisation...' : 'Actualiser' }}
        </button>
        <a routerLink="/dashboard/new-appointment" class="btn btn-primary">
          📅 Nouveau prélèvement
        </a>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">📋</div>
        <div class="stat-content">
          <div class="stat-number">{{ appointments.length }}</div>
          <div class="stat-label">Total rendez-vous</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">⏳</div>
        <div class="stat-content">
          <div class="stat-number">{{ pendingCount }}</div>
          <div class="stat-label">En cours</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <div class="stat-number">{{ completedCount }}</div>
          <div class="stat-label">Terminés</div>
        </div>
      </div>
      <div class="stat-card urgent" *ngIf="urgentCount > 0">
        <div class="stat-icon">🚨</div>
        <div class="stat-content">
          <div class="stat-number">{{ urgentCount }}</div>
          <div class="stat-label">Urgent</div>
        </div>
      </div>
    </div>

    <!-- Appointments List -->
    <div class="appointments-section">
      <h2 class="section-title">Mes rendez-vous</h2>
      
      <div class="appointments-list" *ngIf="appointments.length > 0">
        <div 
          *ngFor="let appointment of appointments" 
          class="appointment-card"
          [class.urgent]="appointment.isUrgent"
        >
          <!-- Card Header -->
          <div class="card-header">
            <div class="appointment-info">
              <h3 class="appointment-title">
                Rendez-vous #{{ appointment.id }}
                <span *ngIf="appointment.isUrgent" class="urgent-badge">🚨 URGENT</span>
              </h3>
              <div class="appointment-date">
                📅 {{ formatDateTime(appointment.scheduledDate) }}
              </div>
            </div>
            <div class="status-badge" [ngClass]="getStatusBadgeClass(appointment.status)">
              {{ getStatusLabel(appointment.status) }}
            </div>
          </div>

          <!-- Progress Bar -->
          <div class="progress-section">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                [style.width.%]="getProgressPercentage(appointment.status)"
              ></div>
            </div>
            <div class="progress-steps">
              <div class="step" [class.active]="getStepStatus(appointment.status, 1)">
                <div class="step-icon">📝</div>
                <div class="step-label">Demandé</div>
              </div>
              <div class="step" [class.active]="getStepStatus(appointment.status, 2)">
                <div class="step-icon">👩‍⚕️</div>
                <div class="step-label">Assigné</div>
              </div>
              <div class="step" [class.active]="getStepStatus(appointment.status, 3)">
                <div class="step-icon">🏠</div>
                <div class="step-label">Prélèvement</div>
              </div>
              <div class="step" [class.active]="getStepStatus(appointment.status, 4)">
                <div class="step-icon">🔬</div>
                <div class="step-label">Analyse</div>
              </div>
              <div class="step" [class.active]="getStepStatus(appointment.status, 5)">
                <div class="step-icon">📊</div>
                <div class="step-label">Résultats</div>
              </div>
            </div>
          </div>

          <!-- Card Body -->
          <div class="card-body">
            <div class="appointment-details">
              <div class="detail-item">
                <span class="detail-label">📝 Analyses :</span>
                <span class="detail-value">
                  <span *ngFor="let analysis of appointment.analysisTypes; let last = last">
                    {{ analysis.name }}<span *ngIf="!last">, </span>
                  </span>
                </span>
              </div>

              <div class="detail-item">
                <span class="detail-label">📍 Adresse :</span>
                <span class="detail-value">{{ appointment.homeAddress }}</span>
              </div>

              <div class="detail-item" *ngIf="appointment.nurseNotes">
                <span class="detail-label">📝 Notes :</span>
                <span class="detail-value">{{ appointment.nurseNotes }}</span>
              </div>

              <div class="detail-item" *ngIf="appointment.nurse">
                <span class="detail-label">👩‍⚕️ Infirmier(e) :</span>
                <span class="detail-value">{{ appointment.nurse.firstName }} {{ appointment.nurse.lastName }}</span>
              </div>
            </div>

            <!-- Suivi en Temps Réel -->
            <div class="tracking-section" *ngIf="appointment.status === 'NURSE_ON_WAY' || appointment.status === 'IN_PROGRESS'">

              <!-- Bouton Voir Position Infirmier -->
              <div class="nurse-position-card" *ngIf="isNurseLocationShared(appointment)">
                <div class="position-header">
                  <h4>📍 Infirmier en route</h4>
                  <p>{{ appointment.nurse?.firstName }} {{ appointment.nurse?.lastName }} partage sa position</p>
                </div>

                <div class="position-actions">
                  <button
                    mat-raised-button
                    color="primary"
                    (click)="openNursePositionMap(appointment)"
                    class="position-btn">
                    <mat-icon>map</mat-icon>
                    Voir position infirmier
                  </button>

                  <div class="position-info">
                    <span class="info-item">
                      <mat-icon>schedule</mat-icon>
                      Arrivée estimée: {{ getEstimatedArrival(appointment) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- Message si pas de partage de position -->
              <div class="no-position-card" *ngIf="shouldShowNoLocationMessage(appointment)">
                <div class="no-position-content">
                  <span class="no-position-icon">🚗</span>
                  <small>Infirmier en route - Position non partagée</small>
                </div>
              </div>
            </div>

            <!-- Prélèvement en cours -->
            <div class="sampling-section" *ngIf="appointment.status === 'IN_PROGRESS'">
              <div class="sampling-card">
                <h4>🔬 Prélèvement en cours</h4>
                <p>L'infirmier(e) est arrivé(e) et effectue le prélèvement</p>
                <div class="progress-indicator">
                  <div class="pulse-dot"></div>
                  <span>Prélèvement en cours...</span>
                </div>
              </div>
            </div>

            <!-- Analyse en cours -->
            <div class="tracking-section" *ngIf="appointment.status === 'SAMPLING_DONE' || appointment.status === 'ANALYSIS_IN_PROGRESS'">
              <div class="analysis-card">
                <h4>🧪 Analyse en cours</h4>
                <p>Le prélèvement a été effectué avec succès. Les échantillons sont en cours d'analyse au laboratoire.</p>
                <div class="progress-indicator">
                  <div class="pulse-dot"></div>
                  <span>Analyse en laboratoire...</span>
                </div>
                <div class="eta">
                  <span class="eta-label">Résultats attendus :</span>
                  <span class="eta-time">24-48 heures</span>
                </div>
              </div>
            </div>

            <!-- Results Section -->
            <div class="results-section" *ngIf="appointment.status === 'RESULTS_AVAILABLE' || appointment.status === 'COMPLETED'">
              <div class="results-card">
                <h4>📊 Résultats disponibles</h4>
                <p>Prélèvement effectué le {{ formatDate(appointment.scheduledDate) }}</p>
                <div class="results-actions">
                  <button class="btn btn-primary btn-sm" (click)="downloadResults(appointment)">
                    📥 Télécharger PDF
                  </button>
                  <button class="btn btn-outline btn-sm" (click)="viewResults(appointment)">
                    👁️ Consulter
                  </button>
                </div>
                <div class="results-comments" *ngIf="appointment.nurseNotes">
                  <strong>Notes de l'infirmier :</strong>
                  <p>{{ appointment.nurseNotes }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Card Actions -->
          <div class="card-actions">
            <div class="primary-actions">
              <button 
                *ngIf="canCancel(appointment)"
                class="btn btn-danger btn-sm"
                (click)="cancelAppointment(appointment)"
              >
                ❌ Annuler
              </button>
              
              <button 
                *ngIf="canReschedule(appointment)"
                class="btn btn-outline btn-sm"
                (click)="rescheduleAppointment(appointment)"
              >
                📅 Reprogrammer
              </button>
            </div>

            <div class="secondary-actions">
              <button class="btn btn-outline btn-sm" (click)="contactSupport(appointment)">
                💬 Support
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div class="empty-state" *ngIf="appointments.length === 0">
        <div class="empty-icon">📋</div>
        <h3>Aucun rendez-vous</h3>
        <p>Vous n'avez pas encore de prélèvement programmé.</p>
        <a routerLink="/dashboard/new-appointment" class="btn btn-primary">
          📅 Réserver mon premier prélèvement
        </a>
      </div>
    </div>
  </div>
</div>
