.patient-dashboard-container {
  min-height: calc(100vh - 80px);
  background-color: #f9fafb;
  padding: 1.5rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.dashboard-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 0.5rem;
}

.dashboard-subtitle {
  font-size: 1.125rem;
  color: #4b5563;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.15s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.stat-card.urgent {
  border: 2px solid #ef4444;
  animation: pulse 2s infinite;
}

.stat-icon {
  font-size: 2.5rem;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2563eb;
}

.stat-label {
  font-size: 0.875rem;
  color: #4b5563;
}

.appointments-section {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.appointment-card {
  border: 2px solid #e5e7eb;
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.15s ease;
}

.appointment-card:hover {
  border-color: #2563eb;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.appointment-card.urgent {
  border-color: #ef4444;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.appointment-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.urgent-badge {
  font-size: 0.75rem;
  background: #ef4444;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  animation: pulse 2s infinite;
}

.appointment-date {
  font-size: 0.875rem;
  color: #4b5563;
}

.progress-section {
  padding: 1.5rem;
  background: white;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2563eb, #10b981);
  transition: width 0.3s ease;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
  transition: all 0.15s ease;
}

.step.active {
  opacity: 1;
}

.step-icon {
  font-size: 1.125rem;
  margin-bottom: 0.25rem;
}

.step-label {
  font-size: 0.75rem;
  text-align: center;
  color: #4b5563;
}

.card-body {
  padding: 1.5rem;
}

.appointment-details {
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  margin-bottom: 0.75rem;
  align-items: flex-start;
}

.detail-label {
  font-weight: 500;
  color: #4b5563;
  min-width: 120px;
  flex-shrink: 0;
}

.detail-value {
  color: #1f2937;
  flex: 1;
}

.tracking-section,
.results-section {
  margin-bottom: 1.5rem;
}

.tracking-card,
.results-card {
  background: linear-gradient(135deg, #06b6d4 0%, #2563eb 100%);
  color: white;
  padding: 1rem;
  border-radius: 0.75rem;
}

.tracking-card-simple {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  padding: 1rem;
  border-radius: 0.75rem;
}

.sampling-card {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  padding: 1rem;
  border-radius: 0.75rem;
}

.analysis-card {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  padding: 1rem;
  border-radius: 0.75rem;
}

.results-card {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.tracking-card h4,
.tracking-card-simple h4,
.sampling-card h4,
.analysis-card h4,
.results-card h4 {
  margin-bottom: 0.5rem;
}

/* Styles pour les informations de suivi */
.tracking-info {
  margin: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.eta,
.distance,
.last-update {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
}

.eta-label,
.distance-label,
.update-label {
  font-weight: 500;
  opacity: 0.9;
}

.eta-time,
.distance-value,
.update-time {
  font-weight: 600;
  font-size: 1.1em;
}

/* Styles pour les boutons d'action de suivi */
.tracking-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.tracking-actions .btn {
  flex: 1;
  min-width: 140px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.tracking-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.tracking-actions .btn-primary {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.tracking-actions .btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.tracking-actions .btn-outline {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.5);
  color: white;
}

.tracking-actions .btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.7);
}

/* Styles pour l'indicateur de proximité */
.tracking-card.nurse-nearby {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  animation: pulse-nearby 2s infinite;
}

.nearby-message {
  font-weight: 600;
  font-size: 1.1em;
  animation: glow 1.5s ease-in-out infinite alternate;
}

@keyframes pulse-nearby {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

@keyframes glow {
  from {
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }
  to {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8), 0 0 15px rgba(255, 255, 255, 0.6);
  }
}

.info-note {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.info-note small {
  opacity: 0.8;
  font-style: italic;
}

.progress-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.75rem;
}

.pulse-dot {
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

.eta {
  display: flex;
  justify-content: space-between;
  margin: 0.75rem 0;
  font-weight: 500;
}

.results-actions {
  display: flex;
  gap: 0.5rem;
  margin: 0.75rem 0;
}

.results-comments {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.primary-actions,
.secondary-actions {
  display: flex;
  gap: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Status badges */
.status-badge {
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-pending { background: #fef3c7; color: #92400e; }
.status-confirmed { background: #dbeafe; color: #1e40af; }
.status-nurse-assigned { background: #e0e7ff; color: #3730a3; }
.status-nurse-on-way { background: #cffafe; color: #0f766e; }
.status-in-progress { background: #fef3c7; color: #92400e; }
.status-sampling-done { background: #d1fae5; color: #065f46; }

/* Buttons */
.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-primary:hover {
  background: #3b82f6;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: #4b5563;
  border-color: #d1d5db;
}

.btn-outline:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.btn-danger {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .card-header {
    flex-direction: column;
    gap: 0.75rem;
  }

  .progress-steps {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .card-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .primary-actions,
  .secondary-actions {
    width: 100%;
    justify-content: center;
  }

  .position-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .position-btn {
    width: 100%;
    text-align: center;
  }

  .position-info {
    text-align: center;
  }

  .nurse-position-card {
    padding: 16px;
  }
}

/* Styles pour le suivi de position infirmier */
.nurse-position-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 20px;
  margin: 16px 0;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.position-header h4 {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.position-header p {
  margin: 0 0 16px 0;
  opacity: 0.9;
  font-size: 0.95rem;
}

.position-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.position-btn {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  animation: pulse-glow 3s infinite;
}

.position-btn:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.position-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  opacity: 0.9;
}

.no-position-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 8px 12px;
  margin: 8px 0;
  text-align: center;
}

.no-position-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.no-position-icon {
  font-size: 16px;
  color: #6c757d;
}

.no-position-card small {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0;
}

.test-btn {
  background: #e3f2fd !important;
  color: #1976d2 !important;
  border-color: #bbdefb !important;
  font-size: 0.8rem;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.test-btn:hover {
  background: #bbdefb !important;
  border-color: #90caf9 !important;
  transform: translateY(-1px);
}

/* Styles pour la modal de carte */
::ng-deep .nurse-route-dialog {
  border-radius: 12px !important;
  overflow: hidden !important;
}

::ng-deep .nurse-route-dialog .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: 12px !important;
}

/* Animation pour le bouton de position */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
  }
}
