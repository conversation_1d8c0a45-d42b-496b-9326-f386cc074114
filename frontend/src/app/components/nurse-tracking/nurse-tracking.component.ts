import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Appointment } from '../../models/appointment.model';
import { AppointmentService } from '../../services/appointment.service';
import { LocationSharingService } from '../../services/location-sharing.service';
import { NurseTrackingService } from '../../services/nurse-tracking.service';
import { interval, Subscription } from 'rxjs';

@Component({
  selector: 'app-nurse-tracking',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="nurse-tracking-container" *ngIf="appointment && shouldShowTracking()">
      <mat-card class="tracking-card" [ngClass]="getCardClass()">
        <mat-card-header>
          <mat-icon mat-card-avatar [ngClass]="getIconClass()">{{ getTrackingIcon() }}</mat-icon>
          <mat-card-title>{{ getTrackingTitle() }}</mat-card-title>
          <mat-card-subtitle>{{ getTrackingSubtitle() }}</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <div class="tracking-info">
            <!-- Status -->
            <div class="status-section">
              <div class="status-indicator" [ngClass]="getStatusClass()">
                <mat-icon>{{ getStatusIcon() }}</mat-icon>
                <span>{{ getStatusText() }}</span>
              </div>
            </div>

            <!-- Location sharing enabled content -->
            <div *ngIf="appointment.locationSharingEnabled" class="tracking-content">

              <!-- Estimated arrival -->
              <div class="arrival-section" *ngIf="appointment.estimatedArrivalTime">
                <div class="arrival-time">
                  <mat-icon>schedule</mat-icon>
                  <div class="time-info">
                    <div class="time-label">Arrivée estimée</div>
                    <div class="time-value">{{ formatTime(appointment.estimatedArrivalTime) }}</div>
                  </div>
                </div>

                <div class="countdown" *ngIf="timeRemaining">
                  <span class="countdown-text">Dans {{ timeRemaining }}</span>
                </div>
              </div>

              <!-- Distance info -->
              <div class="distance-section" *ngIf="appointment.nurseCurrentLatitude && appointment.nurseCurrentLongitude">
                <div class="distance-info">
                  <mat-icon>straighten</mat-icon>
                  <div class="distance-text">
                    <span class="distance-value">{{ calculateDistance() | number:'1.1-1' }} km</span>
                    <span class="distance-label">de votre domicile</span>
                  </div>
                </div>

                <!-- Bouton pour voir l'itinéraire -->
                <button
                  class="itinerary-btn"
                  (click)="openItinerary()"
                  mat-raised-button
                  color="primary"
                >
                  <mat-icon>directions</mat-icon>
                  Voir l'itinéraire
                </button>
              </div>

              <!-- Last update -->
              <div class="update-section" *ngIf="appointment.locationLastUpdated">
                <div class="last-update">
                  <mat-icon>update</mat-icon>
                  <span>Dernière mise à jour: {{ formatTime(appointment.locationLastUpdated) }}</span>
                </div>
              </div>

              <!-- Loading state -->
              <div class="loading-section" *ngIf="!appointment.nurseCurrentLatitude">
                <mat-spinner diameter="30"></mat-spinner>
                <span>Localisation de l'infirmier en cours...</span>
              </div>
            </div>

            <!-- Location sharing disabled content -->
            <div *ngIf="!appointment.locationSharingEnabled" class="no-tracking-content">
              <div class="no-tracking-message">
                <mat-icon class="no-tracking-icon">location_off</mat-icon>
                <p>L'infirmier a choisi de ne pas partager sa position en temps réel.</p>
                <p class="reassurance">Pas d'inquiétude ! Votre infirmier est en route et vous contactera si nécessaire.</p>
              </div>

              <div class="alternative-info">
                <div class="info-item">
                  <mat-icon>schedule</mat-icon>
                  <span>Rendez-vous prévu à {{ formatTime(appointment.scheduledDate) }}</span>
                </div>
                <div class="info-item" *ngIf="appointment.nurse">
                  <mat-icon>person</mat-icon>
                  <span>{{ appointment.nurse.firstName }} {{ appointment.nurse.lastName }}</span>
                </div>
              </div>
            </div>
          </div>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button (click)="openMap()" *ngIf="appointment.locationSharingEnabled && appointment.nurseCurrentLatitude">
            <mat-icon>map</mat-icon>
            Voir sur la carte
          </button>
          <button mat-button (click)="callNurse()">
            <mat-icon>phone</mat-icon>
            Appeler l'infirmier
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .nurse-tracking-container {
      margin: var(--spacing-4) 0;
    }

    .tracking-card.tracking-enabled {
      background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
      border-left: 4px solid var(--primary-blue);
    }

    .tracking-card.tracking-disabled {
      background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
      border-left: 4px solid var(--neutral-400);
    }

    .tracking-icon {
      background: var(--primary-blue);
      color: white;
    }

    .tracking-icon-disabled {
      background: var(--neutral-400);
      color: white;
    }

    .tracking-info {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);
    }

    .status-section {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      padding: var(--spacing-2) var(--spacing-3);
      border-radius: var(--radius-md);
      background: var(--neutral-100);
      color: var(--neutral-600);
    }

    .status-indicator.status-enabled {
      background: var(--success);
      color: white;
      animation: pulse 2s infinite;
    }

    .status-indicator.status-disabled {
      background: var(--neutral-200);
      color: var(--neutral-600);
    }

    .arrival-section {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .arrival-time {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .time-info {
      display: flex;
      flex-direction: column;
    }

    .time-label {
      font-size: var(--font-size-sm);
      color: var(--neutral-600);
    }

    .time-value {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--primary-blue);
    }

    .countdown {
      text-align: center;
      padding: var(--spacing-2);
      background: var(--primary-blue);
      color: white;
      border-radius: var(--radius-md);
      font-weight: 500;
    }

    .distance-section {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .distance-info {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .distance-text {
      display: flex;
      flex-direction: column;
    }

    .distance-value {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--info);
    }

    .distance-label {
      font-size: var(--font-size-sm);
      color: var(--neutral-600);
    }

    .update-section {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      font-size: var(--font-size-sm);
      color: var(--neutral-600);
    }

    .loading-section {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      justify-content: center;
      padding: var(--spacing-4);
      color: var(--neutral-600);
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    /* No tracking content styles */
    .no-tracking-content {
      text-align: center;
      padding: var(--spacing-4);
    }

    .no-tracking-message {
      margin-bottom: var(--spacing-4);
    }

    .no-tracking-icon {
      font-size: 3rem;
      color: var(--neutral-400);
      margin-bottom: var(--spacing-2);
    }

    .no-tracking-message p {
      margin: var(--spacing-2) 0;
      color: var(--neutral-600);
    }

    .reassurance {
      color: var(--success) !important;
      font-weight: 500;
    }

    .alternative-info {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
      padding: var(--spacing-3);
      background: var(--neutral-50);
      border-radius: var(--radius-md);
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      color: var(--neutral-700);
    }

    @media (max-width: 768px) {
      .arrival-time,
      .distance-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-1);
      }
    }
  `]
})
export class NurseTrackingComponent implements OnInit, OnDestroy {
  @Input() appointment!: Appointment;
  
  timeRemaining: string = '';
  private updateSubscription?: Subscription;

  constructor(
    private appointmentService: AppointmentService,
    private locationSharingService: LocationSharingService,
    private nurseTrackingService: NurseTrackingService
  ) {}

  ngOnInit(): void {
    if (this.appointment && this.appointment.locationSharingEnabled) {
      this.startTimeUpdates();
    }
  }

  shouldShowTracking(): boolean {
    return this.appointment &&
           (this.appointment.status === 'NURSE_ON_WAY' || this.appointment.status === 'IN_PROGRESS');
  }

  getCardClass(): string {
    if (this.appointment.locationSharingEnabled) {
      return 'tracking-enabled';
    } else {
      return 'tracking-disabled';
    }
  }

  getIconClass(): string {
    if (this.appointment.locationSharingEnabled) {
      return 'tracking-icon';
    } else {
      return 'tracking-icon-disabled';
    }
  }

  getTrackingIcon(): string {
    if (this.appointment.locationSharingEnabled) {
      return 'location_on';
    } else {
      return 'location_off';
    }
  }

  getTrackingTitle(): string {
    if (this.appointment.locationSharingEnabled) {
      return 'Suivi en temps réel';
    } else {
      return 'Infirmier en route';
    }
  }

  getTrackingSubtitle(): string {
    if (this.appointment.locationSharingEnabled) {
      return 'Votre infirmier partage sa position';
    } else {
      return 'Position non partagée';
    }
  }

  getStatusClass(): string {
    if (this.appointment.locationSharingEnabled) {
      return 'status-enabled';
    } else {
      return 'status-disabled';
    }
  }

  getStatusIcon(): string {
    if (this.appointment.locationSharingEnabled) {
      return 'radio_button_checked';
    } else {
      return 'radio_button_unchecked';
    }
  }

  getStatusText(): string {
    if (this.appointment.locationSharingEnabled) {
      return 'Position partagée';
    } else {
      return 'Position non partagée';
    }
  }

  ngOnDestroy(): void {
    if (this.updateSubscription) {
      this.updateSubscription.unsubscribe();
    }
  }

  private startTimeUpdates(): void {
    // Mettre à jour le temps restant toutes les 30 secondes
    this.updateSubscription = interval(30000).subscribe(() => {
      this.updateTimeRemaining();
    });
    
    // Mise à jour initiale
    this.updateTimeRemaining();
  }

  private updateTimeRemaining(): void {
    if (this.appointment.estimatedArrivalTime) {
      const now = new Date();
      const arrival = new Date(this.appointment.estimatedArrivalTime);
      const diffMs = arrival.getTime() - now.getTime();
      
      if (diffMs > 0) {
        const minutes = Math.floor(diffMs / (1000 * 60));
        if (minutes > 60) {
          const hours = Math.floor(minutes / 60);
          const remainingMinutes = minutes % 60;
          this.timeRemaining = `${hours}h ${remainingMinutes}min`;
        } else {
          this.timeRemaining = `${minutes} min`;
        }
      } else {
        this.timeRemaining = 'Arrivée imminente';
      }
    }
  }

  calculateDistance(): number {
    if (!this.appointment.nurseCurrentLatitude || 
        !this.appointment.nurseCurrentLongitude ||
        !this.appointment.latitude || 
        !this.appointment.longitude) {
      return 0;
    }

    const R = 6371; // Rayon de la Terre en km
    const dLat = this.toRadians(this.appointment.latitude - this.appointment.nurseCurrentLatitude);
    const dLon = this.toRadians(this.appointment.longitude - this.appointment.nurseCurrentLongitude);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(this.appointment.nurseCurrentLatitude)) * 
      Math.cos(this.toRadians(this.appointment.latitude)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  formatTime(date: Date): string {
    return new Date(date).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  openMap(): void {
    if (this.appointment.nurseCurrentLatitude && this.appointment.nurseCurrentLongitude) {
      const url = `https://www.google.com/maps?q=${this.appointment.nurseCurrentLatitude},${this.appointment.nurseCurrentLongitude}`;
      window.open(url, '_blank');
    }
  }

  callNurse(): void {
    if (this.appointment.nurse?.phone) {
      window.open(`tel:${this.appointment.nurse.phone}`);
    } else {
      alert('Numéro de téléphone de l\'infirmier non disponible');
    }
  }

  openItinerary(): void {
    if (this.appointment.nurseCurrentLatitude && this.appointment.nurseCurrentLongitude &&
        this.appointment.latitude && this.appointment.longitude) {

      // Créer l'URL pour l'itinéraire Google Maps
      const origin = `${this.appointment.nurseCurrentLatitude},${this.appointment.nurseCurrentLongitude}`;
      const destination = `${this.appointment.latitude},${this.appointment.longitude}`;
      const url = `https://www.google.com/maps/dir/${origin}/${destination}`;

      console.log('🗺️ Opening itinerary from nurse to patient');
      console.log('📍 Nurse position:', origin);
      console.log('🏠 Patient address:', destination);

      window.open(url, '_blank');
    } else {
      console.warn('⚠️ Cannot open itinerary: missing location data');
      alert('Impossible d\'afficher l\'itinéraire : données de localisation manquantes');
    }
  }
}
