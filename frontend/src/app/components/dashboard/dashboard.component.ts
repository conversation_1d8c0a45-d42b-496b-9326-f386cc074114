import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule
  ],
  template: `
    <!-- Contenu principal sans header - Le header est maintenant dans app-header -->
    <router-outlet></router-outlet>
  `,
  styles: [`
    /* Styles supprimés - maintenant dans header.component.css */
  `]
})
export class DashboardComponent implements OnInit {
  // Propriétés supprimées - maintenant dans header.component.ts
  
  constructor() {}
  
  ngOnInit(): void {
    // Logique supprimée - maintenant dans header.component.ts
  }
}
