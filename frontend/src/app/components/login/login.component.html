<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <h1 class="auth-title">Connexion</h1>
      <p class="auth-subtitle">Accédez à votre espace MediSample</p>
    </div>

    <form (ngSubmit)="onSubmit()" class="auth-form">
      <div class="form-group">
        <label for="email" class="form-label">Adresse email</label>
        <input
          type="email"
          id="email"
          [(ngModel)]="formData.email"
          name="email"
          class="form-input"
          placeholder="<EMAIL>"
          required
          autocomplete="email"
        />
      </div>

      <div class="form-group">
        <label for="password" class="form-label">Mot de passe</label>
        <input
          type="password"
          id="password"
          [(ngModel)]="formData.password"
          name="password"
          class="form-input"
          placeholder="••••••••"
          required
          autocomplete="current-password"
        />
      </div>

      <div class="error-message" *ngIf="errorMessage">
        {{ errorMessage }}
      </div>

      <button
        type="submit"
        class="auth-button"
        [disabled]="isLoading || !formData.email || !formData.password"
      >
        <span *ngIf="!isLoading">Se connecter</span>
        <span *ngIf="isLoading" class="loading-spinner">
          <span class="spinner"></span>
          Connexion...
        </span>
      </button>
    </form>

    <div class="auth-footer">
      <p class="auth-link-text">
        Pas encore de compte ?
        <a routerLink="/auth/register" class="auth-link">S'inscrire</a>
      </p>
    </div>

    
  </div>
</div>
