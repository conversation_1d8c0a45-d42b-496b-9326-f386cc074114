/* Variables CSS */
:host {
  --primary-color: #1976d2;
  --primary-hover: #1565c0;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --text-primary: #212121;
  --text-secondary: #757575;
  --background-light: #f5f5f5;
  --border-color: #e0e0e0;
  --border-radius: 8px;
  --shadow: 0 2px 4px rgba(0,0,0,0.1);
  --transition: all 0.3s ease;
}

/* Container principal */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

/* Carte d'authentification */
.auth-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  padding: 32px;
  width: 100%;
  max-width: 400px;
}

/* En-tête */
.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.auth-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Formulaire */
.auth-form {
  margin-bottom: 32px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-input::placeholder {
  color: var(--text-secondary);
}

/* Message d'erreur */
.error-message {
  background-color: #ffebee;
  color: var(--error-color);
  padding: 12px 16px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
  font-size: 0.875rem;
  border-left: 4px solid var(--error-color);
}

/* Bouton principal */
.auth-button {
  width: 100%;
  padding: 14px 24px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.auth-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
}

.auth-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Spinner de chargement */
.loading-spinner {
  display: flex;
  align-items: center;
  gap: 8px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pied de page */
.auth-footer {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid var(--border-color);
}

.auth-link-text {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

.auth-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
}

.auth-link:hover {
  text-decoration: underline;
}

/* Section démo */
.demo-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--border-color);
}

.demo-title {
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 16px;
  font-weight: 500;
}

.demo-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.demo-button {
  padding: 8px 12px;
  border: 2px solid transparent;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-align: center;
}

.demo-button.admin {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
}

.demo-button.nurse {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
}

.demo-button.patient {
  background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
  color: white;
}

.demo-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

/* Responsive */
@media (max-width: 480px) {
  .auth-container {
    padding: 16px;
  }
  
  .auth-card {
    padding: 24px;
  }
  
  .auth-title {
    font-size: 1.5rem;
  }
  
  .demo-buttons {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
