import { Component, ChangeDetectorRef } from '@angular/core';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../../services/auth.service';
import { LoginRequest } from '../../models/user.model';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatSnackBarModule
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent {
  formData = {
    email: '',
    password: ''
  };

  isLoading = false;
  errorMessage = '';
  returnUrl: string;

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
  }

  onSubmit(): void {
    if (!this.isLoading && this.formData.email && this.formData.password) {
      this.isLoading = true;
      this.errorMessage = '';

      const credentials: LoginRequest = {
        username: this.formData.email,
        password: this.formData.password
      };

      this.authService.login(credentials).subscribe({
        next: () => {
          // Utiliser setTimeout pour éviter ExpressionChangedAfterItHasBeenCheckedError
          setTimeout(() => {
            this.isLoading = false;
            this.cdr.detectChanges();
          });
          
          // Obtenir l'utilisateur connecté pour déterminer la redirection
          const currentUser = this.authService.getCurrentUser();
          let redirectUrl = this.returnUrl;
          
          // Si pas d'URL de retour spécifique, rediriger selon le rôle
          if (this.returnUrl === '/dashboard') {
            redirectUrl = currentUser ? this.authService.getDashboardUrlByRole(currentUser) : '/dashboard/appointments';
          }
          
          this.snackBar.open('Connexion réussie! Redirection...', 'Fermer', {
            duration: 2000,
            panelClass: ['success-snackbar']
          });
          
          setTimeout(() => {
            this.router.navigate([redirectUrl]);
          }, 1000);
        },
        error: (error) => {
          // Utiliser setTimeout pour éviter ExpressionChangedAfterItHasBeenCheckedError
          setTimeout(() => {
            this.isLoading = false;
            this.errorMessage = error.error?.message || 'Erreur de connexion. Vérifiez vos identifiants.';
            this.cdr.detectChanges();
          });
          console.error('Login error:', error);
        }
      });
    }
  }

  quickLogin(role: string): void {
    // Use the correct credentials that match the backend DataInitializer
    if (role === 'patient') {
      this.formData = {
        email: 'patient1',
        password: 'patient123'
      };
    } else if (role === 'nurse') {
      this.formData = {
        email: 'nurse1',
        password: 'nurse123'
      };
    } else if (role === 'admin') {
      this.formData = {
        email: 'admin',
        password: 'admin123'
      };
    }

    this.onSubmit();
  }
}
