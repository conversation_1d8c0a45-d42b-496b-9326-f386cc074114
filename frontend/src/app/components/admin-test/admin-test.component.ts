import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { AdminService } from '../../services/admin.service';

@Component({
  selector: 'app-admin-test',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <div style="padding: 20px;">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>admin_panel_settings</mat-icon>
            Test Admin Dashboard
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Test de connexion aux endpoints admin</p>
          
          <div style="margin: 20px 0;">
            <button mat-raised-button color="primary" (click)="testPendingAppointments()">
              Test Rendez-vous en attente
            </button>
          </div>
          
          <div style="margin: 20px 0;">
            <button mat-raised-button color="accent" (click)="testAllAppointments()">
              Test Tous les rendez-vous
            </button>
          </div>
          
          <div style="margin: 20px 0;">
            <button mat-raised-button (click)="testAvailableNurses()">
              Test Infirmiers disponibles
            </button>
          </div>
          
          <div *ngIf="result" style="margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
            <h3>Résultat :</h3>
            <pre>{{ result | json }}</pre>
          </div>
          
          <div *ngIf="error" style="margin-top: 20px; padding: 10px; background: #ffebee; border-radius: 4px; color: #c62828;">
            <h3>Erreur :</h3>
            <pre>{{ error | json }}</pre>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    mat-card {
      max-width: 800px;
      margin: 0 auto;
    }
    
    button {
      margin-right: 10px;
      margin-bottom: 10px;
    }
    
    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      max-height: 300px;
      overflow-y: auto;
    }
  `]
})
export class AdminTestComponent implements OnInit {
  result: any = null;
  error: any = null;

  constructor(private adminService: AdminService) {}

  ngOnInit(): void {
    console.log('AdminTestComponent initialisé');
  }

  testPendingAppointments(): void {
    this.clearResults();
    console.log('Test des rendez-vous en attente...');
    
    this.adminService.getPendingAppointments().subscribe({
      next: (data) => {
        console.log('Succès:', data);
        this.result = { 
          message: 'Succès - Rendez-vous en attente', 
          data: data,
          count: data.length 
        };
      },
      error: (error) => {
        console.error('Erreur:', error);
        this.error = {
          message: 'Erreur lors du chargement des rendez-vous en attente',
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          error: error.error
        };
      }
    });
  }

  testAllAppointments(): void {
    this.clearResults();
    console.log('Test de tous les rendez-vous...');
    
    this.adminService.getAllAppointments().subscribe({
      next: (data) => {
        console.log('Succès:', data);
        this.result = { 
          message: 'Succès - Tous les rendez-vous', 
          data: data,
          count: data.length 
        };
      },
      error: (error) => {
        console.error('Erreur:', error);
        this.error = {
          message: 'Erreur lors du chargement de tous les rendez-vous',
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          error: error.error
        };
      }
    });
  }

  testAvailableNurses(): void {
    this.clearResults();
    console.log('Test des infirmiers disponibles...');
    
    this.adminService.getAvailableNurses().subscribe({
      next: (data) => {
        console.log('Succès:', data);
        this.result = { 
          message: 'Succès - Infirmiers disponibles', 
          data: data,
          count: data.length 
        };
      },
      error: (error) => {
        console.error('Erreur:', error);
        this.error = {
          message: 'Erreur lors du chargement des infirmiers disponibles',
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          error: error.error
        };
      }
    });
  }

  private clearResults(): void {
    this.result = null;
    this.error = null;
  }
}
