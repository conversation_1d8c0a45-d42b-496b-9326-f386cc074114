:host {
  --primary-blue: #1976d2;
  --secondary-green: #4caf50;
  --error: #ef4444;
  --info: #3b82f6;

  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #eeeeee;
  --neutral-300: #e0e0e0;
  --neutral-400: #bdbdbd;
  --neutral-500: #9e9e9e;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;

  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-8: 32px;
  --spacing-12: 48px;

  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;

  --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-md: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --shadow-lg: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
  --shadow-xl: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);

  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
}

.nurse-dashboard-container {
  min-height: calc(100vh - 80px);
  background-color: var(--neutral-50);
  padding: var(--spacing-6) 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.dashboard-header {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-green) 100%);
  color: white;
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-2);
}

.dashboard-subtitle {
  font-size: var(--font-size-lg);
  opacity: 0.9;
}

.header-stats {
  display: flex;
  gap: var(--spacing-4);
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4);
  text-align: center;
  min-width: 80px;
}

.stat-card.urgent {
  background: rgba(239, 68, 68, 0.2);
  animation: pulse 2s infinite;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-xs);
  opacity: 0.9;
}

.quick-actions {
  display: flex;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.action-btn {
  padding: var(--spacing-3) var(--spacing-4);
  background: white;
  border: 2px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.action-btn:hover {
  border-color: var(--primary-blue);
  background-color: var(--primary-blue);
  color: white;
}

.map-container {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-md);
}

.map-placeholder {
  text-align: center;
  padding: var(--spacing-8);
  background: var(--neutral-50);
  border-radius: var(--radius-lg);
}

.map-locations {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  justify-content: center;
  margin-top: var(--spacing-4);
}

.location-marker {
  background: var(--primary-blue);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  position: relative;
}

.location-marker.urgent {
  background: var(--error);
  animation: pulse 2s infinite;
}

.urgent-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--error);
  color: white;
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.filters-section {
  margin-bottom: var(--spacing-6);
}

.filter-tabs {
  display: flex;
  gap: var(--spacing-2);
  background: white;
  padding: var(--spacing-2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.filter-tab {
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  background: transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.filter-tab.active {
  background: var(--primary-blue);
  color: white;
}

.appointments-grid {
  display: grid;
  gap: var(--spacing-6);
}

.appointment-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-fast);
  border-left: 4px solid var(--neutral-200);
}

.appointment-card.urgent {
  border-left-color: var(--error);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
}

.appointment-card.in-progress {
  border-left-color: var(--info);
  background: linear-gradient(135deg, white 0%, rgba(59, 130, 246, 0.05) 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--neutral-200);
}

.patient-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: var(--spacing-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.urgent-indicator {
  animation: pulse 2s infinite;
}

.appointment-time {
  font-size: var(--font-size-sm);
  color: var(--neutral-600);
}

.status-badge {
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
}

.card-body {
  padding: var(--spacing-6);
}

.appointment-details {
  margin-bottom: var(--spacing-6);
}

.detail-item {
  display: flex;
  margin-bottom: var(--spacing-3);
  align-items: flex-start;
}

.detail-label {
  font-weight: 500;
  color: var(--neutral-600);
  min-width: 140px;
  flex-shrink: 0;
}

.detail-value {
  color: var(--neutral-800);
  flex: 1;
}

.navigation-section,
.contact-section {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background: var(--neutral-50);
  border-radius: var(--radius-md);
}

.navigation-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
  border: none !important;
  color: white !important;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.3);
}

.navigation-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.4);
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%) !important;
}

.address-mode {
  font-size: 0.8em;
  margin-left: 4px;
  opacity: 0.8;
}

.distance {
  font-size: var(--font-size-sm);
  color: var(--neutral-600);
  font-weight: 600;
  background: #e0f2fe;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #b3e5fc;
}

.address-preview {
  font-size: var(--font-size-xs);
  color: var(--neutral-500);
  font-style: italic;
  margin-left: auto;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-6);
  background: var(--neutral-50);
  border-top: 1px solid var(--neutral-200);
}

.status-actions,
.secondary-actions {
  display: flex;
  gap: var(--spacing-2);
}

.tracking-section {
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, var(--info) 0%, var(--primary-blue) 100%);
  color: white;
}

.tracking-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tracking-status {
  font-weight: 500;
}

.loading-state {
  text-align: center;
  padding: var(--spacing-12);
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--neutral-200);
  border-top: 4px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: var(--spacing-12);
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-4);
}

.statistics-section {
  margin-top: var(--spacing-12);
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--neutral-800);
  margin-bottom: var(--spacing-6);
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-6);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  background: var(--neutral-50);
  border-radius: var(--radius-lg);
}

.stat-icon {
  font-size: 2rem;
}

.stat-content .stat-number {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-blue);
}

.stat-content .stat-label {
  font-size: var(--font-size-sm);
  color: var(--neutral-600);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  max-width: 500px;
  width: 90%;
}

.modal-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  margin-top: var(--spacing-4);
}

.btn {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition-fast);
}

.btn-primary {
  background: var(--primary-blue);
  color: white;
}

.btn-secondary {
  background: var(--secondary-green);
  color: white;
}

.btn-outline {
  background: transparent;
  border-color: var(--neutral-300);
  color: var(--neutral-700);
}

.btn-sm {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-sm);
}

.form-textarea {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  resize: vertical;
  font-family: inherit;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .header-stats {
    width: 100%;
    justify-content: center;
  }

  .quick-actions {
    flex-wrap: wrap;
  }

  .filter-tabs {
    flex-wrap: wrap;
  }

  .card-actions {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .status-actions,
  .secondary-actions {
    width: 100%;
    justify-content: center;
  }
}

/* Styles pour le partage de position */
.location-sharing-section {
  border-top: 1px solid var(--neutral-200);
  background: var(--neutral-50);
}

.tracking-active {
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
}

.tracking-inactive {
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, var(--neutral-100) 0%, var(--neutral-200) 100%);
  color: var(--neutral-700);
}

.tracking-in-progress {
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, var(--info) 0%, var(--primary-blue) 100%);
  color: white;
}

.tracking-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.tracking-details {
  margin: var(--spacing-2) 0;
}

.tracking-note {
  color: rgba(255, 255, 255, 0.9);
  font-style: italic;
  display: block;
  margin-bottom: 0.25rem;
}

.tracking-duration {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  display: block;
}

.tracking-status {
  font-weight: 500;
  font-size: var(--font-size-sm);
}

.tracking-indicator {
  font-size: var(--font-size-xs);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  background: rgba(255, 255, 255, 0.2);
}

.tracking-note {
  font-size: var(--font-size-xs);
  opacity: 0.9;
  font-style: italic;
}

.tracking-active .tracking-indicator {
  animation: pulse 2s infinite;
}

/* Boutons dans les sections de tracking */
.tracking-active .btn,
.tracking-inactive .btn,
.tracking-in-progress .btn {
  margin-top: var(--spacing-2);
}

.tracking-active .btn {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.tracking-active .btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Styles pour la modal de partage de position */
.location-modal {
  max-width: 600px;
  width: 95%;
}

.modal-header {
  text-align: center;
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--neutral-200);
}

.modal-header h3 {
  color: var(--primary-blue);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-2xl);
}

.modal-subtitle {
  color: var(--neutral-600);
  font-size: var(--font-size-sm);
  margin: 0;
}

.location-question {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  padding: var(--spacing-4);
  background: var(--neutral-50);
  border-radius: var(--radius-lg);
}

.question-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.question-content h4 {
  color: var(--neutral-800);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-lg);
}

.question-content p {
  color: var(--neutral-600);
  margin: 0;
  line-height: 1.5;
}

.location-benefits {
  margin-bottom: var(--spacing-6);
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  padding: var(--spacing-3);
  background: white;
  border-radius: var(--radius-md);
  border-left: 3px solid var(--primary-blue);
}

.benefit-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.benefit-item span:last-child {
  color: var(--neutral-700);
  font-size: var(--font-size-sm);
}

/* Actions de la modal */
.location-modal .modal-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: center;
  flex-wrap: wrap;
}

.location-modal .btn {
  flex: 1;
  min-width: 200px;
  padding: var(--spacing-3) var(--spacing-4);
  font-weight: 500;
}

.location-modal .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, #1565c0 100%);
  border-color: var(--primary-blue);
}

.location-modal .btn-primary:hover {
  background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.location-modal .btn-outline {
  border-color: var(--neutral-300);
  color: var(--neutral-600);
}

.location-modal .btn-outline:hover {
  background: var(--neutral-100);
  border-color: var(--neutral-400);
}

/* Styles pour le workflow de mission */
.mission-progress {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  align-items: flex-start;
}

.mission-completed {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
  flex-wrap: wrap;
}

.sampling-checkbox {
  margin-top: var(--spacing-2);
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--neutral-700);
  user-select: none;
}

.checkbox-container input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-blue);
  cursor: pointer;
}

.checkmark {
  position: relative;
}

.checkbox-label {
  font-weight: 500;
}

.checkbox-container:hover .checkbox-label {
  color: var(--primary-blue);
}

/* Styles pour les boutons d'état */
.btn-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  border-color: #17a2b8;
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  border-color: #ffc107;
  color: #212529;
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  border-color: #28a745;
  color: white;
}

/* Animation pour les boutons en cours */
.btn-info, .btn-warning {
  position: relative;
  overflow: hidden;
}

.btn-info::after, .btn-warning::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Responsive pour mobile */
@media (max-width: 768px) {
  .mission-progress {
    width: 100%;
  }

  .mission-completed {
    flex-direction: column;
    width: 100%;
  }

  .mission-completed .btn {
    width: 100%;
  }
}

/* Styles pour la modale de confirmation de prélèvement */
.sampling-modal {
  max-width: 500px;
}

.sampling-icon {
  font-size: 3rem;
  color: var(--secondary-green);
  margin-bottom: var(--spacing-4);
}

.sampling-details {
  background: var(--neutral-50);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  margin: var(--spacing-4) 0;
  border-left: 4px solid var(--primary-blue);
}

.sampling-details .detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.sampling-details .detail-item:last-child {
  margin-bottom: 0;
}

.sampling-details .detail-label {
  font-weight: 600;
  color: var(--neutral-700);
  min-width: 100px;
}

.sampling-details .detail-value {
  color: var(--neutral-800);
  text-align: right;
  flex: 1;
}

.warning-message {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin-top: var(--spacing-4);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-2);
}

.warning-icon {
  font-size: 1.2rem;
  color: #f59e0b;
  margin-top: 2px;
}

.warning-message p {
  margin: 0;
  color: #92400e;
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

.btn-success {
  background: var(--secondary-green);
  color: white;
  border-color: var(--secondary-green);
}

.btn-success:hover {
  background: #45a049;
  border-color: #45a049;
  transform: translateY(-1px);
}

.btn-info {
  background: var(--info);
  color: white;
  border-color: var(--info);
}

.btn-info:hover {
  background: #0284c7;
  border-color: #0284c7;
  transform: translateY(-1px);
}

.close-btn {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--neutral-500);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition-fast);
}

.close-btn:hover {
  background: var(--neutral-100);
  color: var(--neutral-700);
}

/* Styles pour la modale de dépôt de résultats */
.results-modal {
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
}

.results-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.appointment-summary {
  background: var(--neutral-50);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  border-left: 4px solid var(--primary-blue);
}

.appointment-summary h4 {
  margin-bottom: var(--spacing-3);
  color: var(--neutral-800);
  font-size: var(--font-size-lg);
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.summary-label {
  font-weight: 600;
  color: var(--neutral-600);
  font-size: var(--font-size-sm);
}

.summary-value {
  color: var(--neutral-800);
  font-weight: 500;
}

.file-upload-section h4,
.comments-section h4 {
  margin-bottom: var(--spacing-3);
  color: var(--neutral-800);
  font-size: var(--font-size-lg);
}

.upload-description {
  color: var(--neutral-600);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-sm);
}

.file-input-container {
  position: relative;
  margin-bottom: var(--spacing-4);
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.file-input-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  border: 2px dashed var(--neutral-300);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  background: var(--neutral-50);
  justify-content: center;
}

.file-input-label:hover:not(.disabled) {
  border-color: var(--primary-blue);
  background: var(--primary-blue-light);
  color: white;
}

.file-input-label.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.upload-icon {
  font-size: var(--font-size-xl);
}

.upload-text {
  font-weight: 500;
  font-size: var(--font-size-base);
}

.selected-files h5 {
  margin-bottom: var(--spacing-3);
  color: var(--neutral-700);
  font-size: var(--font-size-base);
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  background: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.file-item:hover {
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-sm);
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex: 1;
}

.file-icon {
  font-size: var(--font-size-xl);
  color: var(--error);
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.file-name {
  font-weight: 500;
  color: var(--neutral-800);
}

.file-size {
  font-size: var(--font-size-sm);
  color: var(--neutral-600);
}

.remove-file-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: none;
  background: var(--error);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all var(--transition-fast);
}

.remove-file-btn:hover:not(:disabled) {
  background: #dc2626;
  transform: scale(1.1);
}

.remove-file-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.comments-textarea {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--font-size-base);
  line-height: 1.5;
  resize: vertical;
  min-height: 100px;
  transition: border-color var(--transition-fast);
}

.comments-textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.comments-textarea:disabled {
  background: var(--neutral-100);
  cursor: not-allowed;
}

.info-section {
  margin-top: var(--spacing-4);
}

.info-box {
  display: flex;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: var(--radius-md);
}

.info-icon {
  font-size: var(--font-size-xl);
  color: var(--primary-blue);
  flex-shrink: 0;
}

.info-content h5 {
  margin-bottom: var(--spacing-2);
  color: var(--primary-blue);
  font-size: var(--font-size-base);
}

.info-content ul {
  margin: 0;
  padding-left: var(--spacing-4);
  color: var(--neutral-700);
}

.info-content li {
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-sm);
}

.loading-spinner-small {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-2);
}

/* Responsive pour la modale de prélèvement */
@media (max-width: 768px) {
  .sampling-modal {
    max-width: calc(100vw - 2rem);
  }

  .sampling-details .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-1);
  }

  .sampling-details .detail-value {
    text-align: left;
  }

  .modal-actions {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .modal-actions .btn {
    width: 100%;
  }

  .results-modal {
    max-width: calc(100vw - 1rem);
    margin: var(--spacing-2);
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .file-info {
    width: 100%;
  }

  .remove-file-btn {
    align-self: flex-end;
  }
}

/* Styles pour le statut de partage de position */
.status-indicator {
  margin-bottom: 8px;
}

.status-text {
  display: inline-block;
  padding: 6px 12px;
  background-color: #17a2b8;
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.location-sharing-status {
  margin: 8px 0;
  padding: 8px;
  border-radius: 6px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
}

.sharing-active {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.sharing-inactive {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.sharing-indicator {
  font-size: 13px;
  font-weight: 500;
}

.sharing-active .sharing-indicator {
  color: #28a745;
}

.sharing-inactive .sharing-indicator {
  color: #6c757d;
}

.btn-xs {
  padding: 4px 8px;
  font-size: 11px;
  border-radius: 4px;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid #6c757d;
  color: #6c757d;
}

.btn-outline:hover {
  background-color: #6c757d;
  color: white;
}
