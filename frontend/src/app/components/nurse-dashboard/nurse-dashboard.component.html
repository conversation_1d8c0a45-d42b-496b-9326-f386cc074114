

<div class="nurse-dashboard-container">
  <div class="container">
    <!-- Header -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="welcome-section">
          <h1 class="dashboard-title">Mes missions</h1>
          <p class="dashboard-subtitle">
            Bonjour {{ currentUser?.firstName }}, vous avez {{ appointments.length }} mission(s) aujourd'hui
          </p>
        </div>
        <div class="header-stats">
          <div class="stat-card urgent" *ngIf="urgentCount > 0">
            <div class="stat-number">{{ urgentCount }}</div>
            <div class="stat-label">🚨 Urgent</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ todayAppointments.length }}</div>
            <div class="stat-label">Aujourd'hui</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ completedToday }}</div>
            <div class="stat-label">Terminées</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <button class="action-btn" (click)="refreshMissions()">
        🔄 Actualiser
      </button>
      <button class="action-btn" (click)="showMap = !showMap">
        🗺️ {{ showMap ? 'Masquer' : 'Voir' }} la carte
      </button>
      <button class="action-btn" (click)="toggleAvailability()">
        {{ isAvailable ? '⏸️ Pause' : '▶️ Disponible' }}
      </button>
    </div>

    <!-- Map View -->
    <div class="map-container" *ngIf="showMap">
      <div class="map-placeholder">
        <h3>🗺️ Carte des missions</h3>
        <p>Visualisation des adresses de prélèvement</p>
        <div class="map-locations">
          <div
            *ngFor="let appointment of appointments"
            class="location-marker"
            [class.urgent]="appointment.isUrgent"
          >
            📍 {{ appointment.homeAddress ? appointment.homeAddress.split(',')[0] : 'Adresse non disponible' }}
            <span *ngIf="appointment.isUrgent" class="urgent-badge">URGENT</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
      <div class="filter-tabs">
        <button
          class="filter-tab"
          [class.active]="currentFilter === 'today'"
          (click)="setFilter('today')"
        >
          Aujourd'hui ({{ todayAppointments.length }})
        </button>
        <button
          class="filter-tab"
          [class.active]="currentFilter === 'urgent'"
          (click)="setFilter('urgent')"
        >
          Urgent ({{ urgentCount }})
        </button>
        <button
          class="filter-tab"
          [class.active]="currentFilter === 'pending'"
          (click)="setFilter('pending')"
        >
          En attente ({{ pendingCount }})
        </button>
        <button
          class="filter-tab"
          [class.active]="currentFilter === 'all'"
          (click)="setFilter('all')"
        >
          Toutes ({{ appointments.length }})
        </button>
      </div>
    </div>

    <!-- Appointments List -->
    <div class="appointments-section">
      <!-- Loading indicator -->
      <div *ngIf="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Chargement des missions...</p>
      </div>

      <div *ngIf="!isLoading" class="appointments-grid">
        <div
          *ngFor="let appointment of filteredAppointments"
          class="appointment-card"
          [class.urgent]="appointment.isUrgent"
          [class.in-progress]="appointment.status === 'IN_PROGRESS'"
        >
          <!-- Card Header -->
          <div class="card-header">
            <div class="appointment-info">
              <h3 class="patient-name">
                {{ appointment.patient.firstName }} {{ appointment.patient.lastName }}
                <span *ngIf="appointment.isUrgent" class="urgent-indicator">🚨</span>
              </h3>
              <div class="appointment-time">
                📅 {{ formatDateTime(appointment.scheduledDate) }}
              </div>
            </div>
            <div class="status-badge" [ngClass]="getStatusBadgeClass(appointment.status)">
              {{ getStatusLabel(appointment.status) }}
            </div>
          </div>

          <!-- Card Body -->
          <div class="card-body">
            <div class="appointment-details">
              <div class="detail-item">
                <span class="detail-label">🧪 Analyses :</span>
                <span class="detail-value">
                  <span *ngFor="let analysis of appointment.analysisTypes; let last = last">
                    {{ analysis.name }}<span *ngIf="!last">, </span>
                  </span>
                </span>
              </div>

              <div class="detail-item">
                <span class="detail-label">⏱️ Durée estimée :</span>
                <span class="detail-value">{{ appointment.estimatedDurationMinutes }} minutes</span>
              </div>

              <div class="detail-item">
                <span class="detail-label">📍 Adresse :</span>
                <span class="detail-value">{{ appointment.homeAddress }}</span>
              </div>

              <div class="detail-item" *ngIf="appointment.symptoms">
                <span class="detail-label">🩺 Symptômes :</span>
                <span class="detail-value">{{ appointment.symptoms }}</span>
              </div>

              <div class="detail-item" *ngIf="appointment.specialInstructions">
                <span class="detail-label">📋 Instructions :</span>
                <span class="detail-value">{{ appointment.specialInstructions }}</span>
              </div>
            </div>

            <!-- Navigation -->
            <div class="navigation-section" *ngIf="appointment.latitude && appointment.longitude || appointment.homeAddress">
              <button
                class="btn btn-primary btn-sm navigation-btn"
                (click)="openNavigation(appointment)"
                [title]="(appointment.latitude && appointment.longitude) ? 'Ouvrir l\'itinéraire GPS vers le patient' : 'Ouvrir l\'itinéraire vers l\'adresse du patient'">
                🧭 Itinéraire
                <span *ngIf="!(appointment.latitude && appointment.longitude)" class="address-mode">📍</span>
              </button>
              <span class="distance" *ngIf="appointment.latitude && appointment.longitude">
                ~{{ getEstimatedDistance(appointment) }} km
              </span>
              <span class="address-preview">
                {{ appointment.homeAddress ? appointment.homeAddress.split(',')[0] : 'GPS disponible' }}
              </span>
            </div>

            <!-- Patient Contact -->
            <div class="contact-section">
              <button class="btn btn-outline btn-sm" (click)="callPatient(appointment)">
                📞 Appeler
              </button>
              <button class="btn btn-outline btn-sm" (click)="sendSMS(appointment)">
                💬 SMS
              </button>
            </div>
          </div>
          <!-- Card Actions -->
          <div class="card-actions">
            <div class="status-actions">
              <!-- Avant mission : Démarrer mission + partage -->
              <button
                *ngIf="appointment.status === 'NURSE_ASSIGNED' || appointment.status === 'CONFIRMED'"
                class="btn btn-primary btn-sm"
                (click)="startMission(appointment)"
              >
                🚀 Démarrer mission + partage
              </button>

              <!-- En route vers client -->
              <div *ngIf="appointment.status === 'NURSE_ON_WAY'" class="mission-progress">
                <div class="status-indicator">
                  <span class="status-text">🚗 En route vers client</span>
                </div>


                <button
                  class="btn btn-success btn-sm"
                  (click)="arriveAtPatient(appointment)"
                >
                  🏠 Arrivé chez le patient
                </button>
              </div>

              <!-- Mission en cours (sans partage de position) -->
              <div *ngIf="appointment.status === 'IN_PROGRESS'" class="mission-progress">
                <button class="btn btn-warning btn-sm" disabled>
                  🔄 Mission en cours
                </button>

                <div class="sampling-checkbox">
                  <label class="checkbox-container">
                    <input
                      type="checkbox"
                      [checked]="appointment.samplingCompleted || false"
                      (change)="onSamplingCheckboxChange(appointment, $event)"
                    >
                    <span class="checkmark"></span>
                    <span class="checkbox-label">Prélèvement effectué</span>
                  </label>
                </div>
              </div>

              <!-- Prélèvement effectué -->
              <div *ngIf="appointment.status === 'SAMPLING_DONE'" class="mission-completed">
                <button class="btn btn-success btn-sm" disabled>
                  ✅ Prélèvement effectué
                </button>

                <button
                  class="btn btn-primary btn-sm"
                  (click)="uploadResults(appointment)"
                >
                  📤 Déposer résultats
                </button>

                <button
                  class="btn btn-warning btn-sm"
                  (click)="publishResults(appointment)"
                  title="Publier les résultats et envoyer par email au patient"
                >
                  📧 Publier & Envoyer
                </button>
              </div>

              <!-- Actions pour les missions avec résultats déposés -->
              <div *ngIf="appointment.status === 'RESULTS_AVAILABLE' || appointment.status === 'COMPLETED'" class="mission-results">
                <button class="btn btn-success btn-sm" disabled>
                  ✅ Résultats publiés
                </button>

                <button
                  class="btn btn-info btn-sm"
                  (click)="viewUploadedResults(appointment)"
                >
                  👁️ Consulter résultats
                </button>

                <button
                  class="btn btn-secondary btn-sm"
                  (click)="resendResultsEmail(appointment)"
                  title="Renvoyer l'email avec les résultats au patient"
                >
                  📧 Renvoyer email
                </button>
              </div>
            </div>

            <div class="secondary-actions">
              <button class="btn btn-outline btn-sm" (click)="addNotes(appointment)">
                📝 Notes
              </button>
              <button class="btn btn-outline btn-sm" (click)="reportIssue(appointment)">
                ⚠️ Signaler
              </button>
            </div>
          </div>

          <!-- Location Sharing Section -->
          <div class="location-sharing-section" *ngIf="appointment.status === 'NURSE_ON_WAY' || appointment.status === 'IN_PROGRESS'">
            <!-- Partage de position actif -->
            <div class="tracking-active" *ngIf="isLocationSharingActiveForAppointment(appointment.id)">
              <div class="tracking-info">
                <span class="tracking-status">📍 Position partagée avec le patient</span>
                <span class="tracking-indicator">🟢 Actif</span>
              </div>
              <div class="tracking-details">
                <small class="tracking-note">
                  💡 Le patient peut voir votre position en temps réel
                </small>
                <small class="tracking-duration" *ngIf="appointment.locationLastUpdated">
                  ⏱️ Actif depuis {{ getLocationSharingDuration(appointment) }}
                </small>
              </div>
              <button
                class="btn btn-sm btn-warning"
                (click)="stopLocationSharingForAppointment(appointment)"
                *ngIf="canStopLocationSharing(appointment)"
                title="Arrêter le partage de position avec le patient"
              >
                🛑 Arrêter le partage
              </button>
            </div>

            <!-- Mission en cours -->
            <div class="tracking-in-progress" *ngIf="appointment.status === 'IN_PROGRESS'">
              <div class="tracking-info">
                <span class="tracking-status">� Mission en cours</span>
                <span class="tracking-note" *ngIf="isLocationSharingActiveForAppointment(appointment.id)">
                  Le partage de position s'arrêtera automatiquement après le prélèvement
                </span>
              </div>
            </div>

            <!-- Partage de position inactif pour NURSE_ON_WAY -->
            <div class="tracking-inactive" *ngIf="appointment.status === 'NURSE_ON_WAY' && !isLocationSharingActiveForAppointment(appointment.id)">
              <div class="tracking-info">
                <span class="tracking-status">📍 Partage de position désactivé</span>
                <span class="tracking-indicator">⚪ Inactif</span>
              </div>
              <p class="tracking-status">Le patient ne peut pas voir votre position en temps réel.</p>

              <!-- Bouton pour activer le partage -->
              <button
                class="btn btn-sm btn-primary"
                (click)="activateLocationSharing(appointment)"
                style="margin-top: 10px; margin-right: 10px;"
                title="Activer le partage de position avec le patient"
              >
                📡 Activer le partage
              </button>

              
            </div>
          </div>
        </div>
      </div>
      <!-- Empty State -->
      <div class="empty-state" *ngIf="!isLoading && appointments.length === 0">
        <div class="empty-icon">📋</div>
        <h3>Aucune mission aujourd'hui</h3>
        <p>Vous n'avez pas de missions assignées pour aujourd'hui. Profitez de cette journée plus calme !</p>
        <button class="btn btn-primary" (click)="refreshMissions()">
          🔄 Actualiser
        </button>
      </div>
    </div>

    <!-- Statistics -->
    <div class="statistics-section">
      <h2 class="section-title">Statistiques</h2>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <div class="stat-number">{{ weeklyStats.totalMissions }}</div>
            <div class="stat-label">Missions cette semaine</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">⭐</div>
          <div class="stat-content">
            <div class="stat-number">4.8/5</div>
            <div class="stat-label">Note moyenne</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">🎯</div>
          <div class="stat-content">
            <div class="stat-number">95%</div>
            <div class="stat-label">Taux de réussite</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">💰</div>
          <div class="stat-content">
            <div class="stat-number">850€</div>
            <div class="stat-label">Gains cette semaine</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modals -->
<div class="modal-overlay" *ngIf="showNotesModal" (click)="closeModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <h3>Ajouter des notes</h3>
    <textarea
      [(ngModel)]="currentNotes"
      class="form-textarea"
      placeholder="Ajoutez vos observations..."
      rows="4"
    ></textarea>
    <div class="modal-actions">
      <button class="btn btn-outline" (click)="closeModal()">Annuler</button>
      <button class="btn btn-primary" (click)="saveNotes()">Enregistrer</button>
    </div>
  </div>
</div>

<!-- Modal de partage de position -->
<div class="modal-overlay" *ngIf="showLocationModal" (click)="closeLocationModal()">
  <div class="modal-content location-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>🚀 Démarrer la mission</h3>
      <p class="modal-subtitle">Partage de position avec le patient</p>
    </div>

    <div class="modal-body">
      <div class="location-question">
        <div class="question-icon">📍</div>
        <div class="question-content">
          <h4>Pouvez-vous partager votre position avec le client ?</h4>
          <p>Cela permettra au patient de suivre votre progression en temps réel jusqu'à son domicile.</p>
        </div>
      </div>

      <div class="location-benefits">
        <div class="benefit-item">
          <span class="benefit-icon">✅</span>
          <span>Le patient sera rassuré de voir votre progression</span>
        </div>
        <div class="benefit-item">
          <span class="benefit-icon">⏰</span>
          <span>Estimation d'arrivée plus précise</span>
        </div>
        <div class="benefit-item">
          <span class="benefit-icon">🔒</span>
          <span>Le partage s'arrêtera automatiquement après le prélèvement</span>
        </div>
      </div>
    </div>

    <div class="modal-actions">
      <button class="btn btn-outline" (click)="declineLocationSharing()">
        🚫 Non, continuer sans partage
      </button>
      <button class="btn btn-primary" (click)="confirmLocationSharing()">
        📡 Oui, partager ma position
      </button>
    </div>
  </div>
</div>

<!-- Modal de confirmation pour le prélèvement effectué -->
<div class="modal-overlay" *ngIf="showSamplingConfirmModal" (click)="cancelSamplingCompletion()">
  <div class="modal-content sampling-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>🔬 Confirmation de prélèvement</h3>
      <button class="close-btn" (click)="cancelSamplingCompletion()">×</button>
    </div>

    <div class="modal-body">
      <div class="confirmation-message">
        <div class="icon sampling-icon">✅</div>
        <h4>Prélèvement effectué ?</h4>
        <p>
          Confirmez-vous que le prélèvement a été réalisé avec succès pour ce patient ?
        </p>

        <div class="sampling-details" *ngIf="samplingAppointment">
          <div class="detail-item">
            <span class="detail-label">Patient :</span>
            <span class="detail-value">{{ samplingAppointment.patient.firstName }} {{ samplingAppointment.patient.lastName }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Rendez-vous :</span>
            <span class="detail-value">#{{ samplingAppointment.id }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Analyses :</span>
            <span class="detail-value">
              <span *ngFor="let analysis of samplingAppointment.analysisTypes; let last = last">
                {{ analysis.name }}<span *ngIf="!last">, </span>
              </span>
            </span>
          </div>
        </div>

        <div class="warning-message">
          <div class="warning-icon">⚠️</div>
          <p>Cette action marquera le prélèvement comme terminé et arrêtera automatiquement le partage de position.</p>
        </div>
      </div>
    </div>

    <div class="modal-actions">
      <button class="btn btn-outline" (click)="cancelSamplingCompletion()">
        ❌ Annuler
      </button>
      <button class="btn btn-success" (click)="confirmSamplingCompletion()">
        ✅ Confirmer le prélèvement
      </button>
    </div>
  </div>
</div>

<!-- Modal de dépôt de résultats -->
<div class="modal-overlay" *ngIf="showResultsModal" (click)="cancelResultsUpload()">
  <div class="modal-content results-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>📊 Dépôt des résultats</h3>
      <button class="close-btn" (click)="cancelResultsUpload()" [disabled]="isUploadingResults">×</button>
    </div>

    <div class="modal-body">
      <div class="results-form">

        <!-- Informations du rendez-vous -->
        <div class="appointment-summary" *ngIf="resultsAppointment">
          <h4>📋 Résumé du rendez-vous</h4>
          <div class="summary-grid">
            <div class="summary-item">
              <span class="summary-label">Patient :</span>
              <span class="summary-value">{{ resultsAppointment.patient.firstName }} {{ resultsAppointment.patient.lastName }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">Rendez-vous :</span>
              <span class="summary-value">#{{ resultsAppointment.id }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">Date :</span>
              <span class="summary-value">{{ resultsAppointment.scheduledDate | date:'dd/MM/yyyy à HH:mm' }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">Analyses :</span>
              <span class="summary-value">
                <span *ngFor="let analysis of resultsAppointment.analysisTypes; let last = last">
                  {{ analysis.name }}<span *ngIf="!last">, </span>
                </span>
              </span>
            </div>
          </div>
        </div>

        <!-- Upload de fichiers -->
        <div class="file-upload-section">
          <h4>📁 Fichiers de résultats</h4>
          <p class="upload-description">Téléversez les résultats d'analyses au format PDF</p>

          <div class="file-input-container">
            <input
              type="file"
              id="resultsFileInput"
              multiple
              accept=".pdf,application/pdf"
              (change)="onFileSelected($event)"
              [disabled]="isUploadingResults"
              class="file-input"
            >
            <label for="resultsFileInput" class="file-input-label" [class.disabled]="isUploadingResults">
              <span class="upload-icon">📎</span>
              <span class="upload-text">Choisir des fichiers PDF</span>
            </label>
          </div>

          <!-- Liste des fichiers sélectionnés -->
          <div class="selected-files" *ngIf="selectedFiles.length > 0">
            <h5>Fichiers sélectionnés :</h5>
            <div class="file-list">
              <div *ngFor="let file of selectedFiles; let i = index" class="file-item">
                <div class="file-info">
                  <span class="file-icon">📄</span>
                  <div class="file-details">
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">{{ (file.size / 1024 / 1024).toFixed(2) }} MB</span>
                  </div>
                </div>
                <button
                  class="remove-file-btn"
                  (click)="removeFile(i)"
                  [disabled]="isUploadingResults"
                  title="Supprimer ce fichier"
                >
                  ×
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Commentaires -->
        <div class="comments-section">
          <h4>💬 Commentaires et observations</h4>
          <textarea
            [(ngModel)]="resultComments"
            placeholder="Ajoutez vos commentaires sur les résultats, observations particulières, recommandations..."
            class="comments-textarea"
            rows="4"
            [disabled]="isUploadingResults"
          ></textarea>
        </div>

        <!-- Informations importantes -->
        <div class="info-section">
          <div class="info-box">
            <div class="info-icon">ℹ️</div>
            <div class="info-content">
              <h5>Informations importantes</h5>
              <ul>
                <li>Seuls les fichiers PDF sont acceptés</li>
                <li>Taille maximale par fichier : 10 MB</li>
                <li>Le patient sera automatiquement notifié</li>
                <li>Les résultats seront disponibles dans son espace patient</li>
              </ul>
            </div>
          </div>
        </div>

      </div>
    </div>

    <div class="modal-actions">
      <button
        class="btn btn-outline"
        (click)="cancelResultsUpload()"
        [disabled]="isUploadingResults"
      >
        ❌ Annuler
      </button>
      <button
        class="btn btn-success"
        (click)="submitResults()"
        [disabled]="selectedFiles.length === 0 || isUploadingResults"
      >
        <span *ngIf="!isUploadingResults">✅ Déposer les résultats</span>
        <span *ngIf="isUploadingResults">
          <span class="loading-spinner-small"></span>
          Téléversement en cours...
        </span>
      </button>
    </div>
  </div>
</div>
