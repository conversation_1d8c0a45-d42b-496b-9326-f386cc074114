import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule, Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-test-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    RouterModule
  ],
  template: `
    <div class="test-dashboard">
      <mat-card class="welcome-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>dashboard</mat-icon>
            Test Dashboard
          </mat-card-title>
          <mat-card-subtitle>Vérification de l'authentification</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div class="status-section">
            <h3>📊 Statut de l'authentification</h3>
            
            <div class="status-item">
              <strong>🎫 Token présent:</strong> 
              <span [class]="tokenPresent ? 'success' : 'error'">
                {{ tokenPresent ? '✅ Oui' : '❌ Non' }}
              </span>
            </div>
            
            <div class="status-item">
              <strong>✅ Utilisateur connecté:</strong> 
              <span [class]="isLoggedIn ? 'success' : 'error'">
                {{ isLoggedIn ? '✅ Oui' : '❌ Non' }}
              </span>
            </div>
            
            <div class="status-item" *ngIf="currentUser">
              <strong>👤 Utilisateur actuel:</strong> 
              <span class="success">{{ currentUser.firstName }} {{ currentUser.lastName }}</span>
            </div>
            
            <div class="status-item" *ngIf="currentUser">
              <strong>📧 Email:</strong> 
              <span>{{ currentUser.email }}</span>
            </div>
            
            <div class="status-item" *ngIf="currentUser">
              <strong>🎭 Rôle:</strong> 
              <span class="role-badge">{{ currentUser.role }}</span>
            </div>
            
            <div class="status-item" *ngIf="tokenInfo">
              <strong>⏰ Token expire le:</strong> 
              <span [class]="tokenInfo.isExpired ? 'error' : 'success'">
                {{ tokenInfo.expirationDate }}
              </span>
            </div>
          </div>
          
          <div class="debug-section">
            <h3>🔍 Informations de debug</h3>
            <pre class="debug-info">{{ debugInfo }}</pre>
          </div>
        </mat-card-content>
        
        <mat-card-actions>
          <button mat-raised-button color="primary" routerLink="/dashboard">
            <mat-icon>dashboard</mat-icon>
            Aller au Dashboard Principal
          </button>
          
          <button mat-button (click)="logout()">
            <mat-icon>logout</mat-icon>
            Se déconnecter
          </button>
          
          <button mat-button (click)="refreshData()">
            <mat-icon>refresh</mat-icon>
            Actualiser
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .test-dashboard {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    
    .welcome-card {
      margin-bottom: 20px;
    }
    
    .status-section {
      margin-bottom: 30px;
    }
    
    .status-item {
      margin-bottom: 12px;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 4px;
    }
    
    .success {
      color: #28a745;
      font-weight: bold;
    }
    
    .error {
      color: #dc3545;
      font-weight: bold;
    }
    
    .role-badge {
      background: #007bff;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 0.8rem;
    }
    
    .debug-section {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #dee2e6;
    }
    
    .debug-info {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      font-size: 0.8rem;
      max-height: 200px;
      overflow-y: auto;
    }
    
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    mat-card-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }
  `]
})
export class TestDashboardComponent implements OnInit {
  tokenPresent = false;
  isLoggedIn = false;
  currentUser: any = null;
  tokenInfo: any = null;
  debugInfo = '';

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit() {
    this.refreshData();
  }

  refreshData() {
    // Vérifier le token
    const token = this.authService.getToken();
    this.tokenPresent = !!token;
    
    // Vérifier si connecté
    this.isLoggedIn = this.authService.isLoggedIn();
    
    // Récupérer l'utilisateur actuel
    this.currentUser = this.authService.getCurrentUser();
    
    // Analyser le token
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        this.tokenInfo = {
          expirationDate: new Date(payload.exp * 1000).toLocaleString(),
          isExpired: payload.exp * 1000 <= Date.now(),
          subject: payload.sub,
          issuedAt: new Date(payload.iat * 1000).toLocaleString()
        };
      } catch (error) {
        this.tokenInfo = { error: 'Token invalide' };
      }
    }
    
    // Créer les infos de debug
    this.debugInfo = JSON.stringify({
      tokenPresent: this.tokenPresent,
      tokenLength: token ? token.length : 0,
      isLoggedIn: this.isLoggedIn,
      currentUser: this.currentUser,
      tokenInfo: this.tokenInfo,
      localStorage: {
        token: !!localStorage.getItem('token')
      },
      timestamp: new Date().toISOString()
    }, null, 2);
    
    console.log('🔍 Test Dashboard - Données actualisées:', {
      tokenPresent: this.tokenPresent,
      isLoggedIn: this.isLoggedIn,
      currentUser: this.currentUser
    });
  }

  logout() {
    this.authService.logout();
    this.router.navigate(['/login']);
  }
}
