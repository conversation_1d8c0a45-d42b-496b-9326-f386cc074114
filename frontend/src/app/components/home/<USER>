import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="home-container">
      <!-- Hero Section -->
      <section class="hero">
        <div class="container">
          <div class="hero-content">
            <div class="hero-text">
              <h1 class="hero-title" *ngIf="!authService.isAuthenticated()">
                Prélèvements médicaux à domicile
                <span class="hero-highlight">simplifiés</span>
              </h1>
              <h1 class="hero-title" *ngIf="authService.isAuthenticated()">
                Bienvenue sur
                <span class="hero-highlight">MediSample</span>
              </h1>
              <p class="hero-description" *ngIf="!authService.isAuthenticated()">
                Réservez vos analyses médicales à domicile en quelques clics.
                Nos infirmiers qualifiés se déplacent chez vous pour vos prélèvements
                et vous suivez vos résultats en temps réel.
              </p>
              <p class="hero-description" *ngIf="authService.isPatient()">
                Gérez vos prélèvements médicaux en toute simplicité.
                Réservez, suivez et consultez vos résultats depuis votre espace personnel.
              </p>
              <p class="hero-description" *ngIf="authService.isNurse()">
                Accédez à vos missions de prélèvement et gérez votre planning
                depuis votre tableau de bord professionnel.
              </p>
              <p class="hero-description" *ngIf="authService.isAdmin()">
                Supervisez l'ensemble des opérations, gérez les utilisateurs
                et consultez les statistiques de la plateforme.
              </p>
              <div class="hero-actions" *ngIf="!authService.isAuthenticated()">
                <a routerLink="/auth/register" class="btn btn-primary btn-lg">
                  Commencer maintenant
                </a>
                <a routerLink="/auth/login" class="btn btn-outline btn-lg">
                  Se connecter
                </a>
              </div>
              <div class="hero-actions" *ngIf="authService.isAuthenticated()">
                <a *ngIf="authService.isPatient()" routerLink="/dashboard/new-appointment" class="btn btn-primary btn-lg">
                  📅 Réserver un prélèvement
                </a>
                <a *ngIf="authService.isPatient()" routerLink="/dashboard/appointments" class="btn btn-outline btn-lg">
                  📋 Mes rendez-vous
                </a>
                <a *ngIf="authService.isNurse()" routerLink="/dashboard/nurse-dashboard" class="btn btn-primary btn-lg">
                  🚑 Mes missions
                </a>
                <a *ngIf="authService.isAdmin()" routerLink="/dashboard/admin-appointments" class="btn btn-primary btn-lg">
                  ⚙️ Administration
                </a>
              
              </div>
            </div>
            <div class="hero-image">
              <img src="https://images.pexels.com/photos/4386466/pexels-photo-4386466.jpeg?auto=compress&cs=tinysrgb&w=600" 
                   alt="Infirmière préparant un prélèvement médical" 
                   class="hero-img">
            </div>
          </div>
        </div>
      </section>

      <!-- Features Section -->
      <section class="features">
        <div class="container">
          <h2 class="section-title">Pourquoi choisir MediSample ?</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">🏠</div>
              <h3 class="feature-title">À domicile</h3>
              <p class="feature-description">
                Prélèvements effectués dans le confort de votre domicile, 
                sans besoin de vous déplacer.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">👩‍⚕️</div>
              <h3 class="feature-title">Professionnels qualifiés</h3>
              <p class="feature-description">
                Équipe d'infirmiers diplômés et expérimentés pour 
                tous vos besoins de prélèvements.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">📱</div>
              <h3 class="feature-title">Suivi en temps réel</h3>
              <p class="feature-description">
                Suivez l'avancement de vos analyses et recevez 
                vos résultats directement sur la plateforme.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🚨</div>
              <h3 class="feature-title">Service d'urgence</h3>
              <p class="feature-description">
                Prise en charge prioritaire pour les demandes 
                urgentes avec intervention rapide.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🔒</div>
              <h3 class="feature-title">Sécurisé</h3>
              <p class="feature-description">
                Vos données médicales sont protégées et 
                conformes aux normes de confidentialité.
              </p>
            </div>
            <div class="feature-card">
              <div class="feature-icon">🎯</div>
              <h3 class="feature-title">IA intégrée</h3>
              <p class="feature-description">
                Assistant intelligent pour vous suggérer les 
                analyses adaptées à vos symptômes.
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- How it works Section -->
      <section class="how-it-works">
        <div class="container">
          <h2 class="section-title">Comment ça marche ?</h2>
          <div class="steps-container">
            <div class="step">
              <div class="step-number">1</div>
              <div class="step-content">
                <h3 class="step-title">Réservez en ligne</h3>
                <p class="step-description">
                  Choisissez vos analyses, votre créneau et 
                  votre adresse en quelques clics.
                </p>
              </div>
            </div>
            <div class="step">
              <div class="step-number">2</div>
              <div class="step-content">
                <h3 class="step-title">Infirmier assigné</h3>
                <p class="step-description">
                  Un professionnel proche de chez vous 
                  est automatiquement assigné à votre demande.
                </p>
              </div>
            </div>
            <div class="step">
              <div class="step-number">3</div>
              <div class="step-content">
                <h3 class="step-title">Prélèvement à domicile</h3>
                <p class="step-description">
                  L'infirmier se rend chez vous à l'heure 
                  convenue pour effectuer le prélèvement.
                </p>
              </div>
            </div>
            <div class="step">
              <div class="step-number">4</div>
              <div class="step-content">
                <h3 class="step-title">Résultats en ligne</h3>
                <p class="step-description">
                  Recevez vos résultats directement sur 
                  votre espace personnel sécurisé.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="cta" *ngIf="!authService.isAuthenticated()">
        <div class="container">
          <div class="cta-content">
            <h2 class="cta-title">Prêt à commencer ?</h2>
            <p class="cta-description">
              Rejoignez des milliers de patients qui font confiance à MediSample 
              pour leurs prélèvements médicaux à domicile.
            </p>
            <div class="cta-actions">
              <a routerLink="/auth/register" class="btn btn-primary btn-lg">
                Créer un compte
              </a>
              <a routerLink="/auth/login" class="btn btn-outline btn-lg">
                J'ai déjà un compte
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  `,
  styles: [`
    .home-container {
      min-height: calc(100vh - 80px);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Hero Section */
    .hero {
      background: linear-gradient(135deg, #1976d2 0%, #4caf50 100%);
      color: white;
      padding: 80px 0;
      margin-top: -1px;
    }

    .hero-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 60px;
      align-items: center;
    }

    .hero-title {
      font-size: 3rem;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 24px;
    }

    .hero-highlight {
      color: #ff9800;
    }

    .hero-description {
      font-size: 1.125rem;
      line-height: 1.6;
      margin-bottom: 32px;
      opacity: 0.95;
    }

    .hero-actions {
      display: flex;
      gap: 16px;
    }

    .hero-image {
      text-align: center;
    }

    .hero-img {
      width: 100%;
      max-width: 500px;
      height: 400px;
      object-fit: cover;
      border-radius: 16px;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    }

    /* Buttons */
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.2s ease;
      border: 2px solid transparent;
    }

    .btn-lg {
      padding: 16px 32px;
      font-size: 1.125rem;
    }

    .btn-primary {
      background: #ff9800;
      color: white;
    }

    .btn-primary:hover {
      background: #f57c00;
      transform: translateY(-2px);
    }

    .btn-outline {
      border-color: rgba(255, 255, 255, 0.5);
      color: white;
    }

    .btn-outline:hover {
      background-color: white;
      color: #1976d2;
      border-color: white;
    }

    /* Features Section */
    .features {
      padding: 80px 0;
      background-color: white;
    }

    .section-title {
      text-align: center;
      font-size: 2.5rem;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 60px;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 40px;
    }

    .feature-card {
      text-align: center;
      padding: 32px;
      border-radius: 16px;
      transition: transform 0.2s ease;
    }

    .feature-card:hover {
      transform: translateY(-4px);
    }

    .feature-icon {
      font-size: 3rem;
      margin-bottom: 16px;
    }

    .feature-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 12px;
    }

    .feature-description {
      color: #64748b;
      line-height: 1.6;
    }

    /* How it works Section */
    .how-it-works {
      padding: 80px 0;
      background-color: #f8fafc;
    }

    .steps-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 40px;
    }

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 24px;
    }

    .step-number {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #1976d2, #4caf50);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: 16px;
    }

    .step-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 12px;
    }

    .step-description {
      color: #64748b;
      line-height: 1.6;
    }

    /* CTA Section */
    .cta {
      padding: 80px 0;
      background: linear-gradient(135deg, #1e293b 0%, #374151 100%);
      color: white;
    }

    .cta-content {
      text-align: center;
      max-width: 600px;
      margin: 0 auto;
    }

    .cta-title {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 16px;
    }

    .cta-description {
      font-size: 1.125rem;
      line-height: 1.6;
      margin-bottom: 32px;
      opacity: 0.9;
    }

    .cta-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 32px;
      }

      .hero-title {
        font-size: 2.5rem;
      }

      .hero-actions,
      .cta-actions {
        flex-direction: column;
        align-items: center;
      }

      .features-grid {
        grid-template-columns: 1fr;
      }

      .steps-container {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .hero {
        padding: 60px 0;
      }

      .features,
      .how-it-works,
      .cta {
        padding: 60px 0;
      }

      .hero-title {
        font-size: 2rem;
      }

      .section-title,
      .cta-title {
        font-size: 2rem;
      }
    }
  `]
})
export class HomeComponent {
  constructor(public authService: AuthService) {}
}
