import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { Role } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate, CanActivateChild {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkAdminAccess(state.url);
  }

  canActivateChild(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkAdminAccess(state.url);
  }

  private checkAdminAccess(url: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      take(1),
      map(user => {
        // Vérifier si l'utilisateur est connecté
        if (!user) {
          console.warn('🚫 Access denied: User not authenticated');
          this.router.navigate(['/auth/login'], { 
            queryParams: { returnUrl: url },
            replaceUrl: true 
          });
          return false;
        }

        // Vérifier si l'utilisateur a le rôle ADMIN
        if (user.role !== Role.ADMIN) {
          console.warn('🚫 Access denied: User is not admin', {
            userId: user.id,
            userRole: user.role,
            requiredRole: Role.ADMIN
          });
          
          // Rediriger vers le dashboard approprié selon le rôle
          this.redirectToUserDashboard(user.role);
          return false;
        }

        // Vérifier si le compte admin est actif
        if (!user.enabled) {
          console.warn('🚫 Access denied: Admin account is inactive');
          this.router.navigate(['/auth/account-suspended']);
          return false;
        }

        console.log('✅ Admin access granted', {
          userId: user.id,
          userRole: user.role,
          url: url
        });

        return true;
      })
    );
  }

  private redirectToUserDashboard(role: Role): void {
    switch (role) {
      case Role.PATIENT:
        this.router.navigate(['/dashboard/appointments']);
        break;
      case Role.NURSE:
        this.router.navigate(['/dashboard/nurse-dashboard']);
        break;
      default:
        this.router.navigate(['/auth/login']);
        break;
    }
  }
}

// Guard pour les permissions spécifiques d'admin
@Injectable({
  providedIn: 'root'
})
export class AdminPermissionGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    const requiredPermission = route.data['permission'] as string;
    
    return this.authService.currentUser$.pipe(
      take(1),
      map(user => {
        if (!user || user.role !== Role.ADMIN) {
          this.router.navigate(['/auth/login']);
          return false;
        }

        // Vérifier les permissions spécifiques
        if (requiredPermission && !this.hasPermission(user, requiredPermission)) {
          console.warn('🚫 Access denied: Insufficient permissions', {
            userId: user.id,
            requiredPermission: requiredPermission,
            userPermissions: user.permissions || []
          });
          
          this.router.navigate(['/dashboard/admin-dashboard'], {
            queryParams: { error: 'insufficient_permissions' }
          });
          return false;
        }

        return true;
      })
    );
  }

  private hasPermission(user: any, permission: string): boolean {
    // Si l'utilisateur est super admin, il a toutes les permissions
    if (user.isSuperAdmin) {
      return true;
    }

    // Vérifier les permissions spécifiques
    const userPermissions = user.permissions || [];
    return userPermissions.includes(permission);
  }
}
