import { Injectable } from '@angular/core';
import { Observable, from, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

export interface LocationResult {
  success: boolean;
  coordinates?: LocationCoordinates;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class GeolocationService {

  constructor() { }

  getCurrentPosition(): Observable<LocationResult> {
    if (!navigator.geolocation) {
      return of({
        success: false,
        error: 'La géolocalisation n\'est pas supportée par ce navigateur'
      });
    }

    const options: PositionOptions = {
      enableHighAccuracy: true,
      timeout: 30000, // Augmenté à 30 secondes
      maximumAge: 0   // Forcer une nouvelle position, pas de cache
    };

    return from(
      new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, options);
      })
    ).pipe(
      map((position: GeolocationPosition) => ({
        success: true,
        coordinates: {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy
        }
      })),
      catchError((error: GeolocationPositionError) => {
        let errorMessage = 'Erreur de géolocalisation';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'L\'accès à la géolocalisation a été refusé';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Les informations de localisation ne sont pas disponibles';
            break;
          case error.TIMEOUT:
            errorMessage = 'La demande de géolocalisation a expiré';
            break;
        }

        return of({
          success: false,
          error: errorMessage
        });
      })
    );
  }
}
