import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, interval } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { User, AdminPermission } from '../models/user.model';
import { AuthService } from './auth.service';

export interface SecurityEvent {
  id: number;
  userId: number;
  action: string;
  resource: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
  details?: any;
}

export interface SessionInfo {
  sessionId: string;
  userId: number;
  ipAddress: string;
  userAgent: string;
  loginTime: Date;
  lastActivity: Date;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class AdminSecurityService {
  private apiUrl = `${environment.apiUrl}/admin/security`;
  private sessionCheckInterval = 5 * 60 * 1000; // 5 minutes
  private sessionWarningTime = 2 * 60 * 1000; // 2 minutes avant expiration
  
  private sessionWarning$ = new BehaviorSubject<boolean>(false);
  private sessionTimer: any;

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {
    this.initSessionMonitoring();
  }

  // Vérification des permissions (Observable)
  hasPermission(permission: AdminPermission): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => {
        if (!user) return false;
        if (user.isSuperAdmin) return true;
        return user.permissions?.includes(permission) || false;
      })
    );
  }

  // Vérification synchrone des permissions
  hasPermissionSync(permission: AdminPermission): boolean {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) return false;
    if (currentUser.isSuperAdmin) return true;
    return currentUser.permissions?.includes(permission) || false;
  }

  // Vérification multiple de permissions
  hasAnyPermission(permissions: AdminPermission[]): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => {
        if (!user) return false;
        if (user.isSuperAdmin) return true;
        return permissions.some(permission => 
          user.permissions?.includes(permission) || false
        );
      })
    );
  }

  // Vérification de toutes les permissions
  hasAllPermissions(permissions: AdminPermission[]): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => {
        if (!user) return false;
        if (user.isSuperAdmin) return true;
        return permissions.every(permission => 
          user.permissions?.includes(permission) || false
        );
      })
    );
  }

  // Enregistrement d'événements de sécurité
  logSecurityEvent(action: string, resource: string, details?: any): Observable<SecurityEvent> {
    const event = {
      action,
      resource,
      details,
      timestamp: new Date()
    };

    return this.http.post<SecurityEvent>(`${this.apiUrl}/events`, event).pipe(
      tap(event => console.log('🔒 Security event logged:', event)),
      catchError(error => {
        console.error('❌ Failed to log security event:', error);
        throw error;
      })
    );
  }

  // Récupération des événements de sécurité
  getSecurityEvents(page: number = 0, size: number = 20): Observable<any> {
    return this.http.get(`${this.apiUrl}/events?page=${page}&size=${size}`);
  }

  // Récupération des sessions actives
  getActiveSessions(): Observable<SessionInfo[]> {
    return this.http.get<SessionInfo[]>(`${this.apiUrl}/sessions`);
  }

  // Révocation d'une session
  revokeSession(sessionId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/sessions/${sessionId}`).pipe(
      tap(() => this.logSecurityEvent('SESSION_REVOKED', 'SESSION', { sessionId }))
    );
  }

  // Révocation de toutes les sessions d'un utilisateur
  revokeUserSessions(userId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/users/${userId}/sessions`).pipe(
      tap(() => this.logSecurityEvent('ALL_SESSIONS_REVOKED', 'USER', { userId }))
    );
  }

  // Verrouillage d'un compte utilisateur
  lockUserAccount(userId: number, reason: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/users/${userId}/lock`, { reason }).pipe(
      tap(() => this.logSecurityEvent('ACCOUNT_LOCKED', 'USER', { userId, reason }))
    );
  }

  // Déverrouillage d'un compte utilisateur
  unlockUserAccount(userId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/users/${userId}/unlock`, {}).pipe(
      tap(() => this.logSecurityEvent('ACCOUNT_UNLOCKED', 'USER', { userId }))
    );
  }

  // Réinitialisation forcée du mot de passe
  forcePasswordReset(userId: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/users/${userId}/force-password-reset`, {}).pipe(
      tap(() => this.logSecurityEvent('PASSWORD_RESET_FORCED', 'USER', { userId }))
    );
  }

  // Activation/Désactivation de la 2FA
  toggle2FA(userId: number, enabled: boolean): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/users/${userId}/2fa`, { enabled }).pipe(
      tap(() => this.logSecurityEvent('2FA_TOGGLED', 'USER', { userId, enabled }))
    );
  }

  // Monitoring de session
  private initSessionMonitoring(): void {
    // Vérifier la session toutes les 5 minutes
    interval(this.sessionCheckInterval).subscribe(() => {
      this.checkSessionValidity();
    });
  }

  private checkSessionValidity(): void {
    this.authService.currentUser$.subscribe(user => {
      if (user && user.sessionTimeout) {
        const now = new Date().getTime();
        const lastActivity = user.lastLoginAt ? new Date(user.lastLoginAt).getTime() : now;
        const sessionExpiry = lastActivity + (user.sessionTimeout * 1000);
        const timeUntilExpiry = sessionExpiry - now;

        // Avertir 2 minutes avant expiration
        if (timeUntilExpiry <= this.sessionWarningTime && timeUntilExpiry > 0) {
          this.sessionWarning$.next(true);
        }

        // Session expirée
        if (timeUntilExpiry <= 0) {
          this.handleSessionExpiry();
        }
      }
    });
  }

  private handleSessionExpiry(): void {
    console.warn('🔒 Session expired - logging out user');
    this.logSecurityEvent('SESSION_EXPIRED', 'SESSION');
    this.authService.logout();
  }

  // Observables publics
  get sessionWarning(): Observable<boolean> {
    return this.sessionWarning$.asObservable();
  }

  // Prolonger la session
  extendSession(): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/extend-session`, {}).pipe(
      tap(() => {
        this.sessionWarning$.next(false);
        this.logSecurityEvent('SESSION_EXTENDED', 'SESSION');
      })
    );
  }

  // Validation de l'adresse IP
  validateIPAccess(ipAddress: string): Observable<boolean> {
    return this.http.post<{ allowed: boolean }>(`${this.apiUrl}/validate-ip`, { ipAddress }).pipe(
      map(response => response.allowed)
    );
  }

  // Récupération des tentatives de connexion suspectes
  getSuspiciousLoginAttempts(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/suspicious-logins`);
  }

  // Blocage d'une adresse IP
  blockIPAddress(ipAddress: string, reason: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/block-ip`, { ipAddress, reason }).pipe(
      tap(() => this.logSecurityEvent('IP_BLOCKED', 'SECURITY', { ipAddress, reason }))
    );
  }

  // Déblocage d'une adresse IP
  unblockIPAddress(ipAddress: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/block-ip/${ipAddress}`).pipe(
      tap(() => this.logSecurityEvent('IP_UNBLOCKED', 'SECURITY', { ipAddress }))
    );
  }
}