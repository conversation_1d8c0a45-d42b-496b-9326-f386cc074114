import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from '../../environments/environment';

export interface ResultFile {
  id: number;
  fileName: string;
  fileSize: number;
  uploadDate: Date;
  fileType: string;
  downloadUrl?: string;
}

export interface AppointmentResult {
  id: number;
  appointmentId: number;
  files: ResultFile[];
  comments: string;
  uploadDate: Date;
  nurseId: number;
  nurseName: string;
}

@Injectable({
  providedIn: 'root'
})
export class ResultsService {
  private apiUrl = `${environment.apiUrl}/api/results`;

  constructor(private http: HttpClient) {}

  // Récupérer les résultats d'un rendez-vous
  getAppointmentResults(appointmentId: number): Observable<AppointmentResult> {
    return this.http.get<AppointmentResult>(`${this.apiUrl}/appointment/${appointmentId}/status`);
  }

  // Télécharger le PDF des résultats
  downloadResultFile(appointmentId: number, fileName: string): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/appointment/${appointmentId}/download`, {
      responseType: 'blob'
    });
  }

  // Publier les résultats et envoyer l'email avec PDF
  publishResults(appointmentId: number, comments: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/appointment/${appointmentId}/publish`, {
      comments: comments
    });
  }

  // Renvoyer l'email avec les résultats
  resendResultsEmail(appointmentId: number, comments: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/appointment/${appointmentId}/resend-email`, {
      comments: comments
    });
  }

  // Uploader des résultats (pour l'infirmier) - garde la simulation pour l'instant
  uploadResults(appointmentId: number, files: File[], comments: string): Observable<AppointmentResult> {
    const formData = new FormData();
    formData.append('appointmentId', appointmentId.toString());
    formData.append('comments', comments);

    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });

    // TODO: Implémenter l'upload réel plus tard
    return this.simulateUploadResults(appointmentId, files, comments);
  }

  // Simulation pour le développement
  private simulateGetResults(appointmentId: number): Observable<AppointmentResult> {
    const mockResult: AppointmentResult = {
      id: 1,
      appointmentId: appointmentId,
      files: [
        {
          id: 1,
          fileName: 'Analyse_Sanguine_Patient_' + appointmentId + '.pdf',
          fileSize: 1024 * 1024 * 2.5, // 2.5 MB
          uploadDate: new Date(),
          fileType: 'application/pdf',
          downloadUrl: 'assets/mock-results/sample-blood-test.pdf'
        },
        {
          id: 2,
          fileName: 'Rapport_Medical_' + appointmentId + '.pdf',
          fileSize: 1024 * 1024 * 1.8, // 1.8 MB
          uploadDate: new Date(),
          fileType: 'application/pdf',
          downloadUrl: 'assets/mock-results/sample-medical-report.pdf'
        }
      ],
      comments: 'Résultats normaux. Tous les paramètres sont dans les valeurs de référence. Aucune anomalie détectée.',
      uploadDate: new Date(),
      nurseId: 1,
      nurseName: 'Marie Dubois'
    };

    return of(mockResult);
  }

  private simulateDownloadFile(fileId: number, fileName: string): Observable<Blob> {
    // Créer un PDF factice pour la démonstration
    const pdfContent = this.generateMockPDF(fileName);
    const blob = new Blob([pdfContent], { type: 'application/pdf' });
    return of(blob);
  }

  private simulateUploadResults(appointmentId: number, files: File[], comments: string): Observable<AppointmentResult> {
    const mockResult: AppointmentResult = {
      id: Date.now(),
      appointmentId: appointmentId,
      files: files.map((file, index) => ({
        id: Date.now() + index,
        fileName: file.name,
        fileSize: file.size,
        uploadDate: new Date(),
        fileType: file.type,
        downloadUrl: URL.createObjectURL(file)
      })),
      comments: comments,
      uploadDate: new Date(),
      nurseId: 1,
      nurseName: 'Marie Dubois'
    };

    return of(mockResult);
  }

  private generateMockPDF(fileName: string): string {
    // Contenu PDF factice (en réalité, ceci devrait être un vrai PDF)
    return `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(${fileName}) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000369 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
466
%%EOF`;
  }

  // Méthode utilitaire pour formater la taille des fichiers
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Méthode pour déclencher le téléchargement d'un fichier
  triggerDownload(blob: Blob, fileName: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}
