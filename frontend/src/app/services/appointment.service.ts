import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { Appointment, AppointmentCreate, AppointmentUpdate, AnalysisType } from '../models/appointment.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class AppointmentService {
  private apiUrl = `${environment.apiUrl}/appointments`;
  private analysisUrl = `${environment.apiUrl}/analysis-types`;

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getToken();
    console.log('🔐 AppointmentService - Création des headers avec token:', !!token);

    if (token) {
      console.log('🎫 AppointmentService - Token:', token.substring(0, 50) + '...');
      return new HttpHeaders({
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      });
    } else {
      console.log('❌ AppointmentService - Aucun token trouvé');
      return new HttpHeaders({
        'Content-Type': 'application/json'
      });
    }
  }

  createAppointment(appointment: AppointmentCreate): Observable<Appointment> {
    return this.http.post<Appointment>(this.apiUrl, appointment);
  }

  getAppointmentById(id: number): Observable<Appointment> {
    return this.http.get<Appointment>(`${this.apiUrl}/${id}`);
  }

  getMyAppointments(): Observable<Appointment[]> {
    console.log('📞 AppointmentService.getMyAppointments() - Début de l\'appel');
    const token = this.authService.getToken();
    console.log('🎫 AppointmentService.getMyAppointments() - Token présent:', !!token);

    return this.http.get<Appointment[]>(`${this.apiUrl}/my-appointments`);
  }

  getUpcomingAppointments(): Observable<Appointment[]> {
    return this.http.get<Appointment[]>(`${this.apiUrl}/upcoming`);
  }

  getUrgentAppointments(): Observable<Appointment[]> {
    return this.http.get<Appointment[]>(`${this.apiUrl}/urgent`);
  }

  getNurseAppointments(): Observable<Appointment[]> {
    return this.http.get<Appointment[]>(`${this.apiUrl}/nurse-appointments`);
  }

  updateAppointmentStatus(id: number, status: string): Observable<Appointment> {
    return this.http.put<Appointment>(`${this.apiUrl}/${id}/status`, { status });
  }

  // Nouvelle méthode pour mettre à jour le statut ET le partage de position
  updateAppointmentStatusAndSharing(id: number, status: string, locationSharingEnabled: boolean): Observable<Appointment> {
    return this.http.put<Appointment>(`${this.apiUrl}/${id}/status-and-sharing`, {
      status,
      locationSharingEnabled
    });
  }

  startLocationTracking(id: number): Observable<Appointment> {
    return this.http.post<Appointment>(`${this.apiUrl}/${id}/start-tracking`, {});
  }

  updateNurseLocation(id: number, latitude: number, longitude: number): Observable<Appointment> {
    return this.http.put<Appointment>(`${this.apiUrl}/${id}/update-location`, { latitude, longitude });
  }

  stopLocationTracking(id: number): Observable<Appointment> {
    return this.http.post<Appointment>(`${this.apiUrl}/${id}/stop-tracking`, {});
  }

  updateAppointment(id: number, update: AppointmentUpdate): Observable<Appointment> {
    return this.http.put<Appointment>(`${this.apiUrl}/${id}`, update);
  }

  assignNurse(appointmentId: number, nurseId: number): Observable<Appointment> {
    return this.http.put<Appointment>(`${this.apiUrl}/${appointmentId}/assign-nurse/${nurseId}`, {});
  }

  cancelAppointment(id: number, reason: string): Observable<any> {
    return this.http.put(`${this.apiUrl}/${id}/cancel`, null, {
      params: { reason }
    });
  }

  getAnalysisTypes(): Observable<AnalysisType[]> {
    return this.http.get<AnalysisType[]>(`${this.analysisUrl}`);
  }

  getActiveAnalysisTypes(): Observable<AnalysisType[]> {
    return this.http.get<AnalysisType[]>(`${this.analysisUrl}/active`);
  }
}
