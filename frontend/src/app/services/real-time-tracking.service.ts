import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, interval, Subscription } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';

export interface NursePosition {
  nurseId: number;
  appointmentId: number;
  latitude: number;
  longitude: number;
  timestamp: Date;
  accuracy: number;
  heading?: number;
  speed?: number;
  status: 'ON_WAY' | 'ARRIVED' | 'OFFLINE';
}

export interface TrackingUpdate {
  appointmentId: number;
  nursePosition: NursePosition;
  estimatedArrival: Date;
  distanceRemaining: number;
}

@Injectable({
  providedIn: 'root'
})
export class RealTimeTrackingService {
  private apiUrl = `${environment.apiUrl}/tracking`;
  
  // Observables pour le suivi en temps réel
  private nursePositionSubject = new BehaviorSubject<NursePosition | null>(null);
  private trackingUpdatesSubject = new BehaviorSubject<TrackingUpdate | null>(null);
  
  // Subscriptions pour la gestion
  private positionWatchId: number | null = null;
  private trackingInterval: Subscription | null = null;
  private isTracking = false;

  constructor(private http: HttpClient) {}

  // Observable pour que les patients suivent la position de l'infirmier
  getNursePosition(): Observable<NursePosition | null> {
    return this.nursePositionSubject.asObservable();
  }

  // Observable pour les mises à jour de suivi
  getTrackingUpdates(): Observable<TrackingUpdate | null> {
    return this.trackingUpdatesSubject.asObservable();
  }

  // Démarrer le suivi pour un infirmier
  startNurseTracking(appointmentId: number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      console.log('🚀 Démarrage du suivi infirmier pour RDV:', appointmentId);
      
      if (!navigator.geolocation) {
        console.error('❌ Géolocalisation non supportée');
        reject(new Error('Géolocalisation non supportée'));
        return;
      }

      // Options de géolocalisation haute précision
      const options: PositionOptions = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 5000 // 5 secondes max pour le cache
      };

      // Démarrer le suivi de position
      this.positionWatchId = navigator.geolocation.watchPosition(
        (position) => {
          const nursePosition: NursePosition = {
            nurseId: this.getCurrentNurseId(),
            appointmentId: appointmentId,
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            timestamp: new Date(),
            accuracy: position.coords.accuracy,
            heading: position.coords.heading || undefined,
            speed: position.coords.speed || undefined,
            status: 'ON_WAY'
          };

          console.log('📍 Position infirmier mise à jour:', nursePosition);
          
          // Envoyer la position au serveur
          this.sendPositionUpdate(nursePosition);
          
          // Mettre à jour l'observable local
          this.nursePositionSubject.next(nursePosition);
          
          this.isTracking = true;
          resolve(true);
        },
        (error) => {
          console.error('❌ Erreur de géolocalisation:', error);
          this.isTracking = false;
          reject(error);
        },
        options
      );

      // Démarrer l'intervalle de mise à jour
      this.startTrackingInterval(appointmentId);
    });
  }

  // Arrêter le suivi
  stopNurseTracking(): void {
    console.log('🛑 Arrêt du suivi infirmier');
    
    if (this.positionWatchId !== null) {
      navigator.geolocation.clearWatch(this.positionWatchId);
      this.positionWatchId = null;
    }

    if (this.trackingInterval) {
      this.trackingInterval.unsubscribe();
      this.trackingInterval = null;
    }

    this.isTracking = false;
    this.nursePositionSubject.next(null);
    this.trackingUpdatesSubject.next(null);
  }

  // Marquer l'infirmier comme arrivé
  markAsArrived(appointmentId: number): Observable<any> {
    const currentPosition = this.nursePositionSubject.value;
    if (currentPosition) {
      currentPosition.status = 'ARRIVED';
      this.nursePositionSubject.next(currentPosition);
    }

    return this.http.post(`${this.apiUrl}/arrived`, { appointmentId });
  }

  // Obtenir la position actuelle d'un infirmier (pour les patients)
  getNursePositionForAppointment(appointmentId: number): Observable<NursePosition> {
    return this.http.get<NursePosition>(`${this.apiUrl}/nurse-position/${appointmentId}`);
  }

  // Démarrer le suivi côté patient
  startPatientTracking(appointmentId: number): void {
    console.log('👁️ Démarrage du suivi patient pour RDV:', appointmentId);
    
    // Polling toutes les 10 secondes pour récupérer la position de l'infirmier
    this.trackingInterval = interval(10000).subscribe(() => {
      this.getNursePositionForAppointment(appointmentId).subscribe({
        next: (position) => {
          console.log('📍 Position infirmier reçue:', position);
          this.nursePositionSubject.next(position);
          
          // Calculer les informations de suivi
          this.calculateTrackingInfo(appointmentId, position);
        },
        error: (error) => {
          console.error('❌ Erreur récupération position:', error);
        }
      });
    });
  }

  // Vérifier si le suivi est actif
  isTrackingActive(): boolean {
    return this.isTracking;
  }

  // Méthodes privées
  private startTrackingInterval(appointmentId: number): void {
    // Envoyer des mises à jour toutes les 30 secondes
    this.trackingInterval = interval(30000).subscribe(() => {
      if (this.isTracking) {
        console.log('🔄 Mise à jour périodique du suivi');
        // La position est déjà mise à jour par watchPosition
      }
    });
  }

  private sendPositionUpdate(position: NursePosition): void {
    this.http.post(`${this.apiUrl}/update-position`, position).subscribe({
      next: (response) => {
        console.log('✅ Position envoyée au serveur:', response);
      },
      error: (error) => {
        console.error('❌ Erreur envoi position:', error);
      }
    });
  }

  private calculateTrackingInfo(appointmentId: number, nursePosition: NursePosition): void {
    // Ici on pourrait calculer la distance restante et l'heure d'arrivée estimée
    // Pour l'instant, on simule ces données
    const trackingUpdate: TrackingUpdate = {
      appointmentId: appointmentId,
      nursePosition: nursePosition,
      estimatedArrival: new Date(Date.now() + 15 * 60 * 1000), // +15 minutes
      distanceRemaining: Math.random() * 5 // Distance aléatoire pour demo
    };

    this.trackingUpdatesSubject.next(trackingUpdate);
  }

  private getCurrentNurseId(): number {
    // Récupérer l'ID de l'infirmier connecté
    const user = JSON.parse(localStorage.getItem('currentUser') || '{}');
    return user.id || 0;
  }
}
