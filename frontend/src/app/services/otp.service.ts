import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface OtpVerificationRequest {
  email: string;
  otpCode: string;
}

export interface ResendOtpRequest {
  email: string;
}

export interface OtpResponse {
  message: string;
}

@Injectable({
  providedIn: 'root'
})
export class OtpService {
  private apiUrl = `${environment.apiUrl}/auth/otp`;

  constructor(private http: HttpClient) {}

  /**
   * Vérifie le code OTP pour l'email
   */
  verifyEmailOtp(email: string, otpCode: string): Observable<OtpResponse> {
    const request: OtpVerificationRequest = { email, otpCode };
    return this.http.post<OtpResponse>(`${this.apiUrl}/verify-email`, request);
  }

  /**
   * Renvoie un code OTP de vérification email
   */
  resendEmailOtp(email: string): Observable<OtpResponse> {
    const request: ResendOtpRequest = { email };
    return this.http.post<OtpResponse>(`${this.apiUrl}/resend-email`, request);
  }
}
