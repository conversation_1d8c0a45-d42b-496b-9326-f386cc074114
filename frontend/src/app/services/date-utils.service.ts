import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DateUtilsService {

  constructor() { }

  formatDateForBackend(date: Date): string {
    if (!date || !(date instanceof Date)) {
      return '';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    // Créer une chaîne ISO locale (sans 'Z' pour éviter la conversion UTC)
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  }

  /**
   * Parse une date venant du backend en tenant compte du fuseau horaire local
   */
  parseDateFromBackend(dateString: string | Date): Date {
    if (!dateString) {
      return new Date();
    }

    if (dateString instanceof Date) {
      return dateString;
    }

    // Si la date vient du backend sans timezone, on la traite comme locale
    if (typeof dateString === 'string' && !dateString.includes('Z') && !dateString.includes('+')) {
      // Ajouter 'T' si ce n'est qu'une date
      const isoString = dateString.includes('T') ? dateString : `${dateString}T00:00:00`;
      return new Date(isoString);
    }

    return new Date(dateString);
  }

  /**
   * Formate une date pour l'affichage (format français)
   */
  formatDateTime(date: string | Date): string {
    if (!date) return '';
    
    const dateObj = this.parseDateFromBackend(date);
    return dateObj.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Formate une date courte pour l'affichage
   */
  formatDate(date: string | Date): string {
    if (!date) return '';
    
    const dateObj = this.parseDateFromBackend(date);
    return dateObj.toLocaleDateString('fr-FR', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  /**
   * Formate seulement l'heure
   */
  formatTime(date: string | Date): string {
    if (!date) return '';
    
    const dateObj = this.parseDateFromBackend(date);
    return dateObj.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Formate une date pour les inputs de type datetime-local
   */
  formatForDateTimeInput(date: Date): string {
    if (!date || !(date instanceof Date)) {
      return '';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }

  /**
   * Vérifie si une date est aujourd'hui
   */
  isToday(date: string | Date): boolean {
    if (!date) return false;
    
    const dateObj = this.parseDateFromBackend(date);
    const today = new Date();
    
    return dateObj.getDate() === today.getDate() &&
           dateObj.getMonth() === today.getMonth() &&
           dateObj.getFullYear() === today.getFullYear();
  }

  /**
   * Calcule la différence en minutes entre deux dates
   */
  getMinutesDifference(date1: string | Date, date2: string | Date): number {
    const d1 = this.parseDateFromBackend(date1);
    const d2 = this.parseDateFromBackend(date2);
    
    return Math.abs(d2.getTime() - d1.getTime()) / (1000 * 60);
  }
}
