import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, interval, Subscription } from 'rxjs';
import { environment } from '../../environments/environment';

export interface NurseLocationUpdate {
  nurseId: number;
  appointmentId: number;
  latitude: number;
  longitude: number;
  accuracy: number;
  speed?: number;
  heading?: number;
  timestamp: string;
}

@Injectable({
  providedIn: 'root'
})
export class NurseTrackingService {
  private apiUrl = `${environment.apiUrl}/tracking`;
  private isTracking = false;
  private trackingSubscription?: Subscription;
  private watchId?: number;
  
  // Observable pour l'état du tracking
  private trackingStatus = new BehaviorSubject<boolean>(false);
  public trackingStatus$ = this.trackingStatus.asObservable();

  constructor(private http: HttpClient) {}

  // Démarrer le suivi de position pour un infirmier
  startTracking(nurseId: number, appointmentId: number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      console.log('🎯 Démarrage du suivi pour infirmier:', nurseId, 'RDV:', appointmentId);

      if (!navigator.geolocation) {
        console.error('❌ Géolocalisation non supportée');
        reject(new Error('Géolocalisation non supportée'));
        return;
      }

      // Options de géolocalisation optimisées pour le suivi
      const options: PositionOptions = {
        enableHighAccuracy: true,
        timeout: 30000,
        maximumAge: 5000 // Position pas plus vieille que 5 secondes
      };

      // Démarrer le suivi continu
      this.watchId = navigator.geolocation.watchPosition(
        (position) => {
          console.log('📍 Nouvelle position reçue:', position);
          this.sendLocationUpdate(nurseId, appointmentId, position);
        },
        (error) => {
          console.error('❌ Erreur géolocalisation:', error);
          this.handleLocationError(error);
        },
        options
      );

      if (this.watchId) {
        this.isTracking = true;
        this.trackingStatus.next(true);
        
        // Envoyer une position toutes les 10 secondes même si pas de changement
        this.trackingSubscription = interval(10000).subscribe(() => {
          if (this.isTracking) {
            navigator.geolocation.getCurrentPosition(
              (position) => this.sendLocationUpdate(nurseId, appointmentId, position),
              (error) => console.warn('⚠️ Position périodique échouée:', error),
              options
            );
          }
        });

        console.log('✅ Suivi démarré avec succès');
        resolve(true);
      } else {
        reject(new Error('Impossible de démarrer le suivi'));
      }
    });
  }

  // Arrêter le suivi de position
  stopTracking(): void {
    console.log('⏹️ Arrêt du suivi...');
    
    if (this.watchId) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = undefined;
    }

    if (this.trackingSubscription) {
      this.trackingSubscription.unsubscribe();
      this.trackingSubscription = undefined;
    }

    this.isTracking = false;
    this.trackingStatus.next(false);
    console.log('✅ Suivi arrêté');
  }

  // Envoyer la mise à jour de position au backend
  private async sendLocationUpdate(nurseId: number, appointmentId: number, position: GeolocationPosition): Promise<void> {
    try {
      const locationUpdate: NurseLocationUpdate = {
        nurseId,
        appointmentId,
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        speed: position.coords.speed || 0,
        heading: position.coords.heading || 0,
        timestamp: new Date().toISOString()
      };

      console.log('📤 Envoi position:', locationUpdate);

      const response = await fetch(`${this.apiUrl}/update-position`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(locationUpdate)
      });

      if (response.ok) {
        console.log('✅ Position envoyée avec succès');
      } else {
        console.error('❌ Erreur envoi position:', response.status);
      }
    } catch (error) {
      console.error('❌ Erreur réseau envoi position:', error);
    }
  }

  // Gérer les erreurs de géolocalisation
  private handleLocationError(error: GeolocationPositionError): void {
    let errorMessage = '';
    
    switch (error.code) {
      case error.PERMISSION_DENIED:
        errorMessage = 'Permission de géolocalisation refusée';
        break;
      case error.POSITION_UNAVAILABLE:
        errorMessage = 'Position non disponible';
        break;
      case error.TIMEOUT:
        errorMessage = 'Timeout de géolocalisation';
        break;
      default:
        errorMessage = 'Erreur inconnue de géolocalisation';
    }
    
    console.error('❌ Erreur géolocalisation:', errorMessage);
  }

  // Vérifier si le suivi est actif
  isTrackingActive(): boolean {
    return this.isTracking;
  }

  // Obtenir une position unique (pour test)
  getCurrentPosition(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        resolve,
        reject,
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 0
        }
      );
    });
  }
}
