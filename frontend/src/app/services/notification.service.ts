import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, catchError, throwError } from 'rxjs';
import { environment } from '../../environments/environment';
import { Notification } from '../models/notification.model';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiUrl = `${environment.apiUrl}/notifications`;
  private unreadCountSubject = new BehaviorSubject<number>(0);
  public unreadCount$ = this.unreadCountSubject.asObservable();

  constructor(private http: HttpClient) {}

  getNotifications(): Observable<Notification[]> {
    console.log('🔔 NotificationService - Récupération des notifications depuis:', this.apiUrl);
    return this.http.get<Notification[]>(this.apiUrl).pipe(
      catchError(error => {
        console.error('❌ Error loading notifications:', error);
        console.error('❌ Error details:', error);
        return throwError(() => error);
      })
    );
  }

  getUnreadNotifications(): Observable<Notification[]> {
    console.log('🔔 NotificationService - Récupération des notifications non lues');
    return this.http.get<Notification[]>(`${this.apiUrl}/unread`).pipe(
      catchError(error => {
        console.error('❌ Error loading unread notifications:', error);
        return throwError(() => error);
      })
    );
  }

  getUnreadCount(): Observable<number> {
    console.log('🔔 NotificationService - Récupération du nombre de notifications non lues');
    return this.http.get<number>(`${this.apiUrl}/unread/count`).pipe(
      catchError(error => {
        console.error('❌ Error loading unread count:', error);
        return throwError(() => error);
      })
    );
  }

  markAsRead(notificationId: number): Observable<Notification> {
    return this.http.put<Notification>(`${this.apiUrl}/${notificationId}/read`, {}).pipe(
      catchError(error => {
        console.error('❌ Error marking notification as read:', error);
        return throwError(() => error);
      })
    );
  }

  markAllAsRead(): Observable<any> {
    return this.http.put(`${this.apiUrl}/read-all`, {}).pipe(
      catchError(error => {
        console.error('❌ Error marking all notifications as read:', error);
        return throwError(() => error);
      })
    );
  }

  updateUnreadCount(): void {
    console.log('🔔 NotificationService - Mise à jour du compteur de notifications non lues');
    this.getUnreadCount().subscribe({
      next: count => {
        console.log('✅ NotificationService - Compteur mis à jour:', count);
        this.unreadCountSubject.next(count);
      },
      error: error => {
        console.error('❌ NotificationService - Erreur lors de la mise à jour du compteur:', error);
        // En cas d'erreur, on garde le compteur à 0
        this.unreadCountSubject.next(0);
      }
    });
  }

  // Method to be called when a new notification is received via WebSocket
  onNewNotification(): void {
    this.updateUnreadCount();
  }
}
