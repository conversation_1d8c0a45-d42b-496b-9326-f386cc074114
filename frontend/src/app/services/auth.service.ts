import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { environment } from '../../environments/environment';
import { LoginRequest, LoginResponse, User, UserRegistration } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiUrl = `${environment.apiUrl}/auth`;
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(private http: HttpClient) {
    // Check if user is already logged in
    const token = this.getToken();
    if (token) {
      // Decode token to get user info (simplified)
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        if (payload.exp * 1000 > Date.now()) {
          // Token is still valid, essayer de récupérer l'utilisateur depuis localStorage
          const savedUser = this.getSavedUser();
          if (savedUser) {
            this.currentUserSubject.next(savedUser);
            console.log('AuthService - Utilisateur récupéré depuis localStorage:', savedUser);
          } else {
            // Sinon, essayer l'endpoint profile
            this.getUserProfile().subscribe({
              next: (user) => {
                console.log('AuthService - Profil utilisateur récupéré depuis API:', user);
              },
              error: (error) => {
                console.log('AuthService - Erreur récupération profil, utilisation du token:', error);
                // Si l'endpoint profile n'existe pas, créer un utilisateur basique depuis le token
                const basicUser: User = {
                  id: 0,
                  username: payload.sub,
                  email: payload.sub + '@example.com',
                  firstName: payload.sub,
                  lastName: '',
                  role: 'PATIENT' as any,
                  enabled: true,
                  createdAt: new Date(),
                  updatedAt: new Date()
                };
                this.currentUserSubject.next(basicUser);
                console.log('AuthService - Utilisateur basique créé depuis le token:', basicUser);
              }
            });
          }
        } else {
          this.logout();
        }
      } catch (error) {
        this.logout();
      }
    }
  }

  login(credentials: LoginRequest): Observable<LoginResponse> {
    console.log('AuthService - Tentative de connexion pour:', credentials.username);
    return this.http.post<LoginResponse>(`${this.apiUrl}/login`, credentials)
      .pipe(
        tap(response => {
          console.log('AuthService - Réponse de connexion:', response);
          if (typeof localStorage !== 'undefined') {
            localStorage.setItem('token', response.token);
            console.log('AuthService - Token sauvegardé');
          }
          const user: User = {
            id: response.id,
            username: response.username,
            email: response.email,
            firstName: response.firstName,
            lastName: response.lastName,
            role: response.role,
            enabled: true,
            createdAt: new Date(),
            updatedAt: new Date(),
            isSuperAdmin: response.isSuperAdmin || false
          };

          // Sauvegarder aussi l'utilisateur dans localStorage
          if (typeof localStorage !== 'undefined') {
            localStorage.setItem('currentUser', JSON.stringify(user));
            console.log('AuthService - Utilisateur sauvegardé dans localStorage');
          }

          this.currentUserSubject.next(user);
          console.log('AuthService - Utilisateur défini:', user);
        })
      );
  }

  register(userData: UserRegistration): Observable<any> {
    return this.http.post(`${environment.apiUrl}/users/register`, userData);
  }

  logout(): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('token');
      localStorage.removeItem('currentUser');
    }
    this.currentUserSubject.next(null);
  }

  /**
   * Retourne l'URL de redirection selon le rôle de l'utilisateur
   */
  getDashboardUrlByRole(user: User): string {
    switch (user.role) {
      case 'ADMIN':
        return '/dashboard/admin-appointments';
      case 'NURSE':
        return '/dashboard/nurse-dashboard';
      case 'PATIENT':
        return '/dashboard/appointments'; // Dashboard patient avec ses rendez-vous
      default:
        return '/dashboard/appointments';
    }
  }

  /**
   * Retourne le label du rôle en français
   */
  getRoleLabel(role: string): string {
    switch (role) {
      case 'ADMIN':
        return 'Administrateur';
      case 'NURSE':
        return 'Infirmier';
      case 'PATIENT':
        return 'Patient';
      default:
        return 'Utilisateur';
    }
  }

  private getSavedUser(): User | null {
    if (typeof localStorage !== 'undefined') {
      const savedUser = localStorage.getItem('currentUser');
      if (savedUser) {
        try {
          const user = JSON.parse(savedUser);

          // Si c'est un admin sans isSuperAdmin défini, on le met à true par défaut
          if (user.role === 'ADMIN' && user.isSuperAdmin === undefined) {
            user.isSuperAdmin = true;
            localStorage.setItem('currentUser', JSON.stringify(user));
            console.log('AuthService - Admin mis à jour avec isSuperAdmin: true');
          }

          return user;
        } catch (error) {
          console.error('Erreur lors du parsing de l\'utilisateur sauvegardé:', error);
          localStorage.removeItem('currentUser');
        }
      }
    }
    return null;
  }

  getUserProfile(): Observable<User> {
    return this.http.get<User>(`${environment.apiUrl}/users/profile`)
      .pipe(
        tap(user => this.currentUserSubject.next(user))
      );
  }

  updateUserProfile(updateData: any): Observable<User> {
    return this.http.put<User>(`${environment.apiUrl}/users/profile`, updateData)
      .pipe(
        tap(user => {
          this.currentUserSubject.next(user);
          // Mettre à jour aussi dans localStorage
          if (typeof localStorage !== 'undefined') {
            localStorage.setItem('currentUser', JSON.stringify(user));
          }
        })
      );
  }

  changePassword(passwordData: { currentPassword: string, newPassword: string }): Observable<any> {
    return this.http.put(`${environment.apiUrl}/users/change-password`, passwordData);
  }

  getToken(): string | null {
    if (typeof localStorage !== 'undefined') {
      return localStorage.getItem('token');
    }
    return null;
  }

  isLoggedIn(): boolean {
    const token = this.getToken();
    if (!token) return false;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 > Date.now();
    } catch {
      return false;
    }
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user ? user.role === role : false;
  }

  // Méthodes de vérification d'authentification et de rôles
  isAuthenticated(): boolean {
    return this.isLoggedIn();
  }

  isPatient(): boolean {
    return this.hasRole('PATIENT');
  }

  isNurse(): boolean {
    return this.hasRole('NURSE');
  }

  isAdmin(): boolean {
    return this.hasRole('ADMIN');
  }

  getAuthHeaders(): HttpHeaders {
    const token = this.getToken();
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });
  }
}
