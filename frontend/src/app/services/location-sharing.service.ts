import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, interval, Subscription } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { GeolocationService, LocationResult } from './geolocation.service';
import { AuthService } from './auth.service';

export interface LocationUpdate {
  appointmentId: number;
  latitude: number;
  longitude: number;
  timestamp: Date;
}

export interface LocationSharingStatus {
  isActive: boolean;
  appointmentId?: number;
  lastUpdate?: Date;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class LocationSharingService {
  private apiUrl = `${environment.apiUrl}/location-sharing`;
  private locationSharingStatus = new BehaviorSubject<LocationSharingStatus>({ isActive: false });
  private locationUpdateSubscription?: Subscription;
  private updateInterval = 5000; // 5 secondes pour un suivi temps réel
  private useSimulation = false; // false = API réelle, true = simulation

  constructor(
    private http: HttpClient,
    private geolocationService: GeolocationService,
    private authService: AuthService
  ) {}

  /**
   * Démarre le partage de position pour un rendez-vous
   */
  startLocationSharing(appointmentId: number): Observable<boolean> {
    console.log('Starting location sharing for appointment:', appointmentId);

    return new Observable(observer => {
      // Vérifier si la géolocalisation est disponible
      if (!navigator.geolocation) {
        observer.error('Géolocalisation non disponible');
        return;
      }

      if (this.useSimulation) {
        // Mode simulation
        console.log('Simulating location sharing start on server for appointment:', appointmentId);

        setTimeout(() => {
          try {
            this.startPeriodicLocationUpdates(appointmentId);
            this.locationSharingStatus.next({
              isActive: true,
              appointmentId: appointmentId,
              lastUpdate: new Date()
            });

            console.log('Location sharing started successfully (simulated)');
            observer.next(true);
            observer.complete();
          } catch (error) {
            console.error('Error starting location sharing:', error);
            this.locationSharingStatus.next({
              isActive: false,
              error: 'Erreur lors du démarrage du partage de position'
            });
            observer.error(error);
          }
        }, 500);
      } else {
        // Mode API réelle
        this.http.post<any>(`${this.apiUrl}/start`, { appointmentId }).subscribe({
          next: (response) => {
            console.log('✅ Location sharing started on server:', response);

            // Démarrer les mises à jour périodiques de position
            this.startPeriodicLocationUpdates(appointmentId);

            // Mettre à jour le statut
            this.locationSharingStatus.next({
              isActive: true,
              appointmentId: appointmentId,
              lastUpdate: new Date()
            });

            observer.next(true);
            observer.complete();
          },
          error: (error) => {
            console.error('❌ Error starting location sharing:', error);
            this.locationSharingStatus.next({
              isActive: false,
              error: 'Erreur lors du démarrage du partage de position'
            });
            observer.error(error);
          }
        });
      }
    });
  }

  /**
   * Arrête le partage de position
   */
  stopLocationSharing(appointmentId: number): Observable<boolean> {
    console.log('Stopping location sharing for appointment:', appointmentId);

    return new Observable(observer => {
      // Arrêter les mises à jour périodiques
      this.stopPeriodicLocationUpdates();

      if (this.useSimulation) {
        // Mode simulation
        console.log('Simulating location sharing stop on server for appointment:', appointmentId);

        setTimeout(() => {
          this.locationSharingStatus.next({ isActive: false });
          console.log('Location sharing stopped successfully (simulated)');
          observer.next(true);
          observer.complete();
        }, 200);
      } else {
        // Mode API réelle
        this.http.post<any>(`${this.apiUrl}/stop`, { appointmentId }).subscribe({
          next: (response) => {
            console.log('✅ Location sharing stopped on server:', response);
            this.locationSharingStatus.next({ isActive: false });
            observer.next(true);
            observer.complete();
          },
          error: (error) => {
            console.error('❌ Error stopping location sharing:', error);
            // Même en cas d'erreur serveur, on arrête localement
            this.locationSharingStatus.next({ isActive: false });
            observer.next(true);
            observer.complete();
          }
        });
      }
    });
  }

  /**
   * Met à jour la position de l'infirmier
   */
  updateNurseLocation(appointmentId: number, latitude: number, longitude: number): Observable<any> {
    // Récupérer l'ID de l'infirmier connecté
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      console.error('❌ Aucun utilisateur connecté');
      return new Observable(observer => observer.error('Utilisateur non connecté'));
    }

    const locationUpdate = {
      nurseId: currentUser.id,
      appointmentId: appointmentId,
      latitude: latitude,
      longitude: longitude,
      accuracy: 10.0,
      speed: 0.0,
      heading: 0.0,
      status: 'ON_WAY'
    };

    console.log('📍 Updating nurse location:', locationUpdate);

    if (this.useSimulation) {
      // Mode simulation
      console.log('Simulating nurse location update');
      return new Observable(observer => {
        setTimeout(() => {
          observer.next({ success: true, message: 'Location updated successfully (simulated)' });
          observer.complete();
        }, 100);
      });
    } else {
      // Mode API réelle - utiliser l'endpoint location-sharing
      return this.http.post<any>(`${this.apiUrl}/update`, locationUpdate);
    }
  }

  /**
   * Récupère la position actuelle de l'infirmier pour un rendez-vous
   */
  getNurseLocation(appointmentId: number): Observable<LocationUpdate> {
    return this.http.get<LocationUpdate>(`${this.apiUrl}/nurse-location/${appointmentId}`);
  }

  /**
   * Démarre les mises à jour périodiques de position
   */
  private startPeriodicLocationUpdates(appointmentId: number): void {
    this.stopPeriodicLocationUpdates(); // Arrêter les précédentes si elles existent

    console.log('🚀 Démarrage du suivi GPS temps réel pour le rendez-vous:', appointmentId);

    // Options de géolocalisation haute précision
    const options: PositionOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0 // Pas de cache
    };

    // Utiliser watchPosition pour un suivi continu en temps réel
    if (navigator.geolocation) {
      this.watchPositionId = navigator.geolocation.watchPosition(
        (position) => {
          console.log('📍 Nouvelle position GPS reçue:', position.coords);

          // Envoyer la position au serveur
          this.updateNurseLocation(
            appointmentId,
            position.coords.latitude,
            position.coords.longitude
          ).subscribe({
            next: (response) => {
              console.log('✅ Position mise à jour avec succès:', response);
              this.locationSharingStatus.next({
                isActive: true,
                appointmentId: appointmentId,
                lastUpdate: new Date()
              });
            },
            error: (error) => {
              console.error('❌ Erreur mise à jour position:', error);
            }
          });
        },
        (error) => {
          console.error('❌ Erreur GPS:', error);
          // Fallback avec interval si watchPosition échoue
          this.startFallbackLocationUpdates(appointmentId);
        },
        options
      );

      console.log('✅ Suivi GPS démarré avec watchId:', this.watchPositionId);
    } else {
      console.warn('⚠️ Géolocalisation non supportée, utilisation du fallback');
      this.startFallbackLocationUpdates(appointmentId);
    }
  }

  /**
   * Méthode de fallback si watchPosition échoue
   */
  private startFallbackLocationUpdates(appointmentId: number): void {
    console.log('⚠️ Utilisation du fallback avec interval pour le suivi');

    this.locationUpdateSubscription = interval(this.updateInterval)
      .pipe(
        switchMap(() => this.geolocationService.getCurrentPosition()),
        catchError((error) => {
          console.error('Error getting current position:', error);
          return [];
        })
      )
      .subscribe({
        next: (location: LocationResult) => {
          if (location.success && location.coordinates) {
            this.updateNurseLocation(appointmentId, location.coordinates.latitude, location.coordinates.longitude)
              .subscribe({
                next: (response) => {
                  console.log('Location updated successfully:', response);
                  this.locationSharingStatus.next({
                    isActive: true,
                    appointmentId: appointmentId,
                    lastUpdate: new Date()
                  });
                },
                error: (error) => {
                  console.error('Error updating location:', error);
                }
              });
          }
        },
        error: (error) => {
          console.error('Error in periodic location updates:', error);
        }
      });
  }

  // Variable pour stocker l'ID du watchPosition
  private watchPositionId: number | null = null;

  /**
   * Arrête les mises à jour périodiques de position
   */
  private stopPeriodicLocationUpdates(): void {
    // Arrêter le suivi GPS watchPosition
    if (this.watchPositionId !== null && navigator.geolocation) {
      console.log('🛑 Arrêt du suivi GPS watchPosition:', this.watchPositionId);
      navigator.geolocation.clearWatch(this.watchPositionId);
      this.watchPositionId = null;
    }

    // Arrêter aussi le fallback interval si actif
    if (this.locationUpdateSubscription) {
      console.log('🛑 Arrêt du suivi par interval');
      this.locationUpdateSubscription.unsubscribe();
      this.locationUpdateSubscription = undefined;
    }
  }

  /**
   * Retourne l'observable du statut de partage de position
   */
  getLocationSharingStatus(): Observable<LocationSharingStatus> {
    return this.locationSharingStatus.asObservable();
  }

  /**
   * Vérifie si le partage de position est actif
   */
  isLocationSharingActive(): boolean {
    return this.locationSharingStatus.value.isActive;
  }

  /**
   * Marque le prélèvement comme effectué et arrête automatiquement le partage
   */
  markSamplingCompleted(appointmentId: number): Observable<boolean> {
    console.log('Marking sampling as completed and stopping location sharing:', appointmentId);

    return new Observable(observer => {
      if (this.useSimulation) {
        // Mode simulation
        console.log('Simulating sampling completion on server for appointment:', appointmentId);

        setTimeout(() => {
          this.stopLocationSharing(appointmentId).subscribe({
            next: (stopped) => {
              console.log('Location sharing stopped automatically after sampling (simulated)');
              observer.next(true);
              observer.complete();
            },
            error: (error) => {
              console.error('Error stopping location sharing after sampling:', error);
              observer.next(true);
              observer.complete();
            }
          });
        }, 300);
      } else {
        // Mode API réelle
        this.http.post<any>(`${this.apiUrl}/sampling-completed`, { appointmentId }).subscribe({
          next: (response) => {
            console.log('✅ Sampling marked as completed on server:', response);

            // Arrêter automatiquement le partage de position
            this.stopLocationSharing(appointmentId).subscribe({
              next: (stopped) => {
                console.log('Location sharing stopped automatically after sampling');
                observer.next(true);
                observer.complete();
              },
              error: (error) => {
                console.error('Error stopping location sharing after sampling:', error);
                observer.next(true);
                observer.complete();
              }
            });
          },
          error: (error) => {
            console.error('❌ Error marking sampling as completed:', error);
            observer.error(error);
          }
        });
      }
    });
  }

  /**
   * Nettoie les ressources lors de la destruction du service
   */
  ngOnDestroy(): void {
    console.log('🧹 Nettoyage des ressources du service de partage de position');
    this.stopPeriodicLocationUpdates();

    // S'assurer que le statut est bien réinitialisé
    this.locationSharingStatus.next({ isActive: false });
  }
}
