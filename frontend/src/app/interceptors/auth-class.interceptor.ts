import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable()
export class AuthClassInterceptor implements HttpInterceptor {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    console.log('🚀 AuthClassInterceptor - APPELÉ pour:', req.url);

    const token = this.authService.getToken();

    console.log('🔒 AuthClassInterceptor - Requête:', req.url);
    console.log('🎫 AuthClassInterceptor - Token présent:', !!token);
    if (token) {
      console.log('🎫 AuthClassInterceptor - Token:', token.substring(0, 50) + '...');
    } else {
      console.log('❌ AuthClassInterceptor - AUCUN TOKEN TROUVÉ !');
    }

    let authReq = req;
    if (token) {
      authReq = req.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`
        }
      });
      console.log('✅ AuthClassInterceptor - Header Authorization ajouté');
    } else {
      console.log('❌ AuthClassInterceptor - Aucun token, pas d\'header Authorization');
    }

    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        console.log('❌ AuthClassInterceptor - Erreur HTTP:', error.status, error.url);
        if (error.status === 401) {
          console.log('🚨 AuthClassInterceptor - Erreur 401, déconnexion forcée');
          this.authService.logout();
          this.router.navigate(['/login']);
        }
        return throwError(() => error);
      })
    );
  }
}
