import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { catchError, throwError } from 'rxjs';
import { AuthService } from '../services/auth.service';

export const AuthInterceptor: HttpInterceptorFn = (req, next) => {
  console.log('🚀 AuthInterceptor - APPELÉ pour:', req.url);

  const authService = inject(AuthService);
  const router = inject(Router);

  const token = authService.getToken();

  console.log('🔒 AuthInterceptor - Requête:', req.url);
  console.log('🎫 AuthInterceptor - Token présent:', !!token);
  if (token) {
    console.log('🎫 AuthInterceptor - Token:', token.substring(0, 50) + '...');
  } else {
    console.log('❌ AuthInterceptor - AUCUN TOKEN TROUVÉ !');
  }

  // Clone the request and add the authorization header if token exists
  let authReq = req;
  if (token) {
    authReq = req.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log('✅ AuthInterceptor - Header Authorization ajouté');
  } else {
    console.log('❌ AuthInterceptor - Aucun token, pas d\'header Authorization');
  }

  return next(authReq).pipe(
    catchError((error: HttpErrorResponse) => {
      console.log('❌ AuthInterceptor - Erreur HTTP:', error.status, error.url);
      if (error.status === 401) {
        console.log('🚨 AuthInterceptor - Erreur 401, déconnexion forcée');
        // Token expired or invalid, logout and redirect to login
        authService.logout();
        router.navigate(['/login']);
      }
      return throwError(() => error);
    })
  );
};
