export interface Notification {
  id: number;
  userId: number;
  userFirstName: string;
  userLastName: string;
  title: string;
  message: string;
  type: NotificationType;
  isRead: boolean;
  isUrgent: boolean;
  relatedAppointmentId?: number;
  actionUrl?: string;
  createdAt: Date;
  readAt?: Date;
}

export enum NotificationType {
  APPOINTMENT_CONFIRMED = 'APPOINTMENT_CONFIRMED',
  NURSE_ASSIGNED = 'NURSE_ASSIGNED',
  NURSE_ON_WAY = 'NURSE_ON_WAY',
  SAMPLING_COMPLETED = 'SAMPLING_COMPLETED',
  RESULTS_AVAILABLE = 'RESULTS_AVAILABLE',
  URGENT_REQUEST = 'URGENT_REQUEST',
  SYSTEM_ALERT = 'SYSTEM_ALERT',
  REMINDER = 'REMINDER',
  ASSIGNED = 'ASSIGNED',
  STATUS_CHANGED = 'STATUS_CHANGED'
}

export const NotificationTypeLabels = {
  [NotificationType.APPOINTMENT_CONFIRMED]: 'Rendez-vous confirmé',
  [NotificationType.NURSE_ASSIGNED]: 'Infirmier assigné',
  [NotificationType.NURSE_ON_WAY]: 'Infirmier en route',
  [NotificationType.SAMPLING_COMPLETED]: 'Prélèvement terminé',
  [NotificationType.RESULTS_AVAILABLE]: 'Résultats disponibles',
  [NotificationType.URGENT_REQUEST]: 'Demande urgente',
  [NotificationType.SYSTEM_ALERT]: 'Alerte système',
  [NotificationType.REMINDER]: 'Rappel',
  [NotificationType.ASSIGNED]: 'Assignation',
  [NotificationType.STATUS_CHANGED]: 'Statut modifié'
};
