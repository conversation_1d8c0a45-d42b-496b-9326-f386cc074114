import { User } from './user.model';

export interface Appointment {
  id: number;
  patient: User;
  nurse?: User;
  analysisTypes: AnalysisType[];
  scheduledDate: Date;
  homeAddress: string;
  latitude?: number;
  longitude?: number;
  status: AppointmentStatus;
  symptoms?: string;
  specialInstructions?: string;
  totalPrice: number;
  isUrgent: boolean;
  estimatedDurationMinutes: number;
  actualStartTime?: Date;
  actualEndTime?: Date;
  nurseNotes?: string;
  // Champs pour le suivi en temps réel
  nurseCurrentLatitude?: number;
  nurseCurrentLongitude?: number;
  locationSharingEnabled?: boolean;
  locationLastUpdated?: Date;
  estimatedArrivalTime?: Date;
  // Champ pour le workflow de mission
  samplingCompleted?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum AppointmentStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  NURSE_ASSIGNED = 'NURSE_ASSIGNED',
  NURSE_ON_WAY = 'NURSE_ON_WAY',
  IN_PROGRESS = 'IN_PROGRESS',
  SAMPLING_DONE = 'SAMPLING_DONE',
  ANALYSIS_IN_PROGRESS = 'ANALYSIS_IN_PROGRESS',
  RESULTS_AVAILABLE = 'RESULTS_AVAILABLE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export interface AnalysisType {
  id: number;
  name: string;
  description?: string;
  price: number;
  durationMinutes: number;
  preparationRequired: boolean;
  preparationInstructions?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AppointmentCreate {
  scheduledDate: Date;
  homeAddress: string;
  latitude?: number;
  longitude?: number;
  analysisTypeIds: number[];
  symptoms?: string;
  specialInstructions?: string;
  isUrgent?: boolean;
}

export interface AppointmentUpdate {
  scheduledDate?: Date;
  status?: AppointmentStatus;
  nurseNotes?: string;
}

export const AppointmentStatusLabels = {
  [AppointmentStatus.PENDING]: 'En attente',
  [AppointmentStatus.CONFIRMED]: 'Confirmé',
  [AppointmentStatus.NURSE_ASSIGNED]: 'Infirmier assigné',
  [AppointmentStatus.NURSE_ON_WAY]: 'En route',
  [AppointmentStatus.IN_PROGRESS]: 'En cours',
  [AppointmentStatus.SAMPLING_DONE]: 'Prélèvement effectué',
  [AppointmentStatus.ANALYSIS_IN_PROGRESS]: 'En cours d\'analyse',
  [AppointmentStatus.RESULTS_AVAILABLE]: 'Résultats disponibles',
  [AppointmentStatus.COMPLETED]: 'Terminé',
  [AppointmentStatus.CANCELLED]: 'Annulé'
};
