export interface User {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: Role;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
  address?: string;
  latitude?: number;
  longitude?: number;
  isAvailable?: boolean;

  // Propriétés de sécurité pour admin
  isSuperAdmin?: boolean;
  permissions?: AdminPermission[];
  lastLoginAt?: Date;
  loginAttempts?: number;
  lockedUntil?: Date;
  passwordChangedAt?: Date;
  twoFactorEnabled?: boolean;
  sessionTimeout?: number;
}

export enum Role {
  PATIENT = 'PATIENT',
  NURSE = 'NURSE',
  ADMIN = 'ADMIN'
}

export enum AdminPermission {
  // Gestion des utilisateurs
  MANAGE_USERS = 'MANAGE_USERS',
  VIEW_USERS = 'VIEW_USERS',
  CREATE_USERS = 'CREATE_USERS',
  UPDATE_USERS = 'UPDATE_USERS',
  DELETE_USERS = 'DELETE_USERS',

  // Gestion des rendez-vous
  MANAGE_APPOINTMENTS = 'MANAGE_APPOINTMENTS',
  VIEW_ALL_APPOINTMENTS = 'VIEW_ALL_APPOINTMENTS',
  ASSIGN_NURSES = 'ASSIGN_NURSES',
  CANCEL_APPOINTMENTS = 'CANCEL_APPOINTMENTS',

  // Gestion des infirmiers
  MANAGE_NURSES = 'MANAGE_NURSES',
  VIEW_NURSE_LOCATIONS = 'VIEW_NURSE_LOCATIONS',
  ASSIGN_NURSE_SCHEDULES = 'ASSIGN_NURSE_SCHEDULES',

  // Rapports et analytics
  VIEW_REPORTS = 'VIEW_REPORTS',
  EXPORT_DATA = 'EXPORT_DATA',
  VIEW_ANALYTICS = 'VIEW_ANALYTICS',

  // Configuration système
  MANAGE_SYSTEM_CONFIG = 'MANAGE_SYSTEM_CONFIG',
  MANAGE_NOTIFICATIONS = 'MANAGE_NOTIFICATIONS',
  VIEW_AUDIT_LOGS = 'VIEW_AUDIT_LOGS',

  // Sécurité
  MANAGE_SECURITY = 'MANAGE_SECURITY',
  VIEW_SECURITY_LOGS = 'VIEW_SECURITY_LOGS',
  MANAGE_PERMISSIONS = 'MANAGE_PERMISSIONS'
}

export interface UserRegistration {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role?: Role;
  address?: string;
  latitude?: number;
  longitude?: number;
}

export interface UserUpdate {
  firstName?: string;
  lastName?: string;
  phone?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  type: string;
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: Role;
  isSuperAdmin?: boolean;
}
