# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
tmp/
out-tsc/

# IDE
.vscode/
.idea/
*.iml
*.ipr
*.iws

# OS
.DS_Store
Thumbs.db

# Angular
.angular/
.sass-cache/

# Testing
coverage/
*.spec.ts
karma.conf.js
protractor.conf.js

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Git
.git/
.gitignore

# Documentation
README.md
*.md

# Scripts
*.bat
*.sh

# Source maps
*.map
