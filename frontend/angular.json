{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "cli": {"packageManager": "npm", "analytics": "************************************"}, "newProjectRoot": "projects", "projects": {"frontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"browser": "src/main.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["node_modules/leaflet/dist/leaflet.css", "src/styles.scss"], "outputMode": "static"}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "1MB", "maximumError": "2MB"}, {"type": "anyComponentStyle", "maximumWarning": "50kB", "maximumError": "100kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "frontend:build:production"}, "development": {"buildTarget": "frontend:build:development", "proxyConfig": "proxy.conf.json"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["node_modules/leaflet/dist/leaflet.css", "src/styles.scss"]}}}}}}