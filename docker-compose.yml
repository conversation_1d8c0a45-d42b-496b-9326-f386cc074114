version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: medical-postgres
    environment:
      POSTGRES_DB: medical_home_sampling
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-sql:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"
    networks:
      - medical-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d medical_home_sampling"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped

  # Spring Boot Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medical-backend
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_NAME: medical_home_sampling
      DB_USERNAME: postgres
      DB_PASSWORD: postgres
      DB_HOST: postgres
      DB_POOL_SIZE: 10
      DDL_AUTO: update
      SHOW_SQL: "true"
      LOG_LEVEL: DEBUG
      SECURITY_LOG_LEVEL: DEBUG
      JWT_SECRET: mySecretKey123456789012345678901234567890
      JWT_EXPIRATION: 86400000
      CORS_ORIGINS: http://localhost,http://localhost:3000,http://localhost:8080,http://frontend,http://192.168.*.*:*,http://10.*.*.*:*,http://172.*.*.*:*,https://prev.intellitech.pro
      INIT_MODE: always
    # Backend accessible uniquement via le réseau Docker interne
    # ports:
    #   - "0.0.0.0:8080:8080"
    networks:
      - medical-network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s
    restart: unless-stopped
    volumes:
      - backend_logs:/app/logs

  # Angular Frontend with Nginx
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: medical-frontend
    ports:
      - "0.0.0.0:3000:80"
    networks:
      - medical-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "test", "-f", "/usr/share/nginx/html/index.html"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    restart: unless-stopped

networks:
  medical-network:
    driver: bridge
    name: medical-network

volumes:
  postgres_data:
    name: medical-postgres-data
  backend_logs:
    name: medical-backend-logs
