#!/bin/bash

# Script de déploiement automatique pour Medical Home Sampling
# Usage: ./deploy.sh [dev|prod]

set -e

echo "🏥 Medical Home Sampling - Script de déploiement"
echo "================================================"

# Vérifier les prérequis
if ! command -v docker &> /dev/null; then
    echo "❌ Docker n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Mode de déploiement (dev par défaut)
MODE=${1:-dev}

echo "📋 Mode de déploiement: $MODE"

# Vérifier si le fichier .env existe
if [ ! -f .env ]; then
    echo "📝 Création du fichier .env depuis .env.example..."
    cp .env.example .env
    echo "⚠️  IMPORTANT: Veuillez ajuster les variables dans le fichier .env selon votre environnement"
    echo "   Notamment CORS_ORIGINS avec votre IP réseau"
    read -p "Appuyez sur Entrée pour continuer..."
fi

# Nettoyer les conteneurs existants
echo "🧹 Nettoyage des conteneurs existants..."
docker-compose down --remove-orphans 2>/dev/null || true
docker-compose -f docker-compose.prod.yml down --remove-orphans 2>/dev/null || true

# Démarrer selon le mode
if [ "$MODE" = "prod" ]; then
    echo "🚀 Démarrage en mode PRODUCTION..."
    docker-compose -f docker-compose.prod.yml up -d --build
    COMPOSE_FILE="docker-compose.prod.yml"
else
    echo "🚀 Démarrage en mode DÉVELOPPEMENT..."
    docker-compose up -d --build
    COMPOSE_FILE="docker-compose.yml"
fi

echo "⏳ Attente du démarrage des services..."
sleep 10

# Vérifier l'état des services
echo "📊 État des services:"
if [ "$MODE" = "prod" ]; then
    docker-compose -f docker-compose.prod.yml ps
else
    docker-compose ps
fi

# Obtenir l'IP locale
LOCAL_IP=$(hostname -I | awk '{print $1}')

echo ""
echo "✅ Déploiement terminé !"
echo "========================"
echo "🌐 Accès local:    http://localhost:3000"
echo "🌐 Accès réseau:   http://$LOCAL_IP:3000"
echo ""
echo "📝 Logs en temps réel:"
if [ "$MODE" = "prod" ]; then
    echo "   docker-compose -f docker-compose.prod.yml logs -f"
else
    echo "   docker-compose logs -f"
fi
echo ""
echo "🛑 Arrêter l'application:"
if [ "$MODE" = "prod" ]; then
    echo "   docker-compose -f docker-compose.prod.yml down"
else
    echo "   docker-compose down"
fi
