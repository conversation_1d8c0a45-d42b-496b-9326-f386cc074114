# PostgreSQL Setup for Medical Home Sampling Application

## 🗄️ Database Migration: MySQL → PostgreSQL

L'application a été migrée de MySQL vers PostgreSQL pour une meilleure performance et des fonctionnalités avancées.

## 📋 Prérequis

### 1. Installation de PostgreSQL

#### Windows:
```bash
# Télécharger depuis https://www.postgresql.org/download/windows/
# Ou utiliser Chocolatey
choco install postgresql
```

#### macOS:
```bash
# Avec Homebrew
brew install postgresql
brew services start postgresql
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 2. Configuration initiale

#### Créer un utilisateur et une base de données:
```sql
-- Se connecter en tant que superutilisateur postgres
sudo -u postgres psql

-- Créer un utilisateur pour l'application
CREATE USER postgres WITH PASSWORD 'postgres';

-- Créer la base de données
CREATE DATABASE medical_home_sampling;

-- Donner tous les privilèges à l'utilisateur
GRANT ALL PRIVILEGES ON DATABASE medical_home_sampling TO postgres;

-- Quitter
\q
```

## ⚙️ Configuration de l'application

### 1. Fichier application.properties

La configuration a été mise à jour automatiquement :

```properties
# PostgreSQL Database Configuration
spring.datasource.url=******************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
```

### 2. Dépendances Maven

Le fichier `pom.xml` a été mis à jour :

```xml
<!-- Database -->
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
    <scope>runtime</scope>
</dependency>
```

### 3. Scripts SQL

Le fichier `data.sql` a été adapté pour PostgreSQL :
- `INSERT IGNORE` → `INSERT ... ON CONFLICT DO NOTHING`
- `NOW()` → `CURRENT_TIMESTAMP`
- Syntaxe PostgreSQL pour les conflits

## 🚀 Démarrage de l'application

### 1. Vérifier que PostgreSQL fonctionne

```bash
# Vérifier le statut du service
sudo systemctl status postgresql  # Linux
brew services list | grep postgresql  # macOS

# Tester la connexion
psql -h localhost -U postgres -d medical_home_sampling
```

### 2. Démarrer l'application Spring Boot

```bash
cd backend
mvn clean install
mvn spring-boot:run
```

### 3. Vérifier les logs

L'application devrait afficher :
```
Successfully connected to PostgreSQL database
Hibernate: create table if not exists...
```

## 🔧 Configuration personnalisée

### Modifier les paramètres de connexion

Si vous utilisez des paramètres différents, modifiez `application.properties` :

```properties
# Votre configuration personnalisée
spring.datasource.url=******************************************
spring.datasource.username=votre-utilisateur
spring.datasource.password=votre-mot-de-passe
```

### Variables d'environnement (recommandé pour la production)

```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=medical_home_sampling
export DB_USERNAME=postgres
export DB_PASSWORD=postgres
```

Puis dans `application.properties` :
```properties
spring.datasource.url=jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:medical_home_sampling}
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:postgres}
```

## 📊 Avantages de PostgreSQL

### Par rapport à MySQL :
- ✅ **Conformité ACID** plus stricte
- ✅ **Types de données avancés** (JSON, Arrays, etc.)
- ✅ **Fonctions géospatiales** intégrées (PostGIS)
- ✅ **Performances** optimisées pour les requêtes complexes
- ✅ **Extensions** riches (UUID, crypto, etc.)
- ✅ **Open source** complet sans restrictions

### Pour l'application médicale :
- 🏥 **Sécurité renforcée** pour les données sensibles
- 📍 **Géolocalisation** native pour le suivi des infirmiers
- 🔍 **Recherche full-text** pour les symptômes
- 📊 **Analytics** avancées pour les statistiques

## 🛠️ Dépannage

### Problème de connexion :
```bash
# Vérifier que PostgreSQL écoute sur le bon port
sudo netstat -plunt | grep 5432

# Vérifier les logs PostgreSQL
sudo tail -f /var/log/postgresql/postgresql-*.log
```

### Erreur d'authentification :
```sql
-- Modifier la méthode d'authentification dans pg_hba.conf
sudo nano /etc/postgresql/*/main/pg_hba.conf

# Changer 'peer' en 'md5' pour les connexions locales
local   all             all                                     md5
host    all             all             127.0.0.1/32            md5
```

### Réinitialiser la base de données :
```sql
DROP DATABASE IF EXISTS medical_home_sampling;
CREATE DATABASE medical_home_sampling;
GRANT ALL PRIVILEGES ON DATABASE medical_home_sampling TO postgres;
```

## 📞 Support

En cas de problème :
1. Vérifiez les logs de l'application Spring Boot
2. Vérifiez les logs PostgreSQL
3. Testez la connexion manuellement avec `psql`
4. Vérifiez la configuration réseau et les pare-feu

L'application est maintenant prête à fonctionner avec PostgreSQL ! 🎉
