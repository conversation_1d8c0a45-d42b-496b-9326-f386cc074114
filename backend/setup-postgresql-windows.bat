@echo off
echo ========================================
echo Configuration PostgreSQL pour Windows
echo ========================================
echo.

echo Etape 1: Verification de l'installation PostgreSQL...
where psql >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: PostgreSQL n'est pas installe ou pas dans le PATH
    echo.
    echo Veuillez installer PostgreSQL depuis:
    echo https://www.postgresql.org/download/windows/
    echo.
    echo Ou avec Chocolatey: choco install postgresql
    pause
    exit /b 1
)

echo PostgreSQL trouve!
echo.

echo Etape 2: Test de connexion...
echo Tentative de connexion avec l'utilisateur postgres...
echo.

echo Veuillez entrer le mot de passe pour l'utilisateur postgres:
psql -U postgres -h localhost -c "SELECT version();"
if %errorlevel% neq 0 (
    echo.
    echo ERREUR: Impossible de se connecter a PostgreSQL
    echo.
    echo Solutions possibles:
    echo 1. Verifiez que le service PostgreSQL est demarre
    echo 2. Verifiez le mot de passe de l'utilisateur postgres
    echo 3. Modifiez le fichier pg_hba.conf pour autoriser les connexions
    echo.
    pause
    exit /b 1
)

echo.
echo Etape 3: Creation de la base de donnees...
psql -U postgres -h localhost -c "DROP DATABASE IF EXISTS medical_home_sampling;"
psql -U postgres -h localhost -c "CREATE DATABASE medical_home_sampling;"
if %errorlevel% neq 0 (
    echo ERREUR: Impossible de creer la base de donnees
    pause
    exit /b 1
)

echo.
echo Etape 4: Verification de la base de donnees...
psql -U postgres -h localhost -d medical_home_sampling -c "SELECT current_database();"
if %errorlevel% neq 0 (
    echo ERREUR: Impossible de se connecter a la base de donnees
    pause
    exit /b 1
)

echo.
echo ========================================
echo Configuration terminee avec succes!
echo ========================================
echo.
echo Base de donnees: medical_home_sampling
echo Utilisateur: postgres
echo Host: localhost
echo Port: 5432
echo.
echo Vous pouvez maintenant demarrer l'application Spring Boot:
echo mvn spring-boot:run
echo.
pause
