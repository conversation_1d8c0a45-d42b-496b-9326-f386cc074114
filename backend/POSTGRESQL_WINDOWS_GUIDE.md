# 🐘 Guide PostgreSQL pour Windows - Medical Home Sampling

## 🚀 Installation rapide (Recommandée)

### Option 1: Script automatique PowerShell

1. **Ouvrez PowerShell en tant qu'administrateur**
   - Clic droit sur le menu Démarrer → "Windows PowerShell (Admin)"

2. **Exécutez le script d'installation**
   ```powershell
   cd C:\Users\<USER>\Desktop\medicalProject\backend
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   .\setup-postgresql.ps1
   ```

3. **Suivez les instructions** du script

### Option 2: Installation manuelle

#### Étape 1: Télécharger PostgreSQL
- Allez sur https://www.postgresql.org/download/windows/
- Téléchargez la version 15.x
- **IMPORTANT**: Notez le mot de passe que vous définissez pour l'utilisateur `postgres`

#### Étape 2: Installation
1. Lancez l'installateur
2. Gardez les paramètres par défaut
3. **Port**: 5432
4. **Mot de passe**: `postgres` (ou notez celui que vous choisissez)
5. <PERSON><PERSON><PERSON> "Stack Builder" à la fin (optionnel)

#### Étape 3: Vérification
```cmd
# Ouvrir l'invite de commande
psql --version
```

## 🔧 Configuration de la base de données

### Méthode 1: Avec pgAdmin (Interface graphique)

1. **Ouvrez pgAdmin** (installé avec PostgreSQL)
2. **Connectez-vous** avec le mot de passe défini
3. **Clic droit sur "Databases"** → Create → Database
4. **Nom**: `medical_home_sampling`
5. **Owner**: `postgres`
6. **Cliquez "Save"**

### Méthode 2: Ligne de commande

```cmd
# Ouvrir l'invite de commande
cd "C:\Program Files\PostgreSQL\15\bin"

# Se connecter à PostgreSQL
psql -U postgres

# Dans psql, créer la base
CREATE DATABASE medical_home_sampling;

# Vérifier
\l

# Quitter
\q
```

## 🛠️ Résolution des problèmes courants

### Problème 1: "psql n'est pas reconnu"

**Solution**: Ajouter PostgreSQL au PATH
```cmd
# Ajouter au PATH système
set PATH=%PATH%;C:\Program Files\PostgreSQL\15\bin
```

Ou via l'interface Windows:
1. Panneau de configuration → Système → Paramètres système avancés
2. Variables d'environnement → PATH → Modifier
3. Ajouter: `C:\Program Files\PostgreSQL\15\bin`

### Problème 2: "Authentification échouée"

**Solution 1**: Vérifier le mot de passe
```cmd
psql -U postgres -h localhost
# Entrer le mot de passe défini lors de l'installation
```

**Solution 2**: Modifier pg_hba.conf
1. Aller dans `C:\Program Files\PostgreSQL\15\data\`
2. Ouvrir `pg_hba.conf` en tant qu'administrateur
3. Changer:
   ```
   # Avant
   local   all             all                                     peer
   host    all             all             127.0.0.1/32            ident
   
   # Après
   local   all             all                                     md5
   host    all             all             127.0.0.1/32            md5
   ```
4. Redémarrer le service PostgreSQL

### Problème 3: Service PostgreSQL ne démarre pas

**Solution**:
```cmd
# Ouvrir l'invite de commande en tant qu'administrateur
net stop postgresql-x64-15
net start postgresql-x64-15
```

Ou via Services Windows:
1. Windows + R → `services.msc`
2. Chercher "postgresql"
3. Clic droit → Redémarrer

## ✅ Test de la configuration

### 1. Test de connexion
```cmd
psql -U postgres -h localhost -d medical_home_sampling
```

### 2. Test depuis l'application
```cmd
cd C:\Users\<USER>\Desktop\medicalProject\backend
mvn spring-boot:run
```

**Recherchez dans les logs**:
```
HikariPool-1 - Start completed.
Hibernate: create table if not exists...
```

## 🔐 Configuration de sécurité (Production)

### Créer un utilisateur dédié
```sql
-- Se connecter en tant que postgres
psql -U postgres

-- Créer un utilisateur pour l'application
CREATE USER medical_app WITH PASSWORD 'votre_mot_de_passe_securise';

-- Donner les permissions
GRANT ALL PRIVILEGES ON DATABASE medical_home_sampling TO medical_app;

-- Quitter
\q
```

### Modifier application.properties
```properties
spring.datasource.username=medical_app
spring.datasource.password=votre_mot_de_passe_securise
```

## 📊 Outils utiles

### pgAdmin 4
- Interface graphique pour gérer PostgreSQL
- Installé automatiquement avec PostgreSQL
- Accès: http://localhost:5050 (ou via le menu Démarrer)

### Commandes psql utiles
```sql
-- Lister les bases de données
\l

-- Se connecter à une base
\c medical_home_sampling

-- Lister les tables
\dt

-- Voir la structure d'une table
\d nom_table

-- Quitter
\q
```

## 🚨 En cas de problème persistant

### Option de secours: Docker
Si PostgreSQL pose trop de problèmes, utilisez Docker:

```cmd
# Installer Docker Desktop pour Windows
# Puis exécuter:
docker run --name postgres-medical -e POSTGRES_PASSWORD=postgres -e POSTGRES_DB=medical_home_sampling -p 5432:5432 -d postgres:15
```

### Support
1. **Vérifiez les logs** de PostgreSQL dans:
   `C:\Program Files\PostgreSQL\15\data\log\`

2. **Testez la connexion** manuellement avant de lancer l'application

3. **Vérifiez le pare-feu** Windows (port 5432)

## 🎯 Checklist finale

- [ ] PostgreSQL installé et démarré
- [ ] Base `medical_home_sampling` créée
- [ ] Connexion testée avec `psql`
- [ ] Application Spring Boot démarre sans erreur
- [ ] Tables créées automatiquement par Hibernate

**Une fois tout configuré, votre application sera prête avec PostgreSQL !** 🎉
