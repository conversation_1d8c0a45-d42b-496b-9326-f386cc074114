@echo off
echo ========================================
echo Diagnostic PostgreSQL
echo ========================================
echo.

echo 1. Verification du service PostgreSQL...
net start | findstr postgres
if %errorlevel% neq 0 (
    echo ERREUR: Service PostgreSQL non trouve ou non demarre
    echo Tentative de demarrage...
    net start postgresql-x64-15
)
echo.

echo 2. Test de connexion avec mot de passe 'admin'...
set PGPASSWORD=admin
psql -U postgres -h localhost -c "SELECT version();" 2>nul
if %errorlevel% neq 0 (
    echo ERREUR: Connexion echouee avec mot de passe 'admin'
    echo.
    echo Tentative avec mot de passe 'postgres'...
    set PGPASSWORD=postgres
    psql -U postgres -h localhost -c "SELECT version();" 2>nul
    if %errorlevel% neq 0 (
        echo ERREUR: Connexion echouee avec mot de passe 'postgres'
        echo.
        echo Veuillez verifier:
        echo 1. Le service PostgreSQL est demarre
        echo 2. Le mot de passe de l'utilisateur postgres
        echo 3. Le fichier pg_hba.conf permet les connexions md5
        pause
        exit /b 1
    ) else (
        echo SUCCES: Connexion reussie avec mot de passe 'postgres'
        echo ATTENTION: Votre application.properties utilise 'admin' mais le bon mot de passe est 'postgres'
    )
) else (
    echo SUCCES: Connexion reussie avec mot de passe 'admin'
)
echo.

echo 3. Verification de la base de donnees...
psql -U postgres -h localhost -c "\l" | findstr medical_home_sampling
if %errorlevel% neq 0 (
    echo Base de donnees 'medical_home_sampling' non trouvee
    echo Creation de la base de donnees...
    psql -U postgres -h localhost -c "CREATE DATABASE medical_home_sampling;"
    if %errorlevel% neq 0 (
        echo ERREUR: Impossible de creer la base de donnees
        pause
        exit /b 1
    ) else (
        echo Base de donnees creee avec succes!
    )
) else (
    echo Base de donnees 'medical_home_sampling' trouvee!
)
echo.

echo 4. Test de connexion a la base de donnees...
psql -U postgres -h localhost -d medical_home_sampling -c "SELECT current_database();"
if %errorlevel% neq 0 (
    echo ERREUR: Impossible de se connecter a la base de donnees
    pause
    exit /b 1
) else (
    echo SUCCES: Connexion a la base de donnees reussie!
)
echo.

echo ========================================
echo Diagnostic termine
echo ========================================
echo.
echo Configuration actuelle:
echo - Host: localhost
echo - Port: 5432
echo - Database: medical_home_sampling
echo - Username: postgres
echo - Password: %PGPASSWORD%
echo.
echo Vous pouvez maintenant demarrer l'application Spring Boot
pause
