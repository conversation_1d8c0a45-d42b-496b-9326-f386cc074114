# Configuration PostgreSQL pour l'application Medical Home Sampling
# Exécuter en tant qu'administrateur

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Configuration PostgreSQL pour Windows" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Vérification des privilèges administrateur
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERREUR: Ce script doit être exécuté en tant qu'administrateur" -ForegroundColor Red
    Write-Host "Clic droit sur PowerShell -> Exécuter en tant qu'administrateur" -ForegroundColor Yellow
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

# Fonction pour vérifier si PostgreSQL est installé
function Test-PostgreSQLInstalled {
    try {
        $null = Get-Command psql -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# Fonction pour installer PostgreSQL avec Chocolatey
function Install-PostgreSQLWithChoco {
    Write-Host "Installation de PostgreSQL avec Chocolatey..." -ForegroundColor Yellow
    
    # Vérifier si Chocolatey est installé
    try {
        $null = Get-Command choco -ErrorAction Stop
    }
    catch {
        Write-Host "Installation de Chocolatey..." -ForegroundColor Yellow
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    }
    
    # Installer PostgreSQL
    choco install postgresql --params '/Password:postgres' -y
    
    # Ajouter au PATH
    $env:PATH += ";C:\Program Files\PostgreSQL\15\bin"
    [Environment]::SetEnvironmentVariable("PATH", $env:PATH, [EnvironmentVariableTarget]::Machine)
    
    Write-Host "PostgreSQL installé avec succès!" -ForegroundColor Green
    Write-Host "Redémarrage requis pour finaliser l'installation." -ForegroundColor Yellow
}

# Étape 1: Vérification de l'installation
Write-Host "Étape 1: Vérification de PostgreSQL..." -ForegroundColor Green

if (-not (Test-PostgreSQLInstalled)) {
    Write-Host "PostgreSQL n'est pas installé." -ForegroundColor Red
    $install = Read-Host "Voulez-vous l'installer automatiquement? (o/n)"
    
    if ($install -eq "o" -or $install -eq "O") {
        Install-PostgreSQLWithChoco
        Write-Host "Veuillez redémarrer votre ordinateur et relancer ce script." -ForegroundColor Yellow
        Read-Host "Appuyez sur Entrée pour quitter"
        exit 0
    }
    else {
        Write-Host "Veuillez installer PostgreSQL manuellement depuis:" -ForegroundColor Yellow
        Write-Host "https://www.postgresql.org/download/windows/" -ForegroundColor Cyan
        Read-Host "Appuyez sur Entrée pour quitter"
        exit 1
    }
}

Write-Host "PostgreSQL trouvé!" -ForegroundColor Green
Write-Host ""

# Étape 2: Démarrage du service
Write-Host "Étape 2: Démarrage du service PostgreSQL..." -ForegroundColor Green

$services = Get-Service -Name "*postgresql*" -ErrorAction SilentlyContinue
if ($services) {
    foreach ($service in $services) {
        if ($service.Status -ne "Running") {
            Write-Host "Démarrage du service $($service.Name)..." -ForegroundColor Yellow
            Start-Service $service.Name
        }
    }
    Write-Host "Service PostgreSQL démarré!" -ForegroundColor Green
}
else {
    Write-Host "ATTENTION: Service PostgreSQL non trouvé" -ForegroundColor Yellow
}

Write-Host ""

# Étape 3: Configuration de l'authentification
Write-Host "Étape 3: Configuration de l'authentification..." -ForegroundColor Green

# Trouver le fichier pg_hba.conf
$pgDataDir = ""
$possiblePaths = @(
    "C:\Program Files\PostgreSQL\15\data",
    "C:\Program Files\PostgreSQL\14\data",
    "C:\Program Files\PostgreSQL\13\data",
    "C:\PostgreSQL\data"
)

foreach ($path in $possiblePaths) {
    if (Test-Path "$path\pg_hba.conf") {
        $pgDataDir = $path
        break
    }
}

if ($pgDataDir) {
    Write-Host "Fichier pg_hba.conf trouvé dans: $pgDataDir" -ForegroundColor Green
    
    $hbaFile = "$pgDataDir\pg_hba.conf"
    $backup = "$pgDataDir\pg_hba.conf.backup"
    
    # Créer une sauvegarde
    Copy-Item $hbaFile $backup -Force
    
    # Modifier la configuration pour permettre l'authentification par mot de passe
    $content = Get-Content $hbaFile
    $newContent = $content -replace "local\s+all\s+all\s+peer", "local   all             all                                     md5"
    $newContent = $newContent -replace "host\s+all\s+all\s+127\.0\.0\.1/32\s+ident", "host    all             all             127.0.0.1/32            md5"
    $newContent | Set-Content $hbaFile
    
    Write-Host "Configuration pg_hba.conf mise à jour" -ForegroundColor Green
    
    # Redémarrer PostgreSQL
    Write-Host "Redémarrage de PostgreSQL..." -ForegroundColor Yellow
    $services = Get-Service -Name "*postgresql*"
    foreach ($service in $services) {
        Restart-Service $service.Name -Force
    }
    Start-Sleep -Seconds 3
}

# Étape 4: Test de connexion et création de la base
Write-Host "Étape 4: Création de la base de données..." -ForegroundColor Green

$env:PGPASSWORD = "postgres"

# Test de connexion
Write-Host "Test de connexion..." -ForegroundColor Yellow
$testResult = & psql -U postgres -h localhost -c "SELECT version();" 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "Connexion réussie!" -ForegroundColor Green
    
    # Créer la base de données
    Write-Host "Création de la base medical_home_sampling..." -ForegroundColor Yellow
    & psql -U postgres -h localhost -c "DROP DATABASE IF EXISTS medical_home_sampling;" 2>$null
    & psql -U postgres -h localhost -c "CREATE DATABASE medical_home_sampling;"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Base de données créée avec succès!" -ForegroundColor Green
    }
    else {
        Write-Host "Erreur lors de la création de la base" -ForegroundColor Red
    }
}
else {
    Write-Host "Erreur de connexion:" -ForegroundColor Red
    Write-Host $testResult -ForegroundColor Red
    Write-Host ""
    Write-Host "Solutions possibles:" -ForegroundColor Yellow
    Write-Host "1. Vérifiez que le mot de passe postgres est correct" -ForegroundColor White
    Write-Host "2. Redémarrez votre ordinateur" -ForegroundColor White
    Write-Host "3. Réinstallez PostgreSQL" -ForegroundColor White
}

# Étape 5: Résumé
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Configuration terminée!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Configuration de l'application:" -ForegroundColor Green
Write-Host "- Base de données: medical_home_sampling" -ForegroundColor White
Write-Host "- Utilisateur: postgres" -ForegroundColor White
Write-Host "- Mot de passe: postgres" -ForegroundColor White
Write-Host "- Host: localhost" -ForegroundColor White
Write-Host "- Port: 5432" -ForegroundColor White
Write-Host ""
Write-Host "Pour démarrer l'application:" -ForegroundColor Green
Write-Host "cd backend" -ForegroundColor White
Write-Host "mvn spring-boot:run" -ForegroundColor White
Write-Host ""

Read-Host "Appuyez sur Entrée pour quitter"
