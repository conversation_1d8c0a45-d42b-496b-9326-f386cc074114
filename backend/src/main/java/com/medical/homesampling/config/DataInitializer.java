package com.medical.homesampling.config;

import com.medical.homesampling.entity.User;
import com.medical.homesampling.entity.Role;
import com.medical.homesampling.entity.AnalysisType;
import com.medical.homesampling.entity.Symptom;
import com.medical.homesampling.repository.UserRepository;
import com.medical.homesampling.repository.AnalysisTypeRepository;
import com.medical.homesampling.repository.SymptomRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Component
public class DataInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AnalysisTypeRepository analysisTypeRepository;

    @Autowired
    private SymptomRepository symptomRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        logger.info("Starting data initialization...");
        
        try {
            initializeUsers();
            initializeAnalysisTypes();
            initializeSymptoms();
            logger.info("Data initialization completed successfully!");
        } catch (Exception e) {
            logger.error("Error during data initialization: ", e);
        }
    }

    private void initializeUsers() {
        // Create or update admin user
        User admin = userRepository.findByUsername("admin").orElse(new User());
        admin.setUsername("admin");
        admin.setEmail("<EMAIL>");
        admin.setPassword(passwordEncoder.encode("admin123"));
        admin.setFirstName("Admin");
        admin.setLastName("System");
        admin.setPhone("+33123456789");
        admin.setRole(Role.ADMIN);
        admin.setEnabled(true);
        // Set admin as super admin with all permissions
        admin.setIsSuperAdmin(true);
        if (admin.getCreatedAt() == null) {
            admin.setCreatedAt(LocalDateTime.now());
        }
        userRepository.save(admin);
        logger.info("Admin user created/updated: admin/admin123");

        // Create or update nurse user
        User nurse = userRepository.findByUsername("nurse1").orElse(new User());
        nurse.setUsername("nurse1");
        nurse.setEmail("<EMAIL>");
        nurse.setPassword(passwordEncoder.encode("nurse123"));
        nurse.setFirstName("Marie");
        nurse.setLastName("Dubois");
        nurse.setPhone("+33123456790");
        nurse.setRole(Role.NURSE);
        nurse.setEnabled(true);
        nurse.setAddress("123 Rue de la Santé, 75001 Paris");
        nurse.setLatitude(48.8566);
        nurse.setLongitude(2.3522);
        if (nurse.getCreatedAt() == null) {
            nurse.setCreatedAt(LocalDateTime.now());
        }
        userRepository.save(nurse);
        logger.info("Nurse user created/updated: nurse1/nurse123");

        // Create or update patient user
        User patient = userRepository.findByUsername("patient1").orElse(new User());
        patient.setUsername("patient1");
        patient.setEmail("<EMAIL>");
        patient.setPassword(passwordEncoder.encode("patient123"));
        patient.setFirstName("Jean");
        patient.setLastName("Martin");
        patient.setPhone("+33123456791");
        patient.setRole(Role.PATIENT);
        patient.setEnabled(true);
        patient.setAddress("456 Avenue des Patients, 75002 Paris");
        patient.setLatitude(48.8606);
        patient.setLongitude(2.3376);
        if (patient.getCreatedAt() == null) {
            patient.setCreatedAt(LocalDateTime.now());
        }
        userRepository.save(patient);
        logger.info("Patient user created/updated: patient1/patient123");
    }

    private void initializeAnalysisTypes() {
        // First, fix any existing analysis types that have null isActive
        var existingTypes = analysisTypeRepository.findAll();
        int fixedCount = 0;
        for (AnalysisType type : existingTypes) {
            if (type.getIsActive() == null) {
                type.setIsActive(true);
                analysisTypeRepository.save(type);
                fixedCount++;
            }
        }
        if (fixedCount > 0) {
            logger.info("Fixed {} analysis types with null isActive", fixedCount);
        }

        // Then create new ones if needed
        if (analysisTypeRepository.count() == 0) {
            AnalysisType[] analysisTypes = {
                createAnalysisType("Prise de sang complète", "Analyse sanguine complète avec numération formule sanguine", new BigDecimal("45.00"), 15, false),
                createAnalysisType("Test COVID-19", "Test PCR pour détection du COVID-19", new BigDecimal("65.00"), 10, false),
                createAnalysisType("Glycémie", "Mesure du taux de glucose dans le sang", new BigDecimal("25.00"), 5, true),
                createAnalysisType("Cholestérol", "Analyse du taux de cholestérol", new BigDecimal("35.00"), 10, true),
                createAnalysisType("Analyse d'urine", "Examen cytobactériologique des urines", new BigDecimal("30.00"), 5, false)
            };

            for (AnalysisType analysisType : analysisTypes) {
                analysisTypeRepository.save(analysisType);
            }
            logger.info("Analysis types created: {}", analysisTypes.length);
        } else {
            logger.info("Analysis types already exist: {}", analysisTypeRepository.count());
        }
    }

    private void initializeSymptoms() {
        if (symptomRepository.count() == 0) {
            Symptom[] symptoms = {
                createSymptom("Fatigue persistante", "Sensation de fatigue qui dure plusieurs semaines", "1,3,4"),
                createSymptom("Fièvre", "Température corporelle élevée", "2,1"),
                createSymptom("Maux de tête fréquents", "Céphalées récurrentes", "1,3"),
                createSymptom("Troubles digestifs", "Problèmes de digestion, nausées", "1,5"),
                createSymptom("Essoufflement", "Difficultés respiratoires", "1,2")
            };
            
            for (Symptom symptom : symptoms) {
                symptomRepository.save(symptom);
            }
            logger.info("Symptoms created: {}", symptoms.length);
        } else {
            logger.info("Symptoms already exist: {}", symptomRepository.count());
        }
    }

    private AnalysisType createAnalysisType(String name, String description, BigDecimal price, Integer duration, Boolean preparationRequired) {
        AnalysisType analysisType = new AnalysisType();
        analysisType.setName(name);
        analysisType.setDescription(description);
        analysisType.setPrice(price);
        analysisType.setDurationMinutes(duration);
        analysisType.setPreparationRequired(preparationRequired);
        analysisType.setIsActive(true);
        analysisType.setCreatedAt(LocalDateTime.now());
        analysisType.setUpdatedAt(LocalDateTime.now());
        return analysisType;
    }

    private Symptom createSymptom(String name, String description, String suggestedAnalyses) {
        Symptom symptom = new Symptom();
        symptom.setName(name);
        symptom.setDescription(description);
        symptom.setSuggestedAnalyses(suggestedAnalyses);
        symptom.setIsActive(true);
        symptom.setCreatedAt(LocalDateTime.now());
        return symptom;
    }
}
