package com.medical.homesampling.mapper;

import com.medical.homesampling.dto.AnalysisTypeDto;
import com.medical.homesampling.dto.AppointmentResponseDto;
import com.medical.homesampling.dto.UserResponseDto;
import com.medical.homesampling.entity.AnalysisType;
import com.medical.homesampling.entity.Appointment;
import com.medical.homesampling.entity.User;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class AppointmentMapper {

    public AppointmentResponseDto toDto(Appointment appointment) {
        if (appointment == null) {
            return null;
        }

        AppointmentResponseDto dto = new AppointmentResponseDto();
        dto.setId(appointment.getId());
        dto.setScheduledDate(appointment.getScheduledDate());
        dto.setHomeAddress(appointment.getHomeAddress());
        dto.setLatitude(appointment.getLatitude());
        dto.setLongitude(appointment.getLongitude());
        dto.setStatus(appointment.getStatus());
        dto.setSymptoms(appointment.getSymptoms());
        dto.setSpecialInstructions(appointment.getSpecialInstructions());
        dto.setTotalPrice(appointment.getTotalPrice());
        dto.setIsUrgent(appointment.getIsUrgent());
        dto.setEstimatedDurationMinutes(appointment.getEstimatedDurationMinutes());
        dto.setActualStartTime(appointment.getActualStartTime());
        dto.setActualEndTime(appointment.getActualEndTime());
        dto.setNurseNotes(appointment.getNurseNotes());

        // Mapper les champs de géolocalisation
        dto.setNurseCurrentLatitude(appointment.getNurseCurrentLatitude());
        dto.setNurseCurrentLongitude(appointment.getNurseCurrentLongitude());
        dto.setLocationSharingEnabled(appointment.getLocationSharingEnabled());
        dto.setLocationLastUpdated(appointment.getLocationLastUpdated());
        dto.setEstimatedArrivalTime(appointment.getEstimatedArrivalTime());
        dto.setSamplingCompleted(appointment.getSamplingCompleted());

        dto.setCreatedAt(appointment.getCreatedAt());
        dto.setUpdatedAt(appointment.getUpdatedAt());

        // Mapper le patient
        if (appointment.getPatient() != null) {
            dto.setPatient(toUserDto(appointment.getPatient()));
        }

        // Mapper l'infirmier
        if (appointment.getNurse() != null) {
            dto.setNurse(toUserDto(appointment.getNurse()));
        }

        // Mapper les types d'analyse
        try {
            if (appointment.getAnalysisTypes() != null) {
                dto.setAnalysisTypes(appointment.getAnalysisTypes().stream()
                        .map(this::toAnalysisTypeDto)
                        .collect(Collectors.toList()));
            }
        } catch (Exception e) {
            System.err.println("Error mapping analysis types: " + e.getMessage());
            // Set empty list if there's an issue with lazy loading
            dto.setAnalysisTypes(List.of());
        }

        return dto;
    }

    public UserResponseDto toUserDto(User user) {
        if (user == null) {
            return null;
        }

        UserResponseDto dto = new UserResponseDto();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(user.getEmail());
        dto.setFirstName(user.getFirstName());
        dto.setLastName(user.getLastName());
        dto.setPhone(user.getPhone());
        dto.setRole(user.getRole());
        dto.setEnabled(user.getEnabled());
        dto.setAddress(user.getAddress());
        dto.setLatitude(user.getLatitude());
        dto.setLongitude(user.getLongitude());
        dto.setIsAvailable(user.getIsAvailable());

        return dto;
    }

    public AnalysisTypeDto toAnalysisTypeDto(AnalysisType analysisType) {
        if (analysisType == null) {
            return null;
        }

        AnalysisTypeDto dto = new AnalysisTypeDto();
        dto.setId(analysisType.getId());
        dto.setName(analysisType.getName());
        dto.setDescription(analysisType.getDescription());
        dto.setPrice(analysisType.getPrice());
        dto.setDurationMinutes(analysisType.getDurationMinutes());
        dto.setPreparationRequired(analysisType.getPreparationRequired());
        dto.setPreparationInstructions(analysisType.getPreparationInstructions());
        dto.setIsActive(analysisType.getIsActive());

        return dto;
    }

    public List<AppointmentResponseDto> toDtoList(List<Appointment> appointments) {
        if (appointments == null) {
            return null;
        }

        return appointments.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    public List<UserResponseDto> toUserDtoList(List<User> users) {
        if (users == null) {
            return null;
        }

        return users.stream()
                .map(this::toUserDto)
                .collect(Collectors.toList());
    }
}
