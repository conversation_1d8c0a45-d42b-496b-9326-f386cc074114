package com.medical.homesampling.mapper;

import com.medical.homesampling.dto.NotificationResponseDto;
import com.medical.homesampling.entity.Notification;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class NotificationMapper {

    public NotificationResponseDto toDto(Notification notification) {
        if (notification == null) {
            return null;
        }

        NotificationResponseDto dto = new NotificationResponseDto();
        dto.setId(notification.getId());

        // Gérer le lazy loading de l'utilisateur
        try {
            if (notification.getUser() != null) {
                dto.setUserId(notification.getUser().getId());
                dto.setUserFirstName(notification.getUser().getFirstName());
                dto.setUserLastName(notification.getUser().getLastName());
            }
        } catch (Exception e) {
            // En cas d'erreur de lazy loading, on met des valeurs par défaut
            System.err.println("Erreur lors du chargement des données utilisateur: " + e.getMessage());
            dto.setUserId(null);
            dto.setUserFirstName("Utilisateur");
            dto.setUserLastName("Inconnu");
        }

        dto.setTitle(notification.getTitle());
        dto.setMessage(notification.getMessage());
        dto.setType(notification.getType());
        dto.setIsRead(notification.getIsRead());
        dto.setIsUrgent(notification.getIsUrgent());
        dto.setRelatedAppointmentId(notification.getRelatedAppointmentId());
        dto.setActionUrl(notification.getActionUrl());
        dto.setCreatedAt(notification.getCreatedAt());
        dto.setReadAt(notification.getReadAt());

        return dto;
    }

    public List<NotificationResponseDto> toDtoList(List<Notification> notifications) {
        if (notifications == null) {
            return null;
        }

        return notifications.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
