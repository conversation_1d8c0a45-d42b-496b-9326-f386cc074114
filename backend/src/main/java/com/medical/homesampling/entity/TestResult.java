package com.medical.homesampling.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

@Entity
@Table(name = "test_results")
public class TestResult {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "appointment_id", nullable = false)
    @NotNull(message = "Appointment is required")
    private Appointment appointment;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "analysis_type_id", nullable = false)
    @NotNull(message = "Analysis type is required")
    private AnalysisType analysisType;
    
    @Column(name = "result_file_path")
    private String resultFilePath;
    
    @Column(name = "result_file_name")
    private String resultFileName;
    
    @Column(name = "result_file_type")
    private String resultFileType;
    
    @Column(name = "result_summary", columnDefinition = "TEXT")
    private String resultSummary;
    
    @Column(name = "normal_values", columnDefinition = "TEXT")
    private String normalValues;
    
    @Column(name = "interpretation", columnDefinition = "TEXT")
    private String interpretation;
    
    @Column(name = "recommendations", columnDefinition = "TEXT")
    private String recommendations;
    
    @Column(name = "is_critical")
    private Boolean isCritical = false;
    
    @Column(name = "laboratory_technician")
    private String laboratoryTechnician;
    
    @Column(name = "validated_by")
    private String validatedBy;
    
    @Column(name = "validation_date")
    private LocalDateTime validationDate;
    
    @Column(name = "patient_notified")
    private Boolean patientNotified = false;
    
    @Column(name = "notification_date")
    private LocalDateTime notificationDate;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Appointment getAppointment() { return appointment; }
    public void setAppointment(Appointment appointment) { this.appointment = appointment; }
    
    public AnalysisType getAnalysisType() { return analysisType; }
    public void setAnalysisType(AnalysisType analysisType) { this.analysisType = analysisType; }
    
    public String getResultFilePath() { return resultFilePath; }
    public void setResultFilePath(String resultFilePath) { this.resultFilePath = resultFilePath; }
    
    public String getResultFileName() { return resultFileName; }
    public void setResultFileName(String resultFileName) { this.resultFileName = resultFileName; }
    
    public String getResultFileType() { return resultFileType; }
    public void setResultFileType(String resultFileType) { this.resultFileType = resultFileType; }
    
    public String getResultSummary() { return resultSummary; }
    public void setResultSummary(String resultSummary) { this.resultSummary = resultSummary; }
    
    public String getNormalValues() { return normalValues; }
    public void setNormalValues(String normalValues) { this.normalValues = normalValues; }
    
    public String getInterpretation() { return interpretation; }
    public void setInterpretation(String interpretation) { this.interpretation = interpretation; }
    
    public String getRecommendations() { return recommendations; }
    public void setRecommendations(String recommendations) { this.recommendations = recommendations; }
    
    public Boolean getIsCritical() { return isCritical; }
    public void setIsCritical(Boolean isCritical) { this.isCritical = isCritical; }
    
    public String getLaboratoryTechnician() { return laboratoryTechnician; }
    public void setLaboratoryTechnician(String laboratoryTechnician) { this.laboratoryTechnician = laboratoryTechnician; }
    
    public String getValidatedBy() { return validatedBy; }
    public void setValidatedBy(String validatedBy) { this.validatedBy = validatedBy; }
    
    public LocalDateTime getValidationDate() { return validationDate; }
    public void setValidationDate(LocalDateTime validationDate) { this.validationDate = validationDate; }
    
    public Boolean getPatientNotified() { return patientNotified; }
    public void setPatientNotified(Boolean patientNotified) { this.patientNotified = patientNotified; }
    
    public LocalDateTime getNotificationDate() { return notificationDate; }
    public void setNotificationDate(LocalDateTime notificationDate) { this.notificationDate = notificationDate; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
