package com.medical.homesampling.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

@Entity
@Table(name = "samples")
public class Sample {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "appointment_id", nullable = false)
    @NotNull(message = "Appointment is required")
    private Appointment appointment;
    
    @Column(name = "sample_code", unique = true, nullable = false)
    private String sampleCode;
    
    @Column(name = "collection_date")
    private LocalDateTime collectionDate;
    
    @Column(name = "laboratory_name")
    private String laboratoryName;
    
    @Column(name = "laboratory_address")
    private String laboratoryAddress;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private SampleStatus status = SampleStatus.COLLECTED;
    
    @Column(name = "storage_conditions")
    private String storageConditions;
    
    @Column(name = "transport_conditions")
    private String transportConditions;
    
    @Column(name = "quality_notes", columnDefinition = "TEXT")
    private String qualityNotes;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (sampleCode == null) {
            generateSampleCode();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    private void generateSampleCode() {
        // Generate unique sample code: SAMPLE-YYYYMMDD-HHMMSS-ID
        String timestamp = LocalDateTime.now().toString().replaceAll("[^0-9]", "");
        this.sampleCode = "SAMPLE-" + timestamp.substring(0, 14);
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Appointment getAppointment() { return appointment; }
    public void setAppointment(Appointment appointment) { this.appointment = appointment; }
    
    public String getSampleCode() { return sampleCode; }
    public void setSampleCode(String sampleCode) { this.sampleCode = sampleCode; }
    
    public LocalDateTime getCollectionDate() { return collectionDate; }
    public void setCollectionDate(LocalDateTime collectionDate) { this.collectionDate = collectionDate; }
    
    public String getLaboratoryName() { return laboratoryName; }
    public void setLaboratoryName(String laboratoryName) { this.laboratoryName = laboratoryName; }
    
    public String getLaboratoryAddress() { return laboratoryAddress; }
    public void setLaboratoryAddress(String laboratoryAddress) { this.laboratoryAddress = laboratoryAddress; }
    
    public SampleStatus getStatus() { return status; }
    public void setStatus(SampleStatus status) { this.status = status; }
    
    public String getStorageConditions() { return storageConditions; }
    public void setStorageConditions(String storageConditions) { this.storageConditions = storageConditions; }
    
    public String getTransportConditions() { return transportConditions; }
    public void setTransportConditions(String transportConditions) { this.transportConditions = transportConditions; }
    
    public String getQualityNotes() { return qualityNotes; }
    public void setQualityNotes(String qualityNotes) { this.qualityNotes = qualityNotes; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
