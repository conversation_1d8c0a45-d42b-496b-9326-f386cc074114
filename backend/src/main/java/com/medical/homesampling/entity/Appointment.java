package com.medical.homesampling.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "appointments")
public class Appointment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "patient_id", nullable = false)
    @NotNull(message = "Patient is required")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private User patient;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "nurse_id")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
    private User nurse;
    
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "appointment_analyses",
        joinColumns = @JoinColumn(name = "appointment_id"),
        inverseJoinColumns = @JoinColumn(name = "analysis_type_id")
    )
    private List<AnalysisType> analysisTypes;
    
    @Column(name = "scheduled_date")
    @NotNull(message = "Scheduled date is required")
    private LocalDateTime scheduledDate;
    
    @Column(name = "home_address", nullable = false)
    @NotNull(message = "Home address is required")
    private String homeAddress;
    
    @Column(name = "latitude")
    private Double latitude;
    
    @Column(name = "longitude")
    private Double longitude;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AppointmentStatus status = AppointmentStatus.PENDING;
    
    @Column(name = "symptoms", columnDefinition = "TEXT")
    private String symptoms;
    
    @Column(name = "special_instructions", columnDefinition = "TEXT")
    private String specialInstructions;
    
    @Column(name = "total_price", precision = 10, scale = 2)
    private BigDecimal totalPrice;
    
    @Column(name = "is_urgent")
    private Boolean isUrgent = false;
    
    @Column(name = "estimated_duration_minutes")
    private Integer estimatedDurationMinutes;
    
    @Column(name = "actual_start_time")
    private LocalDateTime actualStartTime;
    
    @Column(name = "actual_end_time")
    private LocalDateTime actualEndTime;
    
    @Column(name = "nurse_notes", columnDefinition = "TEXT")
    private String nurseNotes;

    // Champs pour le suivi en temps réel
    @Column(name = "nurse_current_latitude")
    private Double nurseCurrentLatitude;

    @Column(name = "nurse_current_longitude")
    private Double nurseCurrentLongitude;

    @Column(name = "location_sharing_enabled")
    private Boolean locationSharingEnabled = false;

    @Column(name = "location_last_updated")
    private LocalDateTime locationLastUpdated;

    @Column(name = "estimated_arrival_time")
    private LocalDateTime estimatedArrivalTime;

    @Column(name = "sampling_completed")
    private Boolean samplingCompleted = false;

    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public User getPatient() { return patient; }
    public void setPatient(User patient) { this.patient = patient; }
    
    public User getNurse() { return nurse; }
    public void setNurse(User nurse) { this.nurse = nurse; }
    
    public List<AnalysisType> getAnalysisTypes() { return analysisTypes; }
    public void setAnalysisTypes(List<AnalysisType> analysisTypes) { this.analysisTypes = analysisTypes; }
    
    public LocalDateTime getScheduledDate() { return scheduledDate; }
    public void setScheduledDate(LocalDateTime scheduledDate) { this.scheduledDate = scheduledDate; }
    
    public String getHomeAddress() { return homeAddress; }
    public void setHomeAddress(String homeAddress) { this.homeAddress = homeAddress; }
    
    public Double getLatitude() { return latitude; }
    public void setLatitude(Double latitude) { this.latitude = latitude; }
    
    public Double getLongitude() { return longitude; }
    public void setLongitude(Double longitude) { this.longitude = longitude; }
    
    public AppointmentStatus getStatus() { return status; }
    public void setStatus(AppointmentStatus status) { this.status = status; }
    
    public String getSymptoms() { return symptoms; }
    public void setSymptoms(String symptoms) { this.symptoms = symptoms; }
    
    public String getSpecialInstructions() { return specialInstructions; }
    public void setSpecialInstructions(String specialInstructions) { this.specialInstructions = specialInstructions; }
    
    public BigDecimal getTotalPrice() { return totalPrice; }
    public void setTotalPrice(BigDecimal totalPrice) { this.totalPrice = totalPrice; }
    
    public Boolean getIsUrgent() { return isUrgent; }
    public void setIsUrgent(Boolean isUrgent) { this.isUrgent = isUrgent; }
    
    public Integer getEstimatedDurationMinutes() { return estimatedDurationMinutes; }
    public void setEstimatedDurationMinutes(Integer estimatedDurationMinutes) { this.estimatedDurationMinutes = estimatedDurationMinutes; }
    
    public LocalDateTime getActualStartTime() { return actualStartTime; }
    public void setActualStartTime(LocalDateTime actualStartTime) { this.actualStartTime = actualStartTime; }
    
    public LocalDateTime getActualEndTime() { return actualEndTime; }
    public void setActualEndTime(LocalDateTime actualEndTime) { this.actualEndTime = actualEndTime; }
    
    public String getNurseNotes() { return nurseNotes; }
    public void setNurseNotes(String nurseNotes) { this.nurseNotes = nurseNotes; }

    // Getters et setters pour le suivi en temps réel
    public Double getNurseCurrentLatitude() { return nurseCurrentLatitude; }
    public void setNurseCurrentLatitude(Double nurseCurrentLatitude) { this.nurseCurrentLatitude = nurseCurrentLatitude; }

    public Double getNurseCurrentLongitude() { return nurseCurrentLongitude; }
    public void setNurseCurrentLongitude(Double nurseCurrentLongitude) { this.nurseCurrentLongitude = nurseCurrentLongitude; }

    public Boolean getLocationSharingEnabled() { return locationSharingEnabled; }
    public void setLocationSharingEnabled(Boolean locationSharingEnabled) { this.locationSharingEnabled = locationSharingEnabled; }

    public LocalDateTime getLocationLastUpdated() { return locationLastUpdated; }
    public void setLocationLastUpdated(LocalDateTime locationLastUpdated) { this.locationLastUpdated = locationLastUpdated; }

    public LocalDateTime getEstimatedArrivalTime() { return estimatedArrivalTime; }
    public void setEstimatedArrivalTime(LocalDateTime estimatedArrivalTime) { this.estimatedArrivalTime = estimatedArrivalTime; }

    public Boolean getSamplingCompleted() { return samplingCompleted; }
    public void setSamplingCompleted(Boolean samplingCompleted) { this.samplingCompleted = samplingCompleted; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
