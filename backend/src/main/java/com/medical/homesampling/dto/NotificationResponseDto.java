package com.medical.homesampling.dto;

import com.medical.homesampling.entity.NotificationType;
import java.time.LocalDateTime;

public class NotificationResponseDto {
    private Long id;
    private Long userId;
    private String userFirstName;
    private String userLastName;
    private String title;
    private String message;
    private NotificationType type;
    private Boolean isRead;
    private Boolean isUrgent;
    private Long relatedAppointmentId;
    private String actionUrl;
    private LocalDateTime createdAt;
    private LocalDateTime readAt;

    // Constructeurs
    public NotificationResponseDto() {}

    public NotificationResponseDto(Long id, Long userId, String userFirstName, String userLastName,
                                 String title, String message, NotificationType type, Boolean isRead,
                                 Boolean isUrgent, Long relatedAppointmentId, String actionUrl,
                                 LocalDateTime createdAt, LocalDateTime readAt) {
        this.id = id;
        this.userId = userId;
        this.userFirstName = userFirstName;
        this.userLastName = userLastName;
        this.title = title;
        this.message = message;
        this.type = type;
        this.isRead = isRead;
        this.isUrgent = isUrgent;
        this.relatedAppointmentId = relatedAppointmentId;
        this.actionUrl = actionUrl;
        this.createdAt = createdAt;
        this.readAt = readAt;
    }

    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public String getUserFirstName() { return userFirstName; }
    public void setUserFirstName(String userFirstName) { this.userFirstName = userFirstName; }

    public String getUserLastName() { return userLastName; }
    public void setUserLastName(String userLastName) { this.userLastName = userLastName; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public NotificationType getType() { return type; }
    public void setType(NotificationType type) { this.type = type; }

    public Boolean getIsRead() { return isRead; }
    public void setIsRead(Boolean isRead) { this.isRead = isRead; }

    public Boolean getIsUrgent() { return isUrgent; }
    public void setIsUrgent(Boolean isUrgent) { this.isUrgent = isUrgent; }

    public Long getRelatedAppointmentId() { return relatedAppointmentId; }
    public void setRelatedAppointmentId(Long relatedAppointmentId) { this.relatedAppointmentId = relatedAppointmentId; }

    public String getActionUrl() { return actionUrl; }
    public void setActionUrl(String actionUrl) { this.actionUrl = actionUrl; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getReadAt() { return readAt; }
    public void setReadAt(LocalDateTime readAt) { this.readAt = readAt; }
}
