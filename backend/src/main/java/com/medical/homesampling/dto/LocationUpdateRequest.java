package com.medical.homesampling.dto;

import java.time.LocalDateTime;

public class LocationUpdateRequest {
    private Long appointmentId;
    private Double latitude;
    private Double longitude;
    private LocalDateTime timestamp;

    // Constructeurs
    public LocationUpdateRequest() {}

    public LocationUpdateRequest(Long appointmentId, Double latitude, Double longitude, LocalDateTime timestamp) {
        this.appointmentId = appointmentId;
        this.latitude = latitude;
        this.longitude = longitude;
        this.timestamp = timestamp;
    }

    // Getters et Setters
    public Long getAppointmentId() {
        return appointmentId;
    }

    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "LocationUpdateRequest{" +
                "appointmentId=" + appointmentId +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", timestamp=" + timestamp +
                '}';
    }
}
