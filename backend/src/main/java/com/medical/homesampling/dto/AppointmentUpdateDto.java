package com.medical.homesampling.dto;

import com.medical.homesampling.entity.AppointmentStatus;

import java.time.LocalDateTime;

public class AppointmentUpdateDto {

    private LocalDateTime scheduledDate;
    private AppointmentStatus status;
    private String nurseNotes;
    private String address;
    private Double latitude;
    private Double longitude;
    private String notes;

    // Champs pour le suivi en temps réel
    private Double nurseCurrentLatitude;
    private Double nurseCurrentLongitude;
    private Boolean locationSharingEnabled;
    private LocalDateTime locationLastUpdated;
    private LocalDateTime estimatedArrivalTime;
    private Boolean samplingCompleted;

    // Constructors
    public AppointmentUpdateDto() {}

    // Getters and Setters
    public LocalDateTime getScheduledDate() { return scheduledDate; }
    public void setScheduledDate(LocalDateTime scheduledDate) { this.scheduledDate = scheduledDate; }

    public AppointmentStatus getStatus() { return status; }
    public void setStatus(AppointmentStatus status) { this.status = status; }

    public String getNurseNotes() { return nurseNotes; }
    public void setNurseNotes(String nurseNotes) { this.nurseNotes = nurseNotes; }

    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }

    public Double getLatitude() { return latitude; }
    public void setLatitude(Double latitude) { this.latitude = latitude; }

    public Double getLongitude() { return longitude; }
    public void setLongitude(Double longitude) { this.longitude = longitude; }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    public Double getNurseCurrentLatitude() { return nurseCurrentLatitude; }
    public void setNurseCurrentLatitude(Double nurseCurrentLatitude) { this.nurseCurrentLatitude = nurseCurrentLatitude; }

    public Double getNurseCurrentLongitude() { return nurseCurrentLongitude; }
    public void setNurseCurrentLongitude(Double nurseCurrentLongitude) { this.nurseCurrentLongitude = nurseCurrentLongitude; }

    public Boolean getLocationSharingEnabled() { return locationSharingEnabled; }
    public void setLocationSharingEnabled(Boolean locationSharingEnabled) { this.locationSharingEnabled = locationSharingEnabled; }

    public LocalDateTime getLocationLastUpdated() { return locationLastUpdated; }
    public void setLocationLastUpdated(LocalDateTime locationLastUpdated) { this.locationLastUpdated = locationLastUpdated; }

    public LocalDateTime getEstimatedArrivalTime() { return estimatedArrivalTime; }
    public void setEstimatedArrivalTime(LocalDateTime estimatedArrivalTime) { this.estimatedArrivalTime = estimatedArrivalTime; }

    public Boolean getSamplingCompleted() { return samplingCompleted; }
    public void setSamplingCompleted(Boolean samplingCompleted) { this.samplingCompleted = samplingCompleted; }
}
