package com.medical.homesampling.dto;

import com.medical.homesampling.entity.AppointmentStatus;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class AppointmentResponseDto {
    private Long id;
    private UserResponseDto patient;
    private UserResponseDto nurse;
    private List<AnalysisTypeDto> analysisTypes;
    private LocalDateTime scheduledDate;
    private String homeAddress;
    private Double latitude;
    private Double longitude;
    private AppointmentStatus status;
    private String symptoms;
    private String specialInstructions;
    private BigDecimal totalPrice;
    private Boolean isUrgent;
    private Integer estimatedDurationMinutes;
    private LocalDateTime actualStartTime;
    private LocalDateTime actualEndTime;
    private String nurseNotes;

    // Champs pour le suivi en temps réel
    private Double nurseCurrentLatitude;
    private Double nurseCurrentLongitude;
    private Boolean locationSharingEnabled;
    private LocalDateTime locationLastUpdated;
    private LocalDateTime estimatedArrivalTime;
    private Boolean samplingCompleted;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructeurs
    public AppointmentResponseDto() {}

    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public UserResponseDto getPatient() { return patient; }
    public void setPatient(UserResponseDto patient) { this.patient = patient; }

    public UserResponseDto getNurse() { return nurse; }
    public void setNurse(UserResponseDto nurse) { this.nurse = nurse; }

    public List<AnalysisTypeDto> getAnalysisTypes() { return analysisTypes; }
    public void setAnalysisTypes(List<AnalysisTypeDto> analysisTypes) { this.analysisTypes = analysisTypes; }

    public LocalDateTime getScheduledDate() { return scheduledDate; }
    public void setScheduledDate(LocalDateTime scheduledDate) { this.scheduledDate = scheduledDate; }

    public String getHomeAddress() { return homeAddress; }
    public void setHomeAddress(String homeAddress) { this.homeAddress = homeAddress; }

    public Double getLatitude() { return latitude; }
    public void setLatitude(Double latitude) { this.latitude = latitude; }

    public Double getLongitude() { return longitude; }
    public void setLongitude(Double longitude) { this.longitude = longitude; }

    public AppointmentStatus getStatus() { return status; }
    public void setStatus(AppointmentStatus status) { this.status = status; }

    public String getSymptoms() { return symptoms; }
    public void setSymptoms(String symptoms) { this.symptoms = symptoms; }

    public String getSpecialInstructions() { return specialInstructions; }
    public void setSpecialInstructions(String specialInstructions) { this.specialInstructions = specialInstructions; }

    public BigDecimal getTotalPrice() { return totalPrice; }
    public void setTotalPrice(BigDecimal totalPrice) { this.totalPrice = totalPrice; }

    public Boolean getIsUrgent() { return isUrgent; }
    public void setIsUrgent(Boolean isUrgent) { this.isUrgent = isUrgent; }

    public Integer getEstimatedDurationMinutes() { return estimatedDurationMinutes; }
    public void setEstimatedDurationMinutes(Integer estimatedDurationMinutes) { this.estimatedDurationMinutes = estimatedDurationMinutes; }

    public LocalDateTime getActualStartTime() { return actualStartTime; }
    public void setActualStartTime(LocalDateTime actualStartTime) { this.actualStartTime = actualStartTime; }

    public LocalDateTime getActualEndTime() { return actualEndTime; }
    public void setActualEndTime(LocalDateTime actualEndTime) { this.actualEndTime = actualEndTime; }

    public String getNurseNotes() { return nurseNotes; }
    public void setNurseNotes(String nurseNotes) { this.nurseNotes = nurseNotes; }

    // Getters et Setters pour les champs de géolocalisation
    public Double getNurseCurrentLatitude() { return nurseCurrentLatitude; }
    public void setNurseCurrentLatitude(Double nurseCurrentLatitude) { this.nurseCurrentLatitude = nurseCurrentLatitude; }

    public Double getNurseCurrentLongitude() { return nurseCurrentLongitude; }
    public void setNurseCurrentLongitude(Double nurseCurrentLongitude) { this.nurseCurrentLongitude = nurseCurrentLongitude; }

    public Boolean getLocationSharingEnabled() { return locationSharingEnabled; }
    public void setLocationSharingEnabled(Boolean locationSharingEnabled) { this.locationSharingEnabled = locationSharingEnabled; }

    public LocalDateTime getLocationLastUpdated() { return locationLastUpdated; }
    public void setLocationLastUpdated(LocalDateTime locationLastUpdated) { this.locationLastUpdated = locationLastUpdated; }

    public LocalDateTime getEstimatedArrivalTime() { return estimatedArrivalTime; }
    public void setEstimatedArrivalTime(LocalDateTime estimatedArrivalTime) { this.estimatedArrivalTime = estimatedArrivalTime; }

    public Boolean getSamplingCompleted() { return samplingCompleted; }
    public void setSamplingCompleted(Boolean samplingCompleted) { this.samplingCompleted = samplingCompleted; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
