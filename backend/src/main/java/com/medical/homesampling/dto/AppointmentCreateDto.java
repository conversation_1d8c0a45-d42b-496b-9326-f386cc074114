package com.medical.homesampling.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.List;

public class AppointmentCreateDto {

    @NotNull(message = "Scheduled date is required")
    private LocalDateTime scheduledDate;

    @NotBlank(message = "Home address is required")
    private String homeAddress;

    private Double latitude;
    private Double longitude;

    @NotEmpty(message = "At least one analysis type is required")
    private List<Long> analysisTypeIds;

    private String symptoms;
    private String specialInstructions;
    private Boolean isUrgent;

    // Pour la création par l'admin - ID du patient
    private Long patientId;
    
    // Constructors
    public AppointmentCreateDto() {}
    
    // Getters and Setters
    public LocalDateTime getScheduledDate() { return scheduledDate; }
    public void setScheduledDate(LocalDateTime scheduledDate) { this.scheduledDate = scheduledDate; }
    
    public String getHomeAddress() { return homeAddress; }
    public void setHomeAddress(String homeAddress) { this.homeAddress = homeAddress; }
    
    public Double getLatitude() { return latitude; }
    public void setLatitude(Double latitude) { this.latitude = latitude; }
    
    public Double getLongitude() { return longitude; }
    public void setLongitude(Double longitude) { this.longitude = longitude; }
    
    public List<Long> getAnalysisTypeIds() { return analysisTypeIds; }
    public void setAnalysisTypeIds(List<Long> analysisTypeIds) { this.analysisTypeIds = analysisTypeIds; }
    
    public String getSymptoms() { return symptoms; }
    public void setSymptoms(String symptoms) { this.symptoms = symptoms; }
    
    public String getSpecialInstructions() { return specialInstructions; }
    public void setSpecialInstructions(String specialInstructions) { this.specialInstructions = specialInstructions; }
    
    public Boolean getIsUrgent() { return isUrgent; }
    public void setIsUrgent(Boolean isUrgent) { this.isUrgent = isUrgent; }

    public Long getPatientId() { return patientId; }
    public void setPatientId(Long patientId) { this.patientId = patientId; }
}
