package com.medical.homesampling.controller;

import com.medical.homesampling.entity.User;
import com.medical.homesampling.entity.AnalysisType;
import com.medical.homesampling.entity.Symptom;
import com.medical.homesampling.entity.Role;
import com.medical.homesampling.repository.UserRepository;
import com.medical.homesampling.repository.AnalysisTypeRepository;
import com.medical.homesampling.repository.SymptomRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
public class TestController {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AnalysisTypeRepository analysisTypeRepository;

    @Autowired
    private SymptomRepository symptomRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @PostMapping("/init-data")
    public ResponseEntity<Map<String, Object>> initializeTestData() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Check if admin user already exists
            if (userRepository.findByUsername("admin").isEmpty()) {
                // Create admin user
                User admin = new User();
                admin.setUsername("admin");
                admin.setEmail("<EMAIL>");
                admin.setPassword(passwordEncoder.encode("admin123"));
                admin.setFirstName("Admin");
                admin.setLastName("System");
                admin.setPhone("+33123456789");
                admin.setRole(Role.ADMIN);
                admin.setEnabled(true);
                admin.setCreatedAt(LocalDateTime.now());
                userRepository.save(admin);
                response.put("admin_created", true);
            } else {
                response.put("admin_exists", true);
            }

            // Create nurse user
            if (userRepository.findByUsername("nurse1").isEmpty()) {
                User nurse = new User();
                nurse.setUsername("nurse1");
                nurse.setEmail("<EMAIL>");
                nurse.setPassword(passwordEncoder.encode("nurse123"));
                nurse.setFirstName("Marie");
                nurse.setLastName("Dubois");
                nurse.setPhone("+33123456790");
                nurse.setRole(Role.NURSE);
                nurse.setEnabled(true);
                nurse.setAddress("123 Rue de la Santé, 75001 Paris");
                nurse.setLatitude(48.8566);
                nurse.setLongitude(2.3522);
                nurse.setCreatedAt(LocalDateTime.now());
                userRepository.save(nurse);
                response.put("nurse_created", true);
            } else {
                response.put("nurse_exists", true);
            }

            // Create patient user
            if (userRepository.findByUsername("patient1").isEmpty()) {
                User patient = new User();
                patient.setUsername("patient1");
                patient.setEmail("<EMAIL>");
                patient.setPassword(passwordEncoder.encode("patient123"));
                patient.setFirstName("Jean");
                patient.setLastName("Martin");
                patient.setPhone("+33123456791");
                patient.setRole(Role.PATIENT);
                patient.setEnabled(true);
                patient.setAddress("456 Avenue des Patients, 75002 Paris");
                patient.setLatitude(48.8606);
                patient.setLongitude(2.3376);
                patient.setCreatedAt(LocalDateTime.now());
                userRepository.save(patient);
                response.put("patient_created", true);
            } else {
                response.put("patient_exists", true);
            }

            // Create analysis types
            if (analysisTypeRepository.count() == 0) {
                AnalysisType[] analysisTypes = {
                    createAnalysisType("Prise de sang complète", "Analyse sanguine complète avec numération formule sanguine", new BigDecimal("45.00"), 15, false),
                    createAnalysisType("Test COVID-19", "Test PCR pour détection du COVID-19", new BigDecimal("65.00"), 10, false),
                    createAnalysisType("Glycémie", "Mesure du taux de glucose dans le sang", new BigDecimal("25.00"), 5, true),
                    createAnalysisType("Cholestérol", "Analyse du taux de cholestérol", new BigDecimal("35.00"), 10, true),
                    createAnalysisType("Analyse d'urine", "Examen cytobactériologique des urines", new BigDecimal("30.00"), 5, false)
                };
                
                for (AnalysisType analysisType : analysisTypes) {
                    analysisTypeRepository.save(analysisType);
                }
                response.put("analysis_types_created", analysisTypes.length);
            } else {
                response.put("analysis_types_exist", analysisTypeRepository.count());
            }

            // Create symptoms
            if (symptomRepository.count() == 0) {
                Symptom[] symptoms = {
                    createSymptom("Fatigue persistante", "Sensation de fatigue qui dure plusieurs semaines", "1,3,4"),
                    createSymptom("Fièvre", "Température corporelle élevée", "2,1"),
                    createSymptom("Maux de tête fréquents", "Céphalées récurrentes", "1,3"),
                    createSymptom("Troubles digestifs", "Problèmes de digestion, nausées", "1,5"),
                    createSymptom("Essoufflement", "Difficultés respiratoires", "1,2")
                };
                
                for (Symptom symptom : symptoms) {
                    symptomRepository.save(symptom);
                }
                response.put("symptoms_created", symptoms.length);
            } else {
                response.put("symptoms_exist", symptomRepository.count());
            }

            response.put("success", true);
            response.put("message", "Test data initialized successfully");
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    private AnalysisType createAnalysisType(String name, String description, BigDecimal price, Integer duration, Boolean preparationRequired) {
        AnalysisType analysisType = new AnalysisType();
        analysisType.setName(name);
        analysisType.setDescription(description);
        analysisType.setPrice(price);
        analysisType.setDurationMinutes(duration);
        analysisType.setPreparationRequired(preparationRequired);
        analysisType.setIsActive(true);
        analysisType.setCreatedAt(LocalDateTime.now());
        return analysisType;
    }

    private Symptom createSymptom(String name, String description, String suggestedAnalyses) {
        Symptom symptom = new Symptom();
        symptom.setName(name);
        symptom.setDescription(description);
        symptom.setSuggestedAnalyses(suggestedAnalyses);
        symptom.setIsActive(true);
        symptom.setCreatedAt(LocalDateTime.now());
        return symptom;
    }

    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("users_count", userRepository.count());
        status.put("analysis_types_count", analysisTypeRepository.count());
        status.put("symptoms_count", symptomRepository.count());
        status.put("timestamp", LocalDateTime.now());
        return ResponseEntity.ok(status);
    }

    @GetMapping("/hello")
    public ResponseEntity<String> hello() {
        return ResponseEntity.ok("Hello from Test Controller! Backend is working with MySQL.");
    }
}
