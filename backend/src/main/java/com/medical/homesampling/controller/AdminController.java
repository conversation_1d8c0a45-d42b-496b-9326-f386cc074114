package com.medical.homesampling.controller;

import com.medical.homesampling.dto.UserResponseDto;
import com.medical.homesampling.dto.AppointmentResponseDto;
import com.medical.homesampling.dto.AppointmentCreateDto;
import com.medical.homesampling.entity.User;
import com.medical.homesampling.entity.Role;
import com.medical.homesampling.entity.Appointment;
import com.medical.homesampling.entity.AnalysisType;
import com.medical.homesampling.repository.UserRepository;
import com.medical.homesampling.repository.AnalysisTypeRepository;
import com.medical.homesampling.service.AdminSecurityService;
import com.medical.homesampling.service.AppointmentService;
import com.medical.homesampling.mapper.AppointmentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AdminController {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AdminSecurityService adminSecurityService;

    @Autowired
    private AppointmentService appointmentService;

    @Autowired
    private AppointmentMapper appointmentMapper;

    @Autowired
    private AnalysisTypeRepository analysisTypeRepository;

    // ========== GESTION DES PATIENTS ==========

    /**
     * Récupère tous les patients
     */
    @GetMapping("/patients")
    public ResponseEntity<List<UserResponseDto>> getAllPatients() {
        try {
            List<User> patients = userRepository.findByRole(Role.PATIENT);
            List<UserResponseDto> patientDtos = patients.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            // Log de sécurité
            adminSecurityService.logSecurityEvent(
                "PATIENTS_LIST_ACCESSED",
                "PATIENT_MANAGEMENT",
                Map.of("patientCount", patients.size())
            );

            return ResponseEntity.ok(patientDtos);
        } catch (Exception e) {
            adminSecurityService.logSecurityEvent(
                "PATIENTS_LIST_ACCESS_FAILED",
                "PATIENT_MANAGEMENT",
                Map.of("error", e.getMessage())
            );
            throw e;
        }
    }

    /**
     * Récupère les rendez-vous d'un patient spécifique
     */
    @GetMapping("/patients/{patientId}/appointments")
    public ResponseEntity<List<AppointmentResponseDto>> getPatientAppointments(@PathVariable Long patientId) {
        try {
            User patient = userRepository.findById(patientId)
                    .orElseThrow(() -> new RuntimeException("Patient not found with id: " + patientId));

            if (patient.getRole() != Role.PATIENT) {
                throw new RuntimeException("User is not a patient");
            }

            List<Appointment> appointments = appointmentService.getAppointmentsByPatient(patient);
            List<AppointmentResponseDto> appointmentDtos = appointmentMapper.toDtoList(appointments);

            // Log de sécurité
            adminSecurityService.logSecurityEvent(
                "PATIENT_APPOINTMENTS_ACCESSED",
                "PATIENT_MANAGEMENT",
                Map.of("patientId", patientId, "appointmentCount", appointments.size())
            );

            return ResponseEntity.ok(appointmentDtos);
        } catch (Exception e) {
            adminSecurityService.logSecurityEvent(
                "PATIENT_APPOINTMENTS_ACCESS_FAILED",
                "PATIENT_MANAGEMENT",
                Map.of("patientId", patientId, "error", e.getMessage())
            );
            throw e;
        }
    }

    /**
     * Récupère un patient par ID
     */
    @GetMapping("/patients/{id}")
    public ResponseEntity<UserResponseDto> getPatientById(@PathVariable Long id) {
        try {
            User patient = userRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("Patient not found with id: " + id));

            if (patient.getRole() != Role.PATIENT) {
                throw new RuntimeException("User is not a patient");
            }

            UserResponseDto patientDto = convertToDto(patient);

            // Log de sécurité
            adminSecurityService.logSecurityEvent(
                "PATIENT_DETAILS_ACCESSED",
                "PATIENT_MANAGEMENT",
                Map.of("patientId", id)
            );

            return ResponseEntity.ok(patientDto);
        } catch (Exception e) {
            adminSecurityService.logSecurityEvent(
                "PATIENT_DETAILS_ACCESS_FAILED",
                "PATIENT_MANAGEMENT",
                Map.of("patientId", id, "error", e.getMessage())
            );
            throw e;
        }
    }

    /**
     * Crée un nouveau rendez-vous pour un patient spécifique (Admin uniquement)
     */
    @PostMapping("/patients/{patientId}/appointments")
    public ResponseEntity<AppointmentResponseDto> createAppointmentForPatient(
            @PathVariable Long patientId,
            @Valid @RequestBody AppointmentCreateDto createDto) {
        try {
            // Vérifier que le patient existe
            User patient = userRepository.findById(patientId)
                    .orElseThrow(() -> new RuntimeException("Patient not found with id: " + patientId));

            if (patient.getRole() != Role.PATIENT) {
                throw new RuntimeException("User is not a patient");
            }

            // Créer le rendez-vous pour ce patient
            Appointment appointment = appointmentService.createAppointment(createDto, patient);
            AppointmentResponseDto appointmentDto = appointmentMapper.toDto(appointment);

            // Log de sécurité
            adminSecurityService.logSecurityEvent(
                "APPOINTMENT_CREATED_FOR_PATIENT",
                "APPOINTMENT_MANAGEMENT",
                Map.of(
                    "patientId", patientId,
                    "appointmentId", appointment.getId(),
                    "scheduledDate", appointment.getScheduledDate().toString()
                )
            );

            return ResponseEntity.ok(appointmentDto);
        } catch (Exception e) {
            adminSecurityService.logSecurityEvent(
                "APPOINTMENT_CREATION_FAILED",
                "APPOINTMENT_MANAGEMENT",
                Map.of("patientId", patientId, "error", e.getMessage())
            );
            throw e;
        }
    }

    // ========== GESTION DES INFIRMIERS ==========
    // Gestion des infirmiers déplacée vers AdminNurseController

    /**
     * Endpoint pour enregistrer les événements de sécurité
     */
    @PostMapping("/security/events")
    public ResponseEntity<Map<String, Object>> logSecurityEvent(@RequestBody Map<String, Object> eventData) {
        try {
            String action = (String) eventData.get("action");
            String resource = (String) eventData.get("resource");
            Map<String, Object> details = (Map<String, Object>) eventData.get("details");
            
            // Enregistrer l'événement de sécurité
            adminSecurityService.logSecurityEvent(action, resource, details);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Security event logged successfully");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            
            return ResponseEntity.ok(response);
        }
    }

    // ========== GESTION DES ANALYSES ==========

    /**
     * Récupère toutes les analyses
     */
    @GetMapping("/analyses")
    public ResponseEntity<List<AnalysisType>> getAllAnalyses() {
        try {
            List<AnalysisType> analyses = analysisTypeRepository.findAll();
            return ResponseEntity.ok(analyses);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Crée une nouvelle analyse
     */
    @PostMapping("/analyses")
    public ResponseEntity<AnalysisType> createAnalysis(@RequestBody AnalysisType analysisType) {
        try {
            // Vérifier si une analyse avec ce nom existe déjà
            if (analysisTypeRepository.existsByNameIgnoreCase(analysisType.getName())) {
                return ResponseEntity.status(409).build(); // Conflict
            }

            AnalysisType savedAnalysis = analysisTypeRepository.save(analysisType);
            return ResponseEntity.ok(savedAnalysis);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Met à jour une analyse
     */
    @PutMapping("/analyses/{id}")
    public ResponseEntity<AnalysisType> updateAnalysis(@PathVariable Long id, @RequestBody AnalysisType analysisType) {
        try {
            return analysisTypeRepository.findById(id)
                    .map(existingAnalysis -> {
                        existingAnalysis.setName(analysisType.getName());
                        existingAnalysis.setDescription(analysisType.getDescription());
                        existingAnalysis.setPrice(analysisType.getPrice());
                        existingAnalysis.setDurationMinutes(analysisType.getDurationMinutes());
                        existingAnalysis.setIsActive(analysisType.getIsActive());

                        AnalysisType updatedAnalysis = analysisTypeRepository.save(existingAnalysis);
                        return ResponseEntity.ok(updatedAnalysis);
                    })
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Supprime une analyse
     */
    @DeleteMapping("/analyses/{id}")
    public ResponseEntity<Map<String, String>> deleteAnalysis(@PathVariable Long id) {
        try {
            if (!analysisTypeRepository.existsById(id)) {
                return ResponseEntity.notFound().build();
            }

            analysisTypeRepository.deleteById(id);

            Map<String, String> response = new HashMap<>();
            response.put("message", "Analyse supprimée avec succès");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("error", "Erreur lors de la suppression de l'analyse");
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Active/désactive une analyse
     */
    @PatchMapping("/analyses/{id}/status")
    public ResponseEntity<AnalysisType> toggleAnalysisStatus(@PathVariable Long id, @RequestBody Map<String, Boolean> statusUpdate) {
        try {
            return analysisTypeRepository.findById(id)
                    .map(analysis -> {
                        analysis.setIsActive(statusUpdate.get("isActive"));
                        AnalysisType updatedAnalysis = analysisTypeRepository.save(analysis);
                        return ResponseEntity.ok(updatedAnalysis);
                    })
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Convertit un User en UserResponseDto
     */
    private UserResponseDto convertToDto(User user) {
        UserResponseDto dto = new UserResponseDto();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(user.getEmail());
        dto.setFirstName(user.getFirstName());
        dto.setLastName(user.getLastName());
        dto.setPhone(user.getPhone());
        dto.setRole(user.getRole());
        dto.setEnabled(user.getEnabled());
        dto.setAddress(user.getAddress());
        dto.setLatitude(user.getLatitude());
        dto.setLongitude(user.getLongitude());
        dto.setIsAvailable(user.getIsAvailable());
        return dto;
    }
}
