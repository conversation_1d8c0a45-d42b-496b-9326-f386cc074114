package com.medical.homesampling.controller;

import com.medical.homesampling.dto.OtpVerificationDto;
import com.medical.homesampling.dto.ResendOtpDto;
import com.medical.homesampling.entity.User;
import com.medical.homesampling.entity.PendingRegistration;
import com.medical.homesampling.repository.UserRepository;
import com.medical.homesampling.repository.PendingRegistrationRepository;
import com.medical.homesampling.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/auth/otp")
@Tag(name = "OTP Verification", description = "OTP verification APIs")
@CrossOrigin(origins = "*", maxAge = 3600)
public class OtpController {

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PendingRegistrationRepository pendingRegistrationRepository;

    @PostMapping("/verify-email")
    @Operation(summary = "Verify email OTP", description = "Verify email with OTP code")
    public ResponseEntity<?> verifyEmailOtp(@Valid @RequestBody OtpVerificationDto otpDto) {
        System.out.println("\n🎯 CONTRÔLEUR OTP - Vérification email:");
        System.out.println("   Email: " + otpDto.getEmail());
        System.out.println("   Code: " + otpDto.getOtpCode());

        try {
            boolean isVerified = userService.verifyEmailOtp(otpDto.getEmail(), otpDto.getOtpCode());
            System.out.println("   Résultat vérification: " + isVerified);

            if (isVerified) {
                System.out.println("   ✅ Vérification réussie - Utilisateur créé et activé");
                return ResponseEntity.ok(new MessageResponse("Email vérifié avec succès! Vous pouvez maintenant vous connecter."));
            } else {
                System.out.println("   ❌ Vérification échouée");
                return ResponseEntity.badRequest()
                    .body(new MessageResponse("Code OTP invalide ou expiré"));
            }
        } catch (Exception e) {
            System.err.println("   ❌ Exception lors de la vérification: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Erreur: " + e.getMessage()));
        }
    }

    @PostMapping("/resend-email")
    @Operation(summary = "Resend email OTP", description = "Resend email verification OTP")
    public ResponseEntity<?> resendEmailOtp(@Valid @RequestBody ResendOtpDto resendDto) {
        try {
            userService.resendEmailVerificationOtp(resendDto.getEmail());
            return ResponseEntity.ok(new MessageResponse("Code de vérification renvoyé avec succès"));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Erreur: " + e.getMessage()));
        }
    }

    @GetMapping("/debug/{email}")
    @Operation(summary = "Debug user registration status", description = "Check user and pending registration status")
    public ResponseEntity<?> debugUserStatus(@PathVariable String email) {
        Map<String, Object> debug = new HashMap<>();

        // Vérifier dans la table users
        Optional<User> userOpt = userRepository.findByEmail(email);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            debug.put("user_exists", true);
            debug.put("user_id", user.getId());
            debug.put("username", user.getUsername());
            debug.put("role", user.getRole());
            debug.put("enabled", user.getEnabled());
            debug.put("email_verified", user.getEmailVerified());
        } else {
            debug.put("user_exists", false);
        }

        // Vérifier dans les inscriptions en attente
        Optional<PendingRegistration> pendingOpt = pendingRegistrationRepository.findByEmail(email);
        if (pendingOpt.isPresent()) {
            PendingRegistration pending = pendingOpt.get();
            debug.put("pending_exists", true);
            debug.put("pending_username", pending.getUsername());
            debug.put("pending_role", pending.getRole());
            debug.put("pending_created", pending.getCreatedAt());
        } else {
            debug.put("pending_exists", false);
        }

        // Statistiques générales
        debug.put("total_users", userRepository.count());
        debug.put("total_pending", pendingRegistrationRepository.count());

        return ResponseEntity.ok(debug);
    }

    // Classe interne pour les réponses
    public static class MessageResponse {
        private String message;

        public MessageResponse(String message) {
            this.message = message;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
