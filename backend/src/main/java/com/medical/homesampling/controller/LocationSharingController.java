package com.medical.homesampling.controller;

import com.medical.homesampling.dto.LocationUpdateRequest;
import com.medical.homesampling.entity.Appointment;
import com.medical.homesampling.entity.AppointmentStatus;
import com.medical.homesampling.service.AppointmentService;
import com.medical.homesampling.service.LocationSharingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/location-sharing")
@CrossOrigin(origins = "http://localhost:4200")
public class LocationSharingController {

    @Autowired
    private LocationSharingService locationSharingService;

    @Autowired
    private AppointmentService appointmentService;

    /**
     * Démarre le partage de position pour un rendez-vous
     */
    @PostMapping("/start")
    public ResponseEntity<?> startLocationSharing(@RequestBody Map<String, Long> request) {
        try {
            Long appointmentId = request.get("appointmentId");
            System.out.println("🚀 Starting location sharing for appointment: " + appointmentId);

            // Utiliser le service LocationSharingService qui gère correctement la sauvegarde
            Appointment appointment = locationSharingService.startLocationSharing(appointmentId);

            System.out.println("✅ Location sharing started for appointment: " + appointmentId +
                             " - Status: " + appointment.getStatus() +
                             " - Sharing enabled: " + appointment.getLocationSharingEnabled());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Partage de position démarré",
                "appointment", appointment
            ));

        } catch (Exception e) {
            System.err.println("❌ Error starting location sharing: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Erreur lors du démarrage du partage de position: " + e.getMessage()
            ));
        }
    }

    /**
     * Arrête le partage de position pour un rendez-vous
     */
    @PostMapping("/stop")
    public ResponseEntity<?> stopLocationSharing(@RequestBody Map<String, Long> request) {
        try {
            Long appointmentId = request.get("appointmentId");
            System.out.println("🛑 Stopping location sharing for appointment: " + appointmentId);

            // Utiliser le service LocationSharingService
            Appointment appointment = locationSharingService.stopLocationSharing(appointmentId);

            System.out.println("✅ Location sharing stopped for appointment: " + appointmentId +
                             " - Sharing enabled: " + appointment.getLocationSharingEnabled());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Partage de position arrêté",
                "appointment", appointment
            ));

        } catch (Exception e) {
            System.err.println("❌ Error stopping location sharing: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Erreur lors de l'arrêt du partage de position: " + e.getMessage()
            ));
        }
    }

    /**
     * Met à jour la position de l'infirmier
     */
    @PostMapping("/update")
    public ResponseEntity<?> updateNurseLocation(@RequestBody LocationUpdateRequest request) {
        try {
            System.out.println("📍 Updating nurse location for appointment: " + request.getAppointmentId() +
                             " - Lat: " + request.getLatitude() + ", Lng: " + request.getLongitude());

            // Utiliser le service LocationSharingService
            Appointment appointment = locationSharingService.updateNurseLocation(
                request.getAppointmentId(),
                request.getLatitude(),
                request.getLongitude()
            );

            System.out.println("✅ Nurse location updated successfully for appointment: " + request.getAppointmentId());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Position mise à jour",
                "appointment", appointment
            ));

        } catch (Exception e) {
            System.err.println("❌ Error updating nurse location: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Erreur lors de la mise à jour de la position: " + e.getMessage()
            ));
        }
    }

    /**
     * Marque le prélèvement comme effectué et arrête le partage
     */
    @PostMapping("/sampling-completed")
    public ResponseEntity<?> markSamplingCompleted(@RequestBody Map<String, Long> request) {
        try {
            Long appointmentId = request.get("appointmentId");
            System.out.println("🏁 Marking sampling completed for appointment: " + appointmentId);

            // Utiliser le service LocationSharingService pour arrêter le partage
            Appointment appointment = locationSharingService.stopLocationSharing(appointmentId);

            // Mettre à jour le statut à SAMPLING_DONE
            appointment = appointmentService.updateAppointmentStatus(appointmentId, AppointmentStatus.SAMPLING_DONE);

            // Marquer le prélèvement comme terminé
            appointment.setSamplingCompleted(true);
            appointment = appointmentService.saveAppointment(appointment);

            System.out.println("✅ Sampling completed and location sharing stopped for appointment: " + appointmentId);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Prélèvement effectué et partage arrêté",
                "appointment", appointment
            ));

        } catch (Exception e) {
            System.err.println("❌ Error marking sampling completed: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Erreur lors de la finalisation du prélèvement: " + e.getMessage()
            ));
        }
    }

    /**
     * Crée un DTO de mise à jour à partir d'une entité Appointment
     */
    private com.medical.homesampling.dto.AppointmentUpdateDto createUpdateDto(Appointment appointment) {
        com.medical.homesampling.dto.AppointmentUpdateDto dto = new com.medical.homesampling.dto.AppointmentUpdateDto();

        dto.setScheduledDate(appointment.getScheduledDate());
        dto.setAddress(appointment.getHomeAddress());
        dto.setLatitude(appointment.getLatitude());
        dto.setLongitude(appointment.getLongitude());
        dto.setNotes(appointment.getNurseNotes());
        dto.setStatus(appointment.getStatus());
        dto.setNurseCurrentLatitude(appointment.getNurseCurrentLatitude());
        dto.setNurseCurrentLongitude(appointment.getNurseCurrentLongitude());
        dto.setLocationSharingEnabled(appointment.getLocationSharingEnabled());
        dto.setLocationLastUpdated(appointment.getLocationLastUpdated());
        dto.setEstimatedArrivalTime(appointment.getEstimatedArrivalTime());
        dto.setSamplingCompleted(appointment.getSamplingCompleted());

        return dto;
    }
}
