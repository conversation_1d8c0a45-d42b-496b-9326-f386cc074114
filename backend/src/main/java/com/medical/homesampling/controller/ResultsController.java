package com.medical.homesampling.controller;

import com.medical.homesampling.entity.Appointment;
import com.medical.homesampling.entity.AppointmentStatus;
import com.medical.homesampling.service.AppointmentService;
import com.medical.homesampling.service.EmailService;
import com.medical.homesampling.service.NotificationService;
import com.medical.homesampling.service.PdfService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/results")
@CrossOrigin(origins = "*", maxAge = 3600)
public class ResultsController {

    @Autowired
    private AppointmentService appointmentService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private PdfService pdfService;

    @Autowired
    private NotificationService notificationService;

    /**
     * Marque les résultats comme disponibles et envoie l'email avec PDF
     */
    @PostMapping("/appointment/{appointmentId}/publish")
    @PreAuthorize("hasRole('NURSE') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> publishResults(
            @PathVariable Long appointmentId,
            @RequestBody Map<String, String> request) {
        
        try {
            String comments = request.get("comments");
            
            // Récupérer le rendez-vous
            Appointment appointment = appointmentService.getAppointmentById(appointmentId);
            
            // Vérifier que le prélèvement est terminé
            if (appointment.getStatus() != AppointmentStatus.SAMPLING_DONE) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Le prélèvement doit être terminé avant de publier les résultats"
                ));
            }
            
            // Changer le statut à RESULTS_AVAILABLE
            appointment.setStatus(AppointmentStatus.RESULTS_AVAILABLE);
            appointmentService.updateAppointmentStatus(appointment.getId(), AppointmentStatus.RESULTS_AVAILABLE);
            
            // Envoyer l'email avec le PDF
            emailService.sendResultsEmail(appointment, comments);
            
            // Créer une notification
            notificationService.notifyResultsAvailable(appointment);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Résultats publiés et envoyés par email avec succès");
            response.put("appointmentId", appointmentId);
            response.put("patientEmail", appointment.getPatient().getEmail());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("Erreur lors de la publication des résultats: " + e.getMessage());
            e.printStackTrace();
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Erreur lors de la publication des résultats: " + e.getMessage()
            ));
        }
    }

    /**
     * Télécharge le PDF des résultats
     */
    @GetMapping("/appointment/{appointmentId}/download")
    @PreAuthorize("hasRole('PATIENT') or hasRole('NURSE') or hasRole('ADMIN')")
    public ResponseEntity<byte[]> downloadResultsPdf(
            @PathVariable Long appointmentId,
            @RequestParam(required = false) String comments) {
        
        try {
            // Récupérer le rendez-vous
            Appointment appointment = appointmentService.getAppointmentById(appointmentId);
            
            // Vérifier les permissions (patient peut seulement voir ses propres résultats)
            // TODO: Ajouter vérification de sécurité pour les patients
            
            // Générer le PDF
            byte[] pdfBytes = pdfService.generateResultsPdf(appointment, comments);
            
            // Nom du fichier
            String fileName = "Resultats_Analyses_" + appointmentId + "_" + 
                            appointment.getScheduledDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".pdf";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(pdfBytes.length);
            
            return new ResponseEntity<>(pdfBytes, headers, HttpStatus.OK);
            
        } catch (Exception e) {
            System.err.println("Erreur lors du téléchargement du PDF: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Renvoie l'email avec les résultats
     */
    @PostMapping("/appointment/{appointmentId}/resend-email")
    @PreAuthorize("hasRole('NURSE') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> resendResultsEmail(
            @PathVariable Long appointmentId,
            @RequestBody Map<String, String> request) {
        
        try {
            String comments = request.get("comments");
            
            // Récupérer le rendez-vous
            Appointment appointment = appointmentService.getAppointmentById(appointmentId);
            
            // Vérifier que les résultats sont disponibles
            if (appointment.getStatus() != AppointmentStatus.RESULTS_AVAILABLE) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Les résultats ne sont pas encore disponibles"
                ));
            }
            
            // Renvoyer l'email
            emailService.sendResultsEmail(appointment, comments);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Email renvoyé avec succès");
            response.put("patientEmail", appointment.getPatient().getEmail());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("Erreur lors du renvoi de l'email: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Erreur lors du renvoi de l'email: " + e.getMessage()
            ));
        }
    }

    /**
     * Obtient le statut des résultats pour un rendez-vous
     */
    @GetMapping("/appointment/{appointmentId}/status")
    @PreAuthorize("hasRole('PATIENT') or hasRole('NURSE') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getResultsStatus(@PathVariable Long appointmentId) {
        
        try {
            Appointment appointment = appointmentService.getAppointmentById(appointmentId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("appointmentId", appointmentId);
            response.put("status", appointment.getStatus());
            response.put("resultsAvailable", appointment.getStatus() == AppointmentStatus.RESULTS_AVAILABLE);
            response.put("patientEmail", appointment.getPatient().getEmail());
            
            if (appointment.getStatus() == AppointmentStatus.RESULTS_AVAILABLE) {
                response.put("message", "Résultats disponibles");
            } else {
                response.put("message", "Résultats en cours de traitement");
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "Erreur lors de la récupération du statut: " + e.getMessage()
            ));
        }
    }
}
