package com.medical.homesampling.controller;

import com.medical.homesampling.dto.NotificationResponseDto;
import com.medical.homesampling.entity.Notification;
import com.medical.homesampling.entity.User;
import com.medical.homesampling.mapper.NotificationMapper;
import com.medical.homesampling.service.NotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/notifications")
@Tag(name = "Notifications", description = "Notification management APIs")
@CrossOrigin(origins = "*", maxAge = 3600)
public class NotificationController {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private NotificationMapper notificationMapper;

    @GetMapping
    @Operation(summary = "Get user notifications", description = "Get all notifications for the current user")
    public ResponseEntity<List<NotificationResponseDto>> getUserNotifications(Authentication authentication) {
        try {
            System.out.println("=== getUserNotifications called ===");
            System.out.println("Authentication: " + authentication);
            System.out.println("Principal: " + authentication.getPrincipal());
            System.out.println("Principal class: " + authentication.getPrincipal().getClass());

            User user = (User) authentication.getPrincipal();
            System.out.println("User: " + user.getUsername() + " (ID: " + user.getId() + ")");

            List<Notification> notifications = notificationService.getUserNotifications(user);
            System.out.println("Notifications found: " + notifications.size());

            // Convertir en DTOs pour éviter la sérialisation des objets User complets
            List<NotificationResponseDto> notificationDtos = notificationMapper.toDtoList(notifications);
            System.out.println("DTOs created: " + notificationDtos.size());

            return ResponseEntity.ok(notificationDtos);
        } catch (Exception e) {
            System.err.println("Error in getUserNotifications: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    @GetMapping("/unread")
    @Operation(summary = "Get unread notifications", description = "Get unread notifications for the current user")
    public ResponseEntity<List<NotificationResponseDto>> getUnreadNotifications(Authentication authentication) {
        try {
            System.out.println("=== getUnreadNotifications called ===");
            User user = (User) authentication.getPrincipal();
            System.out.println("User: " + user.getUsername() + " (ID: " + user.getId() + ")");

            List<Notification> notifications = notificationService.getUnreadNotifications(user);
            System.out.println("Unread notifications found: " + notifications.size());

            // Convertir en DTOs
            List<NotificationResponseDto> notificationDtos = notificationMapper.toDtoList(notifications);

            return ResponseEntity.ok(notificationDtos);
        } catch (Exception e) {
            System.err.println("Error in getUnreadNotifications: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    @GetMapping("/unread/count")
    @Operation(summary = "Get unread count", description = "Get count of unread notifications for the current user")
    public ResponseEntity<Long> getUnreadCount(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Long count = notificationService.getUnreadCount(user);
        return ResponseEntity.ok(count);
    }

    @PutMapping("/{id}/read")
    @Operation(summary = "Mark notification as read", description = "Mark a specific notification as read")
    public ResponseEntity<Notification> markAsRead(@PathVariable Long id) {
        try {
            Notification notification = notificationService.markAsRead(id);
            return ResponseEntity.ok(notification);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/read-all")
    @Operation(summary = "Mark all as read", description = "Mark all notifications as read for the current user")
    public ResponseEntity<?> markAllAsRead(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        // This would need to be implemented in the service
        return ResponseEntity.ok(new MessageResponse("All notifications marked as read"));
    }

    // Message response class
    public static class MessageResponse {
        private String message;

        public MessageResponse(String message) {
            this.message = message;
        }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
