package com.medical.homesampling.controller;

import com.medical.homesampling.dto.AppointmentCreateDto;
import com.medical.homesampling.dto.AppointmentUpdateDto;
import com.medical.homesampling.dto.AppointmentResponseDto;
import com.medical.homesampling.dto.UserResponseDto;
import com.medical.homesampling.entity.Appointment;
import com.medical.homesampling.entity.AppointmentStatus;
import com.medical.homesampling.entity.Role;
import com.medical.homesampling.entity.User;
import com.medical.homesampling.service.AppointmentService;
import com.medical.homesampling.mapper.AppointmentMapper;
import com.medical.homesampling.repository.AppointmentRepository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import java.util.List;

@RestController
@RequestMapping("/appointments")
@Tag(name = "Appointment Management", description = "Appointment management APIs")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AppointmentController {

    @Autowired
    private AppointmentService appointmentService;

    @Autowired
    private AppointmentMapper appointmentMapper;

    @Autowired
    private AppointmentRepository appointmentRepository;

    @PostMapping
    // @PreAuthorize("hasRole('PATIENT')") // Temporairement désactivé pour test
    @Operation(summary = "Create appointment", description = "Create a new appointment (Patient only)")
    public ResponseEntity<?> createAppointment(@Valid @RequestBody AppointmentCreateDto createDto,
                                             Authentication authentication) {
        try {
            System.out.println("=== Création de rendez-vous ===");
            System.out.println("CreateDto reçu: " + createDto);

            User patient = (User) authentication.getPrincipal();
            System.out.println("Patient: " + patient.getUsername());

            Appointment appointment = appointmentService.createAppointment(createDto, patient);
            System.out.println("Rendez-vous créé avec ID: " + appointment.getId());

            // Utiliser le mapper pour retourner un DTO
            AppointmentResponseDto appointmentDto = appointmentMapper.toDto(appointment);
            return ResponseEntity.ok(appointmentDto);
        } catch (Exception e) {
            System.err.println("Erreur création rendez-vous: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @appointmentService.getAppointmentById(#id).patient.username == authentication.name or @appointmentService.getAppointmentById(#id).nurse.username == authentication.name")
    @Operation(summary = "Get appointment by ID", description = "Get appointment details by ID")
    public ResponseEntity<Appointment> getAppointmentById(@PathVariable Long id) {
        Appointment appointment = appointmentService.getAppointmentById(id);
        return ResponseEntity.ok(appointment);
    }

    @GetMapping("/my-appointments")
    @Operation(summary = "Get current user appointments", description = "Get appointments for the current user")
    public ResponseEntity<List<AppointmentResponseDto>> getCurrentUserAppointments(Authentication authentication) {
        try {
            System.out.println("=== getCurrentUserAppointments called ===");
            User user = (User) authentication.getPrincipal();
            System.out.println("User: " + user.getUsername() + " (Role: " + user.getRole() + ")");

            List<Appointment> appointments;

            if (user.getRole() == Role.PATIENT) {
                System.out.println("Getting appointments for patient");
                appointments = appointmentService.getAppointmentsByPatient(user);
            } else if (user.getRole() == Role.NURSE) {
                System.out.println("Getting appointments for nurse");
                appointments = appointmentService.getAppointmentsByNurse(user);
            } else {
                System.out.println("Getting appointments for admin (empty list)");
                // Admin can see all appointments
                appointments = appointmentService.getAppointmentsByPatient(user); // This will be empty for admin
            }

            System.out.println("Found " + appointments.size() + " appointments");

            // Mapper les entités vers les DTOs pour inclure tous les champs nécessaires
            List<AppointmentResponseDto> appointmentDtos = appointmentMapper.toDtoList(appointments);
            System.out.println("Mapped to " + appointmentDtos.size() + " DTOs");

            return ResponseEntity.ok(appointmentDtos);
        } catch (Exception e) {
            System.err.println("Error in getCurrentUserAppointments: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    @GetMapping("/nurse/{nurseId}")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('NURSE') and #nurseId == authentication.principal.id)")
    @Operation(summary = "Get appointments by nurse", description = "Get appointments assigned to a specific nurse")
    public ResponseEntity<List<Appointment>> getAppointmentsByNurse(@PathVariable Long nurseId) {
        // This would need the UserService to get the nurse by ID
        // For now, we'll assume the nurse is passed correctly
        return ResponseEntity.ok(List.of()); // Placeholder
    }

    @GetMapping("/upcoming")
    @PreAuthorize("hasRole('NURSE')")
    @Operation(summary = "Get upcoming appointments", description = "Get upcoming appointments for current nurse")
    public ResponseEntity<List<Appointment>> getUpcomingAppointments(Authentication authentication) {
        User nurse = (User) authentication.getPrincipal();
        List<Appointment> appointments = appointmentService.getUpcomingAppointmentsByNurse(nurse);
        return ResponseEntity.ok(appointments);
    }

    @GetMapping("/urgent")
    @PreAuthorize("hasRole('NURSE') or hasRole('ADMIN')")
    @Operation(summary = "Get urgent appointments", description = "Get urgent pending appointments")
    public ResponseEntity<List<Appointment>> getUrgentAppointments() {
        List<Appointment> appointments = appointmentService.getUrgentPendingAppointments();
        return ResponseEntity.ok(appointments);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @appointmentService.getAppointmentById(#id).nurse.username == authentication.name")
    @Operation(summary = "Update appointment", description = "Update appointment details")
    public ResponseEntity<?> updateAppointment(@PathVariable Long id,
                                             @Valid @RequestBody AppointmentUpdateDto updateDto) {
        try {
            Appointment appointment = appointmentService.updateAppointment(id, updateDto);
            return ResponseEntity.ok(appointment);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/assign-nurse/{nurseId}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Assign nurse to appointment", description = "Manually assign a nurse to an appointment (Admin only)")
    public ResponseEntity<?> assignNurse(@PathVariable Long id, @PathVariable Long nurseId) {
        try {
            Appointment appointment = appointmentService.assignNurseManually(id, nurseId);
            return ResponseEntity.ok(appointment);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/cancel")
    @PreAuthorize("hasRole('ADMIN') or @appointmentService.getAppointmentById(#id).patient.username == authentication.name")
    @Operation(summary = "Cancel appointment", description = "Cancel an appointment")
    public ResponseEntity<?> cancelAppointment(@PathVariable Long id, @RequestParam String reason) {
        try {
            appointmentService.cancelAppointment(id, reason);
            return ResponseEntity.ok(new MessageResponse("Appointment cancelled successfully!"));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    // ========== MÉTHODES ADMIN ==========

    @GetMapping("/admin/all")
    @Operation(summary = "Get all appointments (Admin)", description = "Get all appointments for admin management")
    // @PreAuthorize("hasAuthority('ROLE_ADMIN')") // Temporairement désactivé pour test
    public ResponseEntity<List<AppointmentResponseDto>> getAllAppointments() {
        try {
            // Test avec une liste vide d'abord
            System.out.println("=== Test getAllAppointments ===");

            // Utiliser les vraies données
            List<Appointment> appointments = appointmentService.getAllAppointments();
            System.out.println("All appointments récupérés: " + appointments.size());

            List<AppointmentResponseDto> appointmentDtos = appointmentMapper.toDtoList(appointments);
            System.out.println("DTOs mappés: " + appointmentDtos.size());

            return ResponseEntity.ok(appointmentDtos);
        } catch (Exception e) {
            System.err.println("Erreur dans getAllAppointments: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    @GetMapping("/admin/pending")
    @Operation(summary = "Get pending appointments (Admin)", description = "Get all pending appointments waiting for nurse assignment")
    // @PreAuthorize("hasAuthority('ROLE_ADMIN')") // Temporairement désactivé pour test
    public ResponseEntity<List<AppointmentResponseDto>> getPendingAppointments() {
        try {
            // Test avec une liste vide d'abord
            System.out.println("=== Test getPendingAppointments ===");

            // Étape 4: Utiliser les vraies données avec le mapper
            List<Appointment> appointments = appointmentService.getPendingAppointments();
            System.out.println("Pending appointments récupérés: " + appointments.size());

            if (appointments.isEmpty()) {
                System.out.println("Aucun rendez-vous en attente trouvé dans la DB");
                return ResponseEntity.ok(new ArrayList<>());
            }

            // Utiliser le mapper pour convertir les vraies données
            List<AppointmentResponseDto> appointmentDtos = appointmentMapper.toDtoList(appointments);
            System.out.println("DTOs mappés: " + appointmentDtos.size());

            return ResponseEntity.ok(appointmentDtos);
        } catch (Exception e) {
            System.err.println("Erreur dans getPendingAppointments: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    @PostMapping("/admin/{appointmentId}/assign-nurse/{nurseId}")
    @Operation(summary = "Assign nurse to appointment (Admin)", description = "Manually assign a nurse to an appointment")
    // @PreAuthorize("hasAuthority('ROLE_ADMIN')") // Temporairement désactivé pour test
    public ResponseEntity<Appointment> assignNurseToAppointment(
            @PathVariable Long appointmentId,
            @PathVariable Long nurseId) {
        Appointment appointment = appointmentService.assignNurseToAppointment(appointmentId, nurseId);
        return ResponseEntity.ok(appointment);
    }

    @PostMapping("/admin/{appointmentId}/auto-assign")
    @Operation(summary = "Auto-assign nearest nurse (Admin)", description = "Automatically assign the nearest available nurse to an appointment")
    // @PreAuthorize("hasAuthority('ROLE_ADMIN')") // Temporairement désactivé pour test
    public ResponseEntity<Appointment> autoAssignNearestNurse(@PathVariable Long appointmentId) {
        Appointment appointment = appointmentService.autoAssignNearestNurse(appointmentId);
        return ResponseEntity.ok(appointment);
    }

    @PostMapping("/admin/auto-assign-all")
    @Operation(summary = "Auto-assign all pending appointments (Admin)", description = "Automatically assign nurses to all pending appointments")
    // @PreAuthorize("hasAuthority('ROLE_ADMIN')") // Temporairement désactivé pour test
    public ResponseEntity<Map<String, Object>> autoAssignAllPendingAppointments() {
        Map<String, Object> result = appointmentService.autoAssignAllPendingAppointments();
        return ResponseEntity.ok(result);
    }

    @PutMapping("/admin/{appointmentId}/status")
    @Operation(summary = "Update appointment status (Admin)", description = "Update the status of an appointment")
    // @PreAuthorize("hasAuthority('ROLE_ADMIN')") // Temporairement désactivé pour test
    public ResponseEntity<Appointment> updateAppointmentStatus(
            @PathVariable Long appointmentId,
            @RequestParam AppointmentStatus status) {
        Appointment appointment = appointmentService.updateAppointmentStatus(appointmentId, status);
        return ResponseEntity.ok(appointment);
    }

    @GetMapping("/admin/nurses/available")
    @Operation(summary = "Get available nurses (Admin)", description = "Get list of available nurses for assignment")
    // @PreAuthorize("hasAuthority('ROLE_ADMIN')") // Temporairement désactivé pour test
    public ResponseEntity<List<UserResponseDto>> getAvailableNurses() {
        try {
            List<User> nurses = appointmentService.getAvailableNurses();
            List<UserResponseDto> nurseDtos = appointmentMapper.toUserDtoList(nurses);
            return ResponseEntity.ok(nurseDtos);
        } catch (Exception e) {
            System.err.println("Erreur dans getAvailableNurses: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    @GetMapping("/nurse-appointments")
    @Operation(summary = "Get nurse appointments", description = "Get all appointments assigned to the current nurse")
    // @PreAuthorize("hasRole('NURSE')") // Temporairement désactivé pour test
    public ResponseEntity<List<AppointmentResponseDto>> getNurseAppointments(Authentication authentication) {
        try {
            System.out.println("=== getNurseAppointments called ===");
            User nurse = (User) authentication.getPrincipal();
            System.out.println("Nurse: " + nurse.getUsername() + " (ID: " + nurse.getId() + ")");

            List<Appointment> appointments = appointmentService.getNurseAppointments(nurse);
            System.out.println("Nurse appointments found: " + appointments.size());

            List<AppointmentResponseDto> appointmentDtos = appointmentMapper.toDtoList(appointments);
            System.out.println("DTOs mapped: " + appointmentDtos.size());

            return ResponseEntity.ok(appointmentDtos);
        } catch (Exception e) {
            System.err.println("Error in getNurseAppointments: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "Update appointment status", description = "Update the status of an appointment")
    public ResponseEntity<AppointmentResponseDto> updateAppointmentStatus(
            @PathVariable Long id,
            @RequestBody Map<String, String> statusUpdate,
            Authentication authentication) {
        try {
            System.out.println("=== updateAppointmentStatus called ===");
            System.out.println("Appointment ID: " + id);
            System.out.println("New status: " + statusUpdate.get("status"));

            String statusStr = statusUpdate.get("status");
            AppointmentStatus status = AppointmentStatus.valueOf(statusStr);

            Appointment appointment = appointmentService.updateAppointmentStatus(id, status);
            AppointmentResponseDto responseDto = appointmentMapper.toDto(appointment);

            return ResponseEntity.ok(responseDto);
        } catch (Exception e) {
            System.err.println("Error in updateAppointmentStatus: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    @PostMapping("/{id}/start-tracking")
    @Operation(summary = "Start location tracking", description = "Start sharing nurse location for real-time tracking")
    public ResponseEntity<AppointmentResponseDto> startLocationTracking(
            @PathVariable Long id,
            Authentication authentication) {
        try {
            System.out.println("=== startLocationTracking called ===");
            System.out.println("Appointment ID: " + id);

            User nurse = (User) authentication.getPrincipal();
            Appointment appointment = appointmentService.startLocationTracking(id, nurse);
            AppointmentResponseDto responseDto = appointmentMapper.toDto(appointment);

            return ResponseEntity.ok(responseDto);
        } catch (Exception e) {
            System.err.println("Error in startLocationTracking: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    @PutMapping("/{id}/status-and-sharing")
    @Operation(summary = "Update appointment status and location sharing", description = "Update both status and location sharing for an appointment")
    public ResponseEntity<AppointmentResponseDto> updateAppointmentStatusAndSharing(
            @PathVariable Long id,
            @RequestBody Map<String, Object> updateData,
            Authentication authentication) {
        try {
            System.out.println("🔄 Updating appointment " + id + " with status and sharing data: " + updateData);

            String status = (String) updateData.get("status");
            Boolean locationSharingEnabled = (Boolean) updateData.get("locationSharingEnabled");

            if (status == null || locationSharingEnabled == null) {
                System.err.println("❌ Missing required fields: status=" + status + ", locationSharingEnabled=" + locationSharingEnabled);
                return ResponseEntity.badRequest().build();
            }

            AppointmentStatus appointmentStatus = AppointmentStatus.valueOf(status);

            Optional<Appointment> appointmentOpt = appointmentRepository.findById(id);
            if (appointmentOpt.isEmpty()) {
                System.err.println("❌ Appointment not found: " + id);
                return ResponseEntity.notFound().build();
            }

            Appointment appointment = appointmentOpt.get();
            System.out.println("📋 Before update - Status: " + appointment.getStatus() + ", Sharing: " + appointment.getLocationSharingEnabled());

            // Mettre à jour les deux champs
            appointment.setStatus(appointmentStatus);
            appointment.setLocationSharingEnabled(locationSharingEnabled);

            Appointment savedAppointment = appointmentRepository.save(appointment);
            System.out.println("📋 After update - Status: " + savedAppointment.getStatus() + ", Sharing: " + savedAppointment.getLocationSharingEnabled());

            AppointmentResponseDto responseDto = appointmentMapper.toDto(savedAppointment);

            System.out.println("✅ Successfully updated appointment " + id + " with status " + status + " and sharing " + locationSharingEnabled);
            return ResponseEntity.ok(responseDto);

        } catch (Exception e) {
            System.err.println("❌ Error in updateAppointmentStatusAndSharing: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    @PutMapping("/{id}/update-location")
    @Operation(summary = "Update nurse location", description = "Update the current location of the nurse")
    public ResponseEntity<AppointmentResponseDto> updateNurseLocation(
            @PathVariable Long id,
            @RequestBody Map<String, Double> locationData,
            Authentication authentication) {
        try {
            System.out.println("=== updateNurseLocation called ===");
            System.out.println("Appointment ID: " + id);
            System.out.println("Location: " + locationData.get("latitude") + ", " + locationData.get("longitude"));

            User nurse = (User) authentication.getPrincipal();
            Double latitude = locationData.get("latitude");
            Double longitude = locationData.get("longitude");

            Appointment appointment = appointmentService.updateNurseLocation(id, nurse, latitude, longitude);
            AppointmentResponseDto responseDto = appointmentMapper.toDto(appointment);

            return ResponseEntity.ok(responseDto);
        } catch (Exception e) {
            System.err.println("Error in updateNurseLocation: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    @PostMapping("/{id}/stop-tracking")
    @Operation(summary = "Stop location tracking", description = "Stop sharing nurse location after sampling")
    public ResponseEntity<AppointmentResponseDto> stopLocationTracking(
            @PathVariable Long id,
            Authentication authentication) {
        try {
            System.out.println("=== stopLocationTracking called ===");
            System.out.println("Appointment ID: " + id);

            User nurse = (User) authentication.getPrincipal();
            Appointment appointment = appointmentService.stopLocationTracking(id, nurse);
            AppointmentResponseDto responseDto = appointmentMapper.toDto(appointment);

            return ResponseEntity.ok(responseDto);
        } catch (Exception e) {
            System.err.println("Error in stopLocationTracking: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    @PostMapping("/{id}/force-enable-sharing")
    @Operation(summary = "Force enable location sharing", description = "Force enable location sharing for debugging")
    public ResponseEntity<Map<String, Object>> forceEnableLocationSharing(@PathVariable Long id) {
        System.out.println("🔧 Force enabling location sharing for appointment: " + id);

        try {
            Optional<Appointment> appointmentOpt = appointmentRepository.findById(id);
            if (appointmentOpt.isEmpty()) {
                System.out.println("❌ Appointment not found: " + id);
                return ResponseEntity.notFound().build();
            }

            Appointment appointment = appointmentOpt.get();
            System.out.println("📋 Before - Status: " + appointment.getStatus() + ", Sharing: " + appointment.getLocationSharingEnabled());

            // Forcer l'activation du partage
            appointment.setLocationSharingEnabled(true);

            Appointment savedAppointment = appointmentRepository.save(appointment);
            System.out.println("📋 After - Status: " + savedAppointment.getStatus() + ", Sharing: " + savedAppointment.getLocationSharingEnabled());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Partage de position forcé avec succès");
            response.put("appointmentId", id);
            response.put("locationSharingEnabled", savedAppointment.getLocationSharingEnabled());
            response.put("status", savedAppointment.getStatus().toString());

            System.out.println("✅ Location sharing force-enabled for appointment: " + id);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("❌ Error force-enabling location sharing: " + e.getMessage());
            e.printStackTrace();

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur lors de l'activation forcée: " + e.getMessage());

            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    @GetMapping("/all-with-locations")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get all appointments with patient locations for admin tracking")
    public ResponseEntity<List<AppointmentResponseDto>> getAllAppointmentsWithLocations() {
        try {
            System.out.println("🗺️ ADMIN - Récupération de tous les rendez-vous avec positions");

            // Récupérer tous les rendez-vous avec leurs patients et infirmiers
            List<Appointment> appointments = appointmentRepository.findAllWithPatientsAndNurses();

            // Convertir en DTO avec toutes les informations
            List<AppointmentResponseDto> appointmentDtos = new ArrayList<>();
            for (Appointment appointment : appointments) {
                AppointmentResponseDto dto = appointmentMapper.toDto(appointment);
                appointmentDtos.add(dto);
            }

            System.out.println("📍 ADMIN - " + appointmentDtos.size() + " rendez-vous avec positions récupérés");
            return ResponseEntity.ok(appointmentDtos);

        } catch (Exception e) {
            System.err.println("❌ ADMIN - Erreur récupération rendez-vous avec positions: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    // Message response class
    public static class MessageResponse {
        private String message;

        public MessageResponse(String message) {
            this.message = message;
        }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
