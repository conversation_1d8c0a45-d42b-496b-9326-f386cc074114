package com.medical.homesampling.controller;

import com.medical.homesampling.entity.AnalysisType;
import com.medical.homesampling.repository.AnalysisTypeRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/analysis-types")
@Tag(name = "Analysis Types", description = "Analysis types management APIs")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AnalysisTypeController {

    @Autowired
    private AnalysisTypeRepository analysisTypeRepository;

    @GetMapping
    @Operation(summary = "Get all analysis types", description = "Get all available analysis types")
    public ResponseEntity<List<AnalysisType>> getAllAnalysisTypes() {
        List<AnalysisType> analysisTypes = analysisTypeRepository.findAll();
        return ResponseEntity.ok(analysisTypes);
    }

    @GetMapping("/active")
    @Operation(summary = "Get active analysis types", description = "Get all active analysis types")
    public ResponseEntity<List<AnalysisType>> getActiveAnalysisTypes() {
        List<AnalysisType> analysisTypes = analysisTypeRepository.findByIsActiveTrueOrderByNameAsc();
        return ResponseEntity.ok(analysisTypes);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get analysis type by ID", description = "Get analysis type details by ID")
    public ResponseEntity<AnalysisType> getAnalysisTypeById(@PathVariable Long id) {
        return analysisTypeRepository.findById(id)
                .map(analysisType -> ResponseEntity.ok(analysisType))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    @Operation(summary = "Search analysis types", description = "Search analysis types by name")
    public ResponseEntity<List<AnalysisType>> searchAnalysisTypes(@RequestParam String name) {
        List<AnalysisType> analysisTypes = analysisTypeRepository.findByNameContainingIgnoreCase(name);
        return ResponseEntity.ok(analysisTypes);
    }
}
