package com.medical.homesampling.controller;

import com.medical.homesampling.dto.UserRegistrationDto;
import com.medical.homesampling.dto.UserUpdateDto;
import com.medical.homesampling.entity.User;
import com.medical.homesampling.entity.Role;
import com.medical.homesampling.service.UserService;
import com.medical.homesampling.service.AdminSecurityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

// import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.HashMap;

@RestController
@RequestMapping("/admin/nurses")
@PreAuthorize("hasRole('ADMIN')")
@CrossOrigin(origins = "*")
public class AdminNurseController {

    @Autowired
    private UserService userService;

    @Autowired
    private AdminSecurityService adminSecurityService;

    /**
     * Récupère tous les infirmiers
     */
    @GetMapping
    public ResponseEntity<List<User>> getAllNurses() {
        try {
            List<User> nurses = userService.getUsersByRole(Role.NURSE);
            
            // Log de sécurité
            adminSecurityService.logSecurityEvent(
                "NURSES_LIST_ACCESSED", 
                "NURSE_MANAGEMENT", 
                Map.of("nurseCount", nurses.size())
            );
            
            return ResponseEntity.ok(nurses);
        } catch (Exception e) {
            adminSecurityService.logSecurityEvent(
                "NURSES_LIST_ACCESS_FAILED", 
                "NURSE_MANAGEMENT", 
                Map.of("error", e.getMessage())
            );
            throw e;
        }
    }

    /**
     * Récupère un infirmier par ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<User> getNurseById(@PathVariable Long id) {
        try {
            User nurse = userService.getUserById(id);
            
            if (nurse.getRole() != Role.NURSE) {
                return ResponseEntity.notFound().build();
            }
            
            adminSecurityService.logSecurityEvent(
                "NURSE_DETAILS_ACCESSED", 
                "NURSE_MANAGEMENT", 
                Map.of("nurseId", id)
            );
            
            return ResponseEntity.ok(nurse);
        } catch (Exception e) {
            adminSecurityService.logSecurityEvent(
                "NURSE_DETAILS_ACCESS_FAILED", 
                "NURSE_MANAGEMENT", 
                Map.of("nurseId", id, "error", e.getMessage())
            );
            throw e;
        }
    }

    /**
     * Test endpoint pour vérifier la connectivité
     */
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testEndpoint() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "AdminNurseController POST endpoint is working");
        response.put("timestamp", java.time.LocalDateTime.now());
        return ResponseEntity.ok(response);
    }

    /**
     * Crée un nouvel infirmier
     */
    @PostMapping
    public ResponseEntity<User> createNurse(@RequestBody UserRegistrationDto nurseDto) {
        try {
            // Forcer le rôle NURSE
            nurseDto.setRole(Role.NURSE);
            
            User newNurse = userService.createUser(nurseDto);
            
            adminSecurityService.logSecurityEvent(
                "NURSE_CREATED", 
                "NURSE_MANAGEMENT", 
                Map.of(
                    "nurseId", newNurse.getId(),
                    "nurseName", newNurse.getFirstName() + " " + newNurse.getLastName(),
                    "email", newNurse.getEmail()
                )
            );
            
            return ResponseEntity.ok(newNurse);
        } catch (Exception e) {
            adminSecurityService.logSecurityEvent(
                "NURSE_CREATION_FAILED", 
                "NURSE_MANAGEMENT", 
                Map.of("email", nurseDto.getEmail(), "error", e.getMessage())
            );
            throw e;
        }
    }

    /**
     * Met à jour un infirmier
     */
    @PutMapping("/{id}")
    public ResponseEntity<User> updateNurse(@PathVariable Long id, @RequestBody UserUpdateDto updateDto) {
        try {
            User existingNurse = userService.getUserById(id);
            
            if (existingNurse.getRole() != Role.NURSE) {
                return ResponseEntity.notFound().build();
            }
            
            User updatedNurse = userService.updateUser(id, updateDto);
            
            adminSecurityService.logSecurityEvent(
                "NURSE_UPDATED", 
                "NURSE_MANAGEMENT", 
                Map.of(
                    "nurseId", id,
                    "nurseName", updatedNurse.getFirstName() + " " + updatedNurse.getLastName()
                )
            );
            
            return ResponseEntity.ok(updatedNurse);
        } catch (Exception e) {
            adminSecurityService.logSecurityEvent(
                "NURSE_UPDATE_FAILED", 
                "NURSE_MANAGEMENT", 
                Map.of("nurseId", id, "error", e.getMessage())
            );
            throw e;
        }
    }

    /**
     * Supprime un infirmier
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteNurse(@PathVariable Long id) {
        try {
            User nurse = userService.getUserById(id);
            
            if (nurse.getRole() != Role.NURSE) {
                return ResponseEntity.notFound().build();
            }
            
            String nurseName = nurse.getFirstName() + " " + nurse.getLastName();
            
            userService.deleteUser(id);
            
            adminSecurityService.logSecurityEvent(
                "NURSE_DELETED", 
                "NURSE_MANAGEMENT", 
                Map.of(
                    "nurseId", id,
                    "nurseName", nurseName
                )
            );
            
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            adminSecurityService.logSecurityEvent(
                "NURSE_DELETION_FAILED", 
                "NURSE_MANAGEMENT", 
                Map.of("nurseId", id, "error", e.getMessage())
            );
            throw e;
        }
    }

    /**
     * Recherche des infirmiers avec filtres
     */
    @GetMapping("/search")
    public ResponseEntity<List<User>> searchNurses(
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String availability) {
        try {
            List<User> nurses = userService.searchNurses(search, status, availability);
            
            adminSecurityService.logSecurityEvent(
                "NURSES_SEARCH_PERFORMED", 
                "NURSE_MANAGEMENT", 
                Map.of(
                    "searchTerm", search != null ? search : "",
                    "statusFilter", status != null ? status : "all",
                    "availabilityFilter", availability != null ? availability : "all",
                    "resultsCount", nurses.size()
                )
            );
            
            return ResponseEntity.ok(nurses);
        } catch (Exception e) {
            adminSecurityService.logSecurityEvent(
                "NURSES_SEARCH_FAILED", 
                "NURSE_MANAGEMENT", 
                Map.of("error", e.getMessage())
            );
            throw e;
        }
    }

    /**
     * Récupère les statistiques des infirmiers
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getNurseStatistics() {
        try {
            Map<String, Object> stats = userService.getNurseStatistics();
            
            adminSecurityService.logSecurityEvent(
                "NURSE_STATISTICS_ACCESSED", 
                "NURSE_MANAGEMENT", 
                Map.of("statsRequested", true)
            );
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            adminSecurityService.logSecurityEvent(
                "NURSE_STATISTICS_ACCESS_FAILED", 
                "NURSE_MANAGEMENT", 
                Map.of("error", e.getMessage())
            );
            throw e;
        }
    }
}
