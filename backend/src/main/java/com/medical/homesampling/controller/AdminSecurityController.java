package com.medical.homesampling.controller;

import com.medical.homesampling.service.AdminSecurityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/admin/security")
@PreAuthorize("hasRole('ADMIN')")
@CrossOrigin(origins = "*")
public class AdminSecurityController {

    @Autowired
    private AdminSecurityService adminSecurityService;

    /**
     * Endpoint pour enregistrer les événements de sécurité
     */
    @PostMapping("/events")
    public ResponseEntity<Map<String, Object>> logSecurityEvent(@RequestBody Map<String, Object> eventData) {
        try {
            String action = (String) eventData.get("action");
            String resource = (String) eventData.get("resource");
            @SuppressWarnings("unchecked")
            Map<String, Object> details = (Map<String, Object>) eventData.get("details");
            
            // Log l'événement de sécurité
            adminSecurityService.logSecurityEvent(action, resource, details);
            
            // Retourner une réponse de succès
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Security event logged successfully",
                "action", action,
                "resource", resource
            ));
            
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Failed to log security event: " + e.getMessage()
            ));
        }
    }

    /**
     * Endpoint pour récupérer les événements de sécurité (placeholder)
     */
    @GetMapping("/events")
    public ResponseEntity<Map<String, Object>> getSecurityEvents(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        // Pour l'instant, retourner une liste vide
        // Dans une vraie implémentation, on récupérerait les événements depuis la base de données
        return ResponseEntity.ok(Map.of(
            "events", java.util.Collections.emptyList(),
            "totalElements", 0,
            "totalPages", 0,
            "currentPage", page,
            "size", size
        ));
    }

    /**
     * Endpoint pour les sessions actives (placeholder)
     */
    @GetMapping("/sessions")
    public ResponseEntity<java.util.List<Object>> getActiveSessions() {
        // Pour l'instant, retourner une liste vide
        return ResponseEntity.ok(java.util.Collections.emptyList());
    }

    /**
     * Endpoint pour prolonger la session (placeholder)
     */
    @PostMapping("/extend-session")
    public ResponseEntity<Map<String, Object>> extendSession() {
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Session extended successfully"
        ));
    }

    /**
     * Endpoint pour valider l'accès IP (placeholder)
     */
    @PostMapping("/validate-ip")
    public ResponseEntity<Map<String, Object>> validateIPAccess(@RequestBody Map<String, String> request) {
        // Pour l'instant, toujours autoriser
        return ResponseEntity.ok(Map.of(
            "allowed", true
        ));
    }

    /**
     * Endpoint pour les tentatives de connexion suspectes (placeholder)
     */
    @GetMapping("/suspicious-logins")
    public ResponseEntity<java.util.List<Object>> getSuspiciousLoginAttempts() {
        return ResponseEntity.ok(java.util.Collections.emptyList());
    }

    /**
     * Endpoint pour bloquer une IP (placeholder)
     */
    @PostMapping("/block-ip")
    public ResponseEntity<Map<String, Object>> blockIPAddress(@RequestBody Map<String, String> request) {
        String ipAddress = request.get("ipAddress");
        String reason = request.get("reason");
        
        adminSecurityService.logSecurityEvent("IP_BLOCKED", "SECURITY", 
            Map.of("ipAddress", ipAddress, "reason", reason));
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "IP address blocked successfully"
        ));
    }

    /**
     * Endpoint pour débloquer une IP (placeholder)
     */
    @DeleteMapping("/block-ip/{ipAddress}")
    public ResponseEntity<Map<String, Object>> unblockIPAddress(@PathVariable String ipAddress) {
        adminSecurityService.logSecurityEvent("IP_UNBLOCKED", "SECURITY", 
            Map.of("ipAddress", ipAddress));
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "IP address unblocked successfully"
        ));
    }
}
