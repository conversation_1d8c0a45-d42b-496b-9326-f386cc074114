package com.medical.homesampling.controller;

import com.medical.homesampling.dto.JwtResponse;
import com.medical.homesampling.dto.LoginRequest;
import com.medical.homesampling.entity.User;
import com.medical.homesampling.entity.Role;
import com.medical.homesampling.entity.AnalysisType;
import com.medical.homesampling.entity.Symptom;
import com.medical.homesampling.repository.UserRepository;
import com.medical.homesampling.repository.AnalysisTypeRepository;
import com.medical.homesampling.repository.SymptomRepository;
import com.medical.homesampling.security.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;

import java.util.Optional;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/auth")
@Tag(name = "Authentication", description = "Authentication management APIs")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AnalysisTypeRepository analysisTypeRepository;

    @Autowired
    private SymptomRepository symptomRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @PostMapping("/login")
    @Operation(summary = "User login", description = "Authenticate user and return JWT token")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword())
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);
            String jwt = jwtUtil.generateToken((User) authentication.getPrincipal());

            User userDetails = (User) authentication.getPrincipal();
            
            return ResponseEntity.ok(new JwtResponse(jwt,
                                                   userDetails.getId(),
                                                   userDetails.getUsername(),
                                                   userDetails.getEmail(),
                                                   userDetails.getFirstName(),
                                                   userDetails.getLastName(),
                                                   userDetails.getRole(),
                                                   userDetails.getIsSuperAdmin()));
        } catch (Exception e) {
            // Log détaillé pour diagnostic
            System.err.println("🔍 ERREUR DE CONNEXION:");
            System.err.println("   Username: " + loginRequest.getUsername());
            System.err.println("   Erreur: " + e.getMessage());
            System.err.println("   Type: " + e.getClass().getSimpleName());

            // Vérifier si l'utilisateur existe
            Optional<User> userOpt = userRepository.findByUsername(loginRequest.getUsername());
            if (userOpt.isEmpty()) {
                System.err.println("   ❌ Utilisateur non trouvé dans la base");
                return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Utilisateur non trouvé. Avez-vous vérifié votre email après inscription ?"));
            } else {
                User user = userOpt.get();
                System.err.println("   ✅ Utilisateur trouvé:");
                System.err.println("      - ID: " + user.getId());
                System.err.println("      - Email: " + user.getEmail());
                System.err.println("      - Rôle: " + user.getRole());
                System.err.println("      - Activé: " + user.getEnabled());
                System.err.println("      - Email vérifié: " + user.getEmailVerified());

                if (!user.getEnabled()) {
                    return ResponseEntity.badRequest()
                        .body(new MessageResponse("Error: Compte désactivé. Contactez l'administrateur."));
                }

                if (!user.getEmailVerified()) {
                    return ResponseEntity.badRequest()
                        .body(new MessageResponse("Error: Email non vérifié. Vérifiez votre boîte email."));
                }
            }

            return ResponseEntity.badRequest()
                .body(new MessageResponse("Error: Nom d'utilisateur ou mot de passe incorrect!"));
        }
    }

    @PostMapping("/logout")
    @Operation(summary = "User logout", description = "Logout user (client-side token removal)")
    public ResponseEntity<?> logoutUser() {
        return ResponseEntity.ok(new MessageResponse("User logged out successfully!"));
    }

    @GetMapping("/init-admin")
    @Operation(summary = "Initialize admin user", description = "Create admin user for testing")
    public ResponseEntity<Map<String, Object>> initializeAdmin() {
        Map<String, Object> response = new HashMap<>();

        try {
            // Delete existing admin user if exists
            userRepository.findByUsername("admin").ifPresent(user -> {
                userRepository.delete(user);
                response.put("admin_deleted", true);
            });

            // Create admin user with properly encoded password
            User admin = new User();
            admin.setUsername("admin");
            admin.setEmail("<EMAIL>");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setFirstName("Admin");
            admin.setLastName("System");
            admin.setPhone("+33123456789");
            admin.setRole(Role.ADMIN);
            admin.setEnabled(true);
            admin.setCreatedAt(LocalDateTime.now());
            userRepository.save(admin);
            response.put("admin_created", true);
            response.put("password_hash", admin.getPassword());

            // Create nurse user
            if (userRepository.findByUsername("nurse1").isEmpty()) {
                User nurse = new User();
                nurse.setUsername("nurse1");
                nurse.setEmail("<EMAIL>");
                nurse.setPassword(passwordEncoder.encode("nurse123"));
                nurse.setFirstName("Marie");
                nurse.setLastName("Dubois");
                nurse.setPhone("+33123456790");
                nurse.setRole(Role.NURSE);
                nurse.setEnabled(true);
                nurse.setAddress("123 Rue de la Santé, 75001 Paris");
                nurse.setLatitude(48.8566);
                nurse.setLongitude(2.3522);
                nurse.setCreatedAt(LocalDateTime.now());
                userRepository.save(nurse);
                response.put("nurse_created", true);
            } else {
                response.put("nurse_exists", true);
            }

            // Create patient user
            if (userRepository.findByUsername("patient1").isEmpty()) {
                User patient = new User();
                patient.setUsername("patient1");
                patient.setEmail("<EMAIL>");
                patient.setPassword(passwordEncoder.encode("patient123"));
                patient.setFirstName("Jean");
                patient.setLastName("Martin");
                patient.setPhone("+33123456791");
                patient.setRole(Role.PATIENT);
                patient.setEnabled(true);
                patient.setAddress("456 Avenue des Patients, 75002 Paris");
                patient.setLatitude(48.8606);
                patient.setLongitude(2.3376);
                patient.setCreatedAt(LocalDateTime.now());
                userRepository.save(patient);
                response.put("patient_created", true);
            } else {
                response.put("patient_exists", true);
            }

            response.put("success", true);
            response.put("message", "Test data initialized successfully");
            response.put("users_count", userRepository.count());

        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    @GetMapping("/fix-analysis-types")
    @Operation(summary = "Fix analysis types", description = "Set isActive=true for all analysis types")
    public ResponseEntity<Map<String, Object>> fixAnalysisTypes() {
        Map<String, Object> response = new HashMap<>();

        try {
            // Get all analysis types and set isActive to true
            var analysisTypes = analysisTypeRepository.findAll();
            int updated = 0;

            for (com.medical.homesampling.entity.AnalysisType analysisType : analysisTypes) {
                if (analysisType.getIsActive() == null) {
                    analysisType.setIsActive(true);
                    analysisTypeRepository.save(analysisType);
                    updated++;
                }
            }

            response.put("success", true);
            response.put("total_analysis_types", analysisTypes.size());
            response.put("updated_count", updated);
            response.put("message", "Analysis types fixed successfully!");

        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    // Simple message response class
    public static class MessageResponse {
        private String message;

        public MessageResponse(String message) {
            this.message = message;
        }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
