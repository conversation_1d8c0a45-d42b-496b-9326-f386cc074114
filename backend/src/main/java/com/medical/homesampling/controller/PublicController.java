package com.medical.homesampling.controller;

import com.medical.homesampling.entity.User;
import com.medical.homesampling.entity.Role;
import com.medical.homesampling.entity.AnalysisType;
import com.medical.homesampling.repository.UserRepository;
import com.medical.homesampling.repository.AnalysisTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/public")
@CrossOrigin(origins = "*")
public class PublicController {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AnalysisTypeRepository analysisTypeRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @GetMapping("/hello")
    @PreAuthorize("permitAll()")
    public ResponseEntity<String> hello() {
        return ResponseEntity.ok("Hello from Public Controller! Backend is working with MySQL on port 3306.");
    }

    @PostMapping("/create-admin")
    @PreAuthorize("permitAll()")
    public ResponseEntity<Map<String, Object>> createAdmin() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Delete existing admin user if exists
            userRepository.findByUsername("admin").ifPresent(user -> {
                userRepository.delete(user);
                response.put("admin_deleted", true);
            });
            
            // Create admin user with properly encoded password
            User admin = new User();
            admin.setUsername("admin");
            admin.setEmail("<EMAIL>");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setFirstName("Admin");
            admin.setLastName("System");
            admin.setPhone("+33123456789");
            admin.setRole(Role.ADMIN);
            admin.setEnabled(true);
            admin.setCreatedAt(LocalDateTime.now());
            userRepository.save(admin);
            
            response.put("success", true);
            response.put("admin_created", true);
            response.put("username", "admin");
            response.put("password", "admin123");
            response.put("message", "Admin user created successfully!");
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("backend_status", "running");
        status.put("database", "MySQL");
        status.put("port", 3306);
        status.put("users_count", userRepository.count());
        status.put("analysis_types_count", analysisTypeRepository.count());
        status.put("timestamp", LocalDateTime.now());
        return ResponseEntity.ok(status);
    }

    @PostMapping("/fix-analysis-types")
    public ResponseEntity<Map<String, Object>> fixAnalysisTypes() {
        Map<String, Object> response = new HashMap<>();

        try {
            // Get all analysis types and set isActive to true
            var analysisTypes = analysisTypeRepository.findAll();
            int updated = 0;

            for (AnalysisType analysisType : analysisTypes) {
                if (analysisType.getIsActive() == null) {
                    analysisType.setIsActive(true);
                    analysisTypeRepository.save(analysisType);
                    updated++;
                }
            }

            response.put("success", true);
            response.put("total_analysis_types", analysisTypes.size());
            response.put("updated_count", updated);
            response.put("message", "Analysis types fixed successfully!");

        } catch (Exception e) {
            response.put("success", false);
            response.put("error", e.getMessage());
        }

        return ResponseEntity.ok(response);
    }
}
