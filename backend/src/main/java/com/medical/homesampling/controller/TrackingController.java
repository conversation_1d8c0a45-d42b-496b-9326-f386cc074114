package com.medical.homesampling.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;
import com.medical.homesampling.repository.UserRepository;
import com.medical.homesampling.repository.AppointmentRepository;
import com.medical.homesampling.service.AppointmentService;
import com.medical.homesampling.entity.User;
import com.medical.homesampling.entity.Role;
import com.medical.homesampling.entity.Appointment;
import com.medical.homesampling.entity.AppointmentStatus;
import org.springframework.beans.factory.annotation.Autowired;

@RestController
@RequestMapping("/tracking")
@CrossOrigin(origins = "*")
public class TrackingController {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private AppointmentService appointmentService;

    // Stockage en mémoire des positions des infirmiers EN TEMPS RÉEL
    private final Map<Long, NursePosition> nursePositions = new ConcurrentHashMap<>();

    // Stockage des dernières positions reçues des infirmiers EN TEMPS RÉEL
    private final Map<Long, NursePosition> realTimePositions = new ConcurrentHashMap<>();
    
    @PostMapping("/update-position")
    public ResponseEntity<?> updateNursePosition(@RequestBody NursePositionRequest request) {
        System.out.println("📍 TEMPS RÉEL - Position infirmier " + request.getNurseId() + " - RDV: " + request.getAppointmentId());
        System.out.println("📍 Coordonnées: " + request.getLatitude() + ", " + request.getLongitude());
        System.out.println("📍 Vitesse: " + request.getSpeed() + " km/h, Précision: " + request.getAccuracy() + "m");

        try {
            // Créer la position temps réel
            NursePosition position = new NursePosition();
            position.setNurseId(request.getNurseId());
            position.setAppointmentId(request.getAppointmentId());
            position.setLatitude(request.getLatitude());
            position.setLongitude(request.getLongitude());
            position.setTimestamp(LocalDateTime.now());
            position.setAccuracy(request.getAccuracy());
            position.setHeading(request.getHeading());
            position.setSpeed(request.getSpeed());
            position.setStatus(request.getStatus() != null ? request.getStatus() : "ON_WAY");

            // Stocker la position temps réel (clé = nurseId pour éviter les doublons)
            realTimePositions.put(request.getNurseId(), position);

            // Aussi stocker dans l'ancien système pour compatibilité
            nursePositions.put(request.getAppointmentId(), position);

            System.out.println("✅ Position temps réel stockée pour infirmier " + request.getNurseId());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Position temps réel mise à jour");
            response.put("timestamp", LocalDateTime.now());
            response.put("nurseId", request.getNurseId());
            response.put("realTimePositionsCount", realTimePositions.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("❌ Erreur mise à jour position: " + e.getMessage());
            e.printStackTrace();

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Erreur mise à jour position: " + e.getMessage());

            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    @GetMapping("/nurse-position/{appointmentId}")
    public ResponseEntity<?> getNursePosition(@PathVariable Long appointmentId) {
        System.out.println("📍 Récupération position infirmier - RDV: " + appointmentId);
        
        NursePosition position = nursePositions.get(appointmentId);
        
        if (position == null) {
            // Simuler une position pour la démo
            position = createDemoPosition(appointmentId);
        }
        
        return ResponseEntity.ok(position);
    }
    
    @PostMapping("/arrived")
    public ResponseEntity<?> markAsArrived(@RequestBody Map<String, Long> request) {
        Long appointmentId = request.get("appointmentId");
        System.out.println("🏁 Infirmier marqué comme arrivé - RDV: " + appointmentId);
        
        NursePosition position = nursePositions.get(appointmentId);
        if (position != null) {
            position.setStatus("ARRIVED");
            position.setTimestamp(LocalDateTime.now());
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Infirmier marqué comme arrivé");
        
        return ResponseEntity.ok(response);
    }
    
    // Créer une position de démo qui bouge
    private NursePosition createDemoPosition(Long appointmentId) {
        NursePosition position = new NursePosition();
        position.setNurseId(1L);
        position.setAppointmentId(appointmentId);
        position.setTimestamp(LocalDateTime.now());
        position.setAccuracy(15.0);
        position.setStatus("ON_WAY");

        // Position qui bouge autour de Tunis (plus loin du patient pour voir le trajet)
        double baseLatitude = 36.8200;  // Un peu plus au nord
        double baseLongitude = 10.1650; // Un peu plus à l'ouest

        // Ajouter un mouvement basé sur le temps pour simuler un déplacement
        long timeOffset = System.currentTimeMillis() / 15000; // Change toutes les 15 secondes
        double latOffset = Math.sin(timeOffset * 0.05) * 0.008; // Mouvement plus visible
        double lngOffset = Math.cos(timeOffset * 0.05) * 0.012; // Mouvement plus visible

        position.setLatitude(baseLatitude + latOffset);
        position.setLongitude(baseLongitude + lngOffset);

        // Simuler une vitesse réaliste
        position.setSpeed(Math.random() * 15 + 20); // 20-35 km/h

        // Simuler un heading (direction)
        position.setHeading((double)((timeOffset * 10) % 360));

        System.out.println("🎭 Position démo générée: " + position.getLatitude() + ", " + position.getLongitude());
        System.out.println("🚗 Vitesse: " + position.getSpeed() + " km/h, Direction: " + position.getHeading() + "°");

        return position;
    }
    
    // Classes internes pour les données
    public static class NursePositionRequest {
        private Long nurseId;
        private Long appointmentId;
        private Double latitude;
        private Double longitude;
        private Double accuracy;
        private Double heading;
        private Double speed;
        private String status;
        
        // Getters et setters
        public Long getNurseId() { return nurseId; }
        public void setNurseId(Long nurseId) { this.nurseId = nurseId; }
        
        public Long getAppointmentId() { return appointmentId; }
        public void setAppointmentId(Long appointmentId) { this.appointmentId = appointmentId; }
        
        public Double getLatitude() { return latitude; }
        public void setLatitude(Double latitude) { this.latitude = latitude; }
        
        public Double getLongitude() { return longitude; }
        public void setLongitude(Double longitude) { this.longitude = longitude; }
        
        public Double getAccuracy() { return accuracy; }
        public void setAccuracy(Double accuracy) { this.accuracy = accuracy; }
        
        public Double getHeading() { return heading; }
        public void setHeading(Double heading) { this.heading = heading; }
        
        public Double getSpeed() { return speed; }
        public void setSpeed(Double speed) { this.speed = speed; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
    
    public static class NursePosition {
        private Long nurseId;
        private Long appointmentId;
        private Double latitude;
        private Double longitude;
        private LocalDateTime timestamp;
        private Double accuracy;
        private Double heading;
        private Double speed;
        private String status;
        
        // Getters et setters
        public Long getNurseId() { return nurseId; }
        public void setNurseId(Long nurseId) { this.nurseId = nurseId; }
        
        public Long getAppointmentId() { return appointmentId; }
        public void setAppointmentId(Long appointmentId) { this.appointmentId = appointmentId; }
        
        public Double getLatitude() { return latitude; }
        public void setLatitude(Double latitude) { this.latitude = latitude; }
        
        public Double getLongitude() { return longitude; }
        public void setLongitude(Double longitude) { this.longitude = longitude; }
        
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        
        public Double getAccuracy() { return accuracy; }
        public void setAccuracy(Double accuracy) { this.accuracy = accuracy; }
        
        public Double getHeading() { return heading; }
        public void setHeading(Double heading) { this.heading = heading; }
        
        public Double getSpeed() { return speed; }
        public void setSpeed(Double speed) { this.speed = speed; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }

    // Endpoint pour le géocodage inverse (contourner CORS)
    @GetMapping("/geocode/reverse")
    public ResponseEntity<?> reverseGeocode(
            @RequestParam("lat") double latitude,
            @RequestParam("lon") double longitude) {

        System.out.println("🌍 Géocodage inverse demandé: " + latitude + ", " + longitude);

        try {
            RestTemplate restTemplate = new RestTemplate();

            // Headers pour Nominatim
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "MedicalHomeSampling/1.0");
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // URL Nominatim (utiliser Locale.US pour éviter les virgules)
            String url = String.format(java.util.Locale.US,
                "https://nominatim.openstreetmap.org/reverse?format=json&lat=%f&lon=%f&zoom=18&addressdetails=1",
                latitude, longitude
            );

            System.out.println("🌐 URL Nominatim: " + url);

            // Faire la requête
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, String.class
            );

            System.out.println("✅ Réponse Nominatim reçue");
            return ResponseEntity.ok(response.getBody());

        } catch (Exception e) {
            System.err.println("❌ Erreur géocodage: " + e.getMessage());

            // Fallback : adresse approximative
            Map<String, Object> fallback = new HashMap<>();
            fallback.put("display_name", "Position approximative, Tunis, Tunisie");
            fallback.put("lat", String.valueOf(latitude));
            fallback.put("lon", String.valueOf(longitude));

            return ResponseEntity.ok(fallback);
        }
    }

    // Endpoint pour le géocodage direct (adresse → coordonnées)
    @GetMapping("/geocode/search")
    public ResponseEntity<?> searchGeocode(@RequestParam("q") String address) {

        System.out.println("🔍 Géocodage direct demandé: " + address);

        try {
            RestTemplate restTemplate = new RestTemplate();

            // Headers pour Nominatim
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "MedicalHomeSampling/1.0");
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // URL Nominatim
            String url = String.format(java.util.Locale.US,
                "https://nominatim.openstreetmap.org/search?format=json&q=%s&limit=1&addressdetails=1",
                java.net.URLEncoder.encode(address, "UTF-8")
            );

            System.out.println("🌐 URL Nominatim: " + url);

            // Faire la requête
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, String.class
            );

            System.out.println("✅ Réponse Nominatim reçue");
            return ResponseEntity.ok(response.getBody());

        } catch (Exception e) {
            System.err.println("❌ Erreur géocodage: " + e.getMessage());

            // Fallback : position par défaut
            Map<String, Object> fallback = new HashMap<>();
            fallback.put("lat", "36.8065");
            fallback.put("lon", "10.1815");
            fallback.put("display_name", address + ", Tunis, Tunisie");

            return ResponseEntity.ok(new Object[]{fallback});
        }
    }

    // Endpoint pour récupérer toutes les positions des infirmiers (pour l'admin)
    @GetMapping("/all-nurses-positions")
    public ResponseEntity<List<Map<String, Object>>> getAllNursesPositions() {
        System.out.println("🗺️ ADMIN - Demande positions TEMPS RÉEL UNIQUEMENT (" + realTimePositions.size() + " positions actives)");

        List<Map<String, Object>> allPositions = new ArrayList<>();

        try {
            // UNIQUEMENT les positions temps réel - PAS de fallback base de données
            if (!realTimePositions.isEmpty()) {
                System.out.println("📍 Retour des positions TEMPS RÉEL uniquement");

                for (NursePosition realTimePos : realTimePositions.values()) {
                    try {
                        com.medical.homesampling.entity.User nurse = userRepository.findById(realTimePos.getNurseId()).orElse(null);
                        com.medical.homesampling.entity.Appointment appointment = appointmentRepository.findById(realTimePos.getAppointmentId()).orElse(null);

                        if (nurse != null && appointment != null) {
                            Map<String, Object> position = createRealTimePosition(realTimePos, nurse, appointment);
                            allPositions.add(position);
                            System.out.println("✅ Position temps réel: " + nurse.getFirstName() + " à " + realTimePos.getLatitude() + ", " + realTimePos.getLongitude());
                        } else {
                            System.out.println("⚠️ Infirmier ou RDV non trouvé pour position temps réel: nurseId=" + realTimePos.getNurseId() + ", appointmentId=" + realTimePos.getAppointmentId());
                        }
                    } catch (Exception e) {
                        System.err.println("⚠️ Erreur traitement position temps réel: " + e.getMessage());
                    }
                }
            }

            // Récupérer aussi les positions depuis la base de données (rendez-vous avec partage activé)
            try {
                List<Appointment> activeAppointments = appointmentService.getActiveAppointmentsWithLocation();
                System.out.println("📍 Récupération des positions depuis la BASE DE DONNÉES:");
                System.out.println("   - Rendez-vous actifs avec position: " + activeAppointments.size());

                for (Appointment appointment : activeAppointments) {
                    // Vérifier si on n'a pas déjà cette position en temps réel
                    boolean hasRealTimePosition = realTimePositions.values().stream()
                        .anyMatch(p -> p.getAppointmentId().equals(appointment.getId()));

                    if (!hasRealTimePosition && appointment.getNurseCurrentLatitude() != null &&
                        appointment.getNurseCurrentLongitude() != null && appointment.getNurse() != null) {

                        try {
                            Map<String, Object> dbPosition = new HashMap<>();
                            dbPosition.put("nurseId", appointment.getNurse().getId());
                            dbPosition.put("appointmentId", appointment.getId());
                            dbPosition.put("latitude", appointment.getNurseCurrentLatitude());
                            dbPosition.put("longitude", appointment.getNurseCurrentLongitude());
                            dbPosition.put("timestamp", appointment.getLocationLastUpdated() != null ?
                                          appointment.getLocationLastUpdated().toString() : LocalDateTime.now().toString());
                            dbPosition.put("status", appointment.getStatus().toString());
                            dbPosition.put("accuracy", 10.0);
                            dbPosition.put("speed", 0.0);
                            dbPosition.put("heading", 0.0);

                            // Informations infirmier
                            dbPosition.put("nurseName", appointment.getNurse().getFirstName() + " " + appointment.getNurse().getLastName());
                            dbPosition.put("nursePhone", appointment.getNurse().getPhone());

                            // Informations patient
                            dbPosition.put("patientName", appointment.getPatient().getFirstName() + " " + appointment.getPatient().getLastName());
                            dbPosition.put("patientAddress", appointment.getHomeAddress());
                            dbPosition.put("patientLatitude", appointment.getLatitude());
                            dbPosition.put("patientLongitude", appointment.getLongitude());

                            allPositions.add(dbPosition);
                            System.out.println("   - Infirmier " + appointment.getNurse().getFirstName() +
                                             " (RDV " + appointment.getId() + "): " +
                                             appointment.getNurseCurrentLatitude() + ", " + appointment.getNurseCurrentLongitude() +
                                             " [DB - " + appointment.getStatus() + "]");
                        } catch (Exception e) {
                            System.err.println("⚠️ Erreur traitement position DB pour RDV " + appointment.getId() + ": " + e.getMessage());
                        }
                    }
                }
            } catch (Exception e) {
                System.err.println("⚠️ Erreur récupération positions DB: " + e.getMessage());
            }

            if (allPositions.isEmpty()) {
                System.out.println("📍 AUCUNE position disponible - Retour liste vide");
                System.out.println("💡 Pour voir des positions, un infirmier doit démarrer le suivi GPS ou avoir un partage de position actif");
            }

            System.out.println("✅ " + allPositions.size() + " positions TEMPS RÉEL envoyées (total stockées: " + realTimePositions.size() + ")");
            return ResponseEntity.ok(allPositions);

        } catch (Exception e) {
            System.err.println("❌ Erreur récupération positions: " + e.getMessage());
            e.printStackTrace();

            // Fallback avec positions simulées si erreur
            return getFallbackNursePositions();
        }
    }

    // Endpoint pour que le patient récupère la position de son infirmier
    @GetMapping("/appointment/{appointmentId}/nurse-position")
    public ResponseEntity<Map<String, Object>> getNursePositionForAppointment(@PathVariable Long appointmentId) {
        System.out.println("📍 PATIENT - Récupération position infirmier pour RDV: " + appointmentId);

        try {
            // Chercher la position temps réel pour ce rendez-vous
            NursePosition position = null;
            for (NursePosition pos : realTimePositions.values()) {
                if (pos.getAppointmentId().equals(appointmentId)) {
                    position = pos;
                    break;
                }
            }

            if (position == null) {
                System.out.println("📍 PATIENT - Aucune position temps réel trouvée pour RDV: " + appointmentId);
                return ResponseEntity.notFound().build();
            }

            // Récupérer les informations de l'infirmier et du patient
            com.medical.homesampling.entity.User nurse = userRepository.findById(position.getNurseId()).orElse(null);
            com.medical.homesampling.entity.Appointment appointment = appointmentRepository.findById(appointmentId).orElse(null);

            if (nurse == null || appointment == null) {
                System.out.println("📍 PATIENT - Infirmier ou RDV non trouvé");
                return ResponseEntity.notFound().build();
            }

            Map<String, Object> positionData = new HashMap<>();
            positionData.put("nurseId", position.getNurseId());
            positionData.put("appointmentId", position.getAppointmentId());
            positionData.put("nurseName", nurse.getFirstName() + " " + nurse.getLastName());
            positionData.put("patientName", appointment.getPatient().getFirstName() + " " + appointment.getPatient().getLastName());
            positionData.put("latitude", position.getLatitude());
            positionData.put("longitude", position.getLongitude());
            positionData.put("accuracy", position.getAccuracy());
            positionData.put("speed", position.getSpeed());
            positionData.put("heading", position.getHeading());
            positionData.put("status", position.getStatus());
            positionData.put("timestamp", position.getTimestamp());

            System.out.println("📍 PATIENT - Position trouvée: " + nurse.getFirstName() + " à " + position.getLatitude() + ", " + position.getLongitude());
            return ResponseEntity.ok(positionData);

        } catch (Exception e) {
            System.err.println("❌ Erreur récupération position pour patient: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    // Méthode pour créer une position d'infirmier réelle
    private java.util.Map<String, Object> createRealNursePosition(
            com.medical.homesampling.entity.User nurse,
            com.medical.homesampling.entity.Appointment appointment,
            String status) {

        java.util.Map<String, Object> position = new HashMap<>();
        position.put("nurseId", nurse.getId());
        position.put("nurseName", nurse.getFirstName() + " " + nurse.getLastName());
        position.put("appointmentId", appointment.getId());
        position.put("patientName", appointment.getPatient().getFirstName() + " " + appointment.getPatient().getLastName());
        position.put("latitude", nurse.getLatitude());
        position.put("longitude", nurse.getLongitude());
        position.put("timestamp", LocalDateTime.now().toString());
        position.put("accuracy", 10.0); // Précision par défaut
        position.put("speed", "ON_WAY".equals(status) ? 25.0 : 0.0);
        position.put("heading", 0.0);
        position.put("status", status);

        // Calculer ETA si en route
        if ("ON_WAY".equals(status) && appointment.getPatient().getLatitude() != null && appointment.getPatient().getLongitude() != null) {
            double distance = calculateDistance(
                nurse.getLatitude(), nurse.getLongitude(),
                appointment.getPatient().getLatitude(), appointment.getPatient().getLongitude()
            );
            int etaMinutes = (int) Math.max(5, distance * 2); // 2 minutes par km minimum 5 min
            position.put("estimatedArrival", etaMinutes + " min");
        }

        return position;
    }

    // Méthode pour créer une position depuis les données temps réel
    private Map<String, Object> createRealTimePosition(
            NursePosition realTimePos,
            com.medical.homesampling.entity.User nurse,
            com.medical.homesampling.entity.Appointment appointment) {

        Map<String, Object> position = new HashMap<>();
        position.put("nurseId", nurse.getId());
        position.put("nurseName", nurse.getFirstName() + " " + nurse.getLastName());
        position.put("appointmentId", appointment.getId());
        position.put("patientName", appointment.getPatient().getFirstName() + " " + appointment.getPatient().getLastName());

        // Utiliser les coordonnées TEMPS RÉEL
        position.put("latitude", realTimePos.getLatitude());
        position.put("longitude", realTimePos.getLongitude());
        position.put("timestamp", realTimePos.getTimestamp().toString());
        position.put("accuracy", realTimePos.getAccuracy());
        position.put("speed", realTimePos.getSpeed());
        position.put("heading", realTimePos.getHeading());
        position.put("status", realTimePos.getStatus());

        // Calculer ETA si en route
        if ("ON_WAY".equals(realTimePos.getStatus()) && appointment.getPatient().getLatitude() != null && appointment.getPatient().getLongitude() != null) {
            double distance = calculateDistance(
                realTimePos.getLatitude(), realTimePos.getLongitude(),
                appointment.getPatient().getLatitude(), appointment.getPatient().getLongitude()
            );
            int etaMinutes = (int) Math.max(5, distance * 2); // 2 minutes par km minimum 5 min
            position.put("estimatedArrival", etaMinutes + " min");
        }

        System.out.println("📍 Position temps réel créée: " + nurse.getFirstName() + " à " + realTimePos.getLatitude() + ", " + realTimePos.getLongitude());
        return position;
    }

    // Méthode fallback avec positions simulées
    private ResponseEntity<java.util.List<java.util.Map<String, Object>>> getFallbackNursePositions() {
        System.out.println("🔄 Utilisation des positions simulées...");
        java.util.List<java.util.Map<String, Object>> allPositions = new java.util.ArrayList<>();

        long currentTime = System.currentTimeMillis();

        // Infirmier 1 - En route vers Tunis
        Map<String, Object> nurse1 = createNursePosition(
            1L, "Dr. Amira Ben Ali", 101L, "Mohamed Trabelsi",
            36.8065 + (Math.sin(currentTime / 10000.0) * 0.01), // Position qui bouge
            10.1815 + (Math.cos(currentTime / 10000.0) * 0.01),
            "ON_WAY", 15.0, 25.0
        );
        allPositions.add(nurse1);

        // Infirmier 2 - En route vers Sfax
        Map<String, Object> nurse2 = createNursePosition(
            2L, "Dr. Karim Mansouri", 102L, "Fatma Gharbi",
            34.7406 + (Math.sin(currentTime / 8000.0) * 0.008),
            10.7603 + (Math.cos(currentTime / 8000.0) * 0.008),
            "ON_WAY", 20.0, 30.0
        );
        allPositions.add(nurse2);

        // Infirmier 3 - Arrivé à Sousse
        Map<String, Object> nurse3 = createNursePosition(
            3L, "Dr. Leila Bouaziz", 103L, "Ahmed Khelifi",
            35.8256, 10.6369,
            "ARRIVED", 5.0, 0.0
        );
        allPositions.add(nurse3);

        // Infirmier 4 - En retard vers Monastir
        Map<String, Object> nurse4 = createNursePosition(
            4L, "Dr. Sami Jebali", 104L, "Nadia Hamdi",
            35.7643 + (Math.sin(currentTime / 12000.0) * 0.005),
            10.8113 + (Math.cos(currentTime / 12000.0) * 0.005),
            "DELAYED", 25.0, 15.0
        );
        allPositions.add(nurse4);

        System.out.println("✅ " + allPositions.size() + " positions infirmiers envoyées");
        return ResponseEntity.ok(allPositions);
    }

    // Méthode utilitaire pour créer une position d'infirmier
    private Map<String, Object> createNursePosition(Long nurseId, String nurseName,
            Long appointmentId, String patientName, double lat, double lng,
            String status, double accuracy, double speed) {

        Map<String, Object> position = new HashMap<>();
        position.put("nurseId", nurseId);
        position.put("nurseName", nurseName);
        position.put("appointmentId", appointmentId);
        position.put("patientName", patientName);
        position.put("latitude", lat);
        position.put("longitude", lng);
        position.put("timestamp", LocalDateTime.now().toString());
        position.put("accuracy", accuracy);
        position.put("speed", speed);
        position.put("heading", (System.currentTimeMillis() / 1000) % 360);
        position.put("status", status);

        // Calculer ETA approximatif
        if ("ON_WAY".equals(status)) {
            int etaMinutes = (int) (Math.random() * 30 + 10); // 10-40 minutes
            position.put("estimatedArrival", etaMinutes + " min");
        }

        return position;
    }

    // Méthode pour calculer la distance entre deux points (en km)
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // Rayon de la Terre en km

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);

        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c; // Distance en km
    }
}
