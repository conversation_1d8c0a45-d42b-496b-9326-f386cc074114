package com.medical.homesampling.repository;

import com.medical.homesampling.entity.Appointment;
import com.medical.homesampling.entity.TestResult;
import com.medical.homesampling.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TestResultRepository extends JpaRepository<TestResult, Long> {
    
    List<TestResult> findByAppointment(Appointment appointment);
    
    @Query("SELECT tr FROM TestResult tr WHERE tr.appointment.patient = :patient ORDER BY tr.createdAt DESC")
    List<TestResult> findByPatient(@Param("patient") User patient);
    
    @Query("SELECT tr FROM TestResult tr WHERE tr.appointment.patient = :patient AND tr.patientNotified = false")
    List<TestResult> findUnnotifiedResultsByPatient(@Param("patient") User patient);
    
    @Query("SELECT tr FROM TestResult tr WHERE tr.isCritical = true AND tr.patientNotified = false")
    List<TestResult> findCriticalUnnotifiedResults();
}
