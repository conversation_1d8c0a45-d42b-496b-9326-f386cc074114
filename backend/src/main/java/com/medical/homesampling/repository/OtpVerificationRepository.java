package com.medical.homesampling.repository;

import com.medical.homesampling.entity.OtpVerification;
import com.medical.homesampling.entity.OtpType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface OtpVerificationRepository extends JpaRepository<OtpVerification, Long> {
    
    Optional<OtpVerification> findByEmailAndOtpCodeAndTypeAndIsUsedFalse(
        String email, String otpCode, OtpType type);
    
    List<OtpVerification> findByEmailAndTypeAndIsUsedFalse(String email, OtpType type);
    
    @Modifying
    @Query("UPDATE OtpVerification o SET o.isUsed = true WHERE o.email = :email AND o.type = :type")
    void markAllAsUsedByEmailAndType(@Param("email") String email, @Param("type") OtpType type);
    
    @Modifying
    @Query("DELETE FROM OtpVerification o WHERE o.expiresAt < :now")
    void deleteExpiredOtps(@Param("now") LocalDateTime now);
    
    @Query("SELECT COUNT(o) FROM OtpVerification o WHERE o.email = :email AND o.type = :type AND o.createdAt > :since")
    long countByEmailAndTypeAndCreatedAtAfter(
        @Param("email") String email, 
        @Param("type") OtpType type, 
        @Param("since") LocalDateTime since);
}
