package com.medical.homesampling.repository;

import com.medical.homesampling.entity.Role;
import com.medical.homesampling.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    Optional<User> findByUsername(String username);
    
    Optional<User> findByEmail(String email);
    
    boolean existsByUsername(String username);
    
    boolean existsByEmail(String email);
    
    List<User> findByRole(Role role);
    
    List<User> findByRoleAndEnabledTrue(Role role);
    
    @Query("SELECT u FROM User u WHERE u.role = :role AND u.isAvailable = true AND u.enabled = true")
    List<User> findAvailableNurses(@Param("role") Role role);
    
    @Query("SELECT u FROM User u WHERE u.role = 'NURSE' AND u.isAvailable = true AND u.enabled = true " +
           "AND u.latitude IS NOT NULL AND u.longitude IS NOT NULL " +
           "ORDER BY (6371 * acos(cos(radians(:lat)) * cos(radians(u.latitude)) * " +
           "cos(radians(u.longitude) - radians(:lng)) + sin(radians(:lat)) * sin(radians(u.latitude)))) ASC")
    List<User> findNearestAvailableNurses(@Param("lat") Double latitude, @Param("lng") Double longitude);

    /**
     * Trouve tous les infirmiers actifs (pour l'admin)
     */
    @Query("SELECT u FROM User u WHERE u.role = 'NURSE' AND u.enabled = true ORDER BY u.firstName, u.lastName")
    List<User> findAllActiveNurses();

    /**
     * Trouve les utilisateurs par région/ville
     */
    @Query("SELECT u FROM User u WHERE u.address LIKE %:region% AND u.role = :role")
    List<User> findByRegionAndRole(@Param("region") String region, @Param("role") Role role);
}
