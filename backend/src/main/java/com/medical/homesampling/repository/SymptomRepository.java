package com.medical.homesampling.repository;

import com.medical.homesampling.entity.Symptom;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SymptomRepository extends JpaRepository<Symptom, Long> {
    
    List<Symptom> findByIsActiveTrueOrderByNameAsc();
    
    @Query("SELECT s FROM Symptom s WHERE s.isActive = true AND LOWER(s.name) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<Symptom> findByKeyword(@Param("keyword") String keyword);
    
    @Query("SELECT s FROM Symptom s WHERE s.isActive = true AND s.severityLevel >= :minSeverity ORDER BY s.severityLevel DESC")
    List<Symptom> findBySeverityLevel(@Param("minSeverity") Integer minSeverity);
}
