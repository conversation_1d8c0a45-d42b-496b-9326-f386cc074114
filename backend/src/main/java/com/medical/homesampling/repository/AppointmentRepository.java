package com.medical.homesampling.repository;

import com.medical.homesampling.entity.Appointment;
import com.medical.homesampling.entity.AppointmentStatus;
import com.medical.homesampling.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, Long> {
    
    List<Appointment> findByPatient(User patient);
    
    List<Appointment> findByNurse(User nurse);
    
    List<Appointment> findByStatus(AppointmentStatus status);
    
    List<Appointment> findByPatientAndStatus(User patient, AppointmentStatus status);
    
    List<Appointment> findByNurseAndStatus(User nurse, AppointmentStatus status);
    
    @Query("SELECT a FROM Appointment a LEFT JOIN FETCH a.analysisTypes WHERE a.nurse = :nurse AND a.scheduledDate BETWEEN :startDate AND :endDate")
    List<Appointment> findByNurseAndDateRange(@Param("nurse") User nurse,
                                            @Param("startDate") LocalDateTime startDate,
                                            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT a FROM Appointment a LEFT JOIN FETCH a.analysisTypes WHERE a.patient = :patient ORDER BY a.scheduledDate DESC")
    List<Appointment> findByPatientOrderByScheduledDateDesc(@Param("patient") User patient);
    
    @Query("SELECT a FROM Appointment a WHERE a.nurse = :nurse AND a.scheduledDate >= :today ORDER BY a.scheduledDate ASC")
    List<Appointment> findUpcomingAppointmentsByNurse(@Param("nurse") User nurse, @Param("today") LocalDateTime today);
    
    @Query("SELECT a FROM Appointment a WHERE a.isUrgent = true AND a.status IN ('PENDING', 'CONFIRMED') ORDER BY a.createdAt ASC")
    List<Appointment> findUrgentPendingAppointments();
    
    @Query("SELECT COUNT(a) FROM Appointment a WHERE a.status = :status")
    Long countByStatus(@Param("status") AppointmentStatus status);
    
    @Query("SELECT COUNT(a) FROM Appointment a WHERE a.createdAt >= :startDate AND a.createdAt <= :endDate")
    Long countAppointmentsByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    // ========== MÉTHODES ADMIN ==========

    /**
     * Trouve tous les rendez-vous triés par date de création (plus récents en premier)
     */
    List<Appointment> findAllByOrderByCreatedAtDesc();

    /**
     * Trouve les rendez-vous en attente sans infirmier affecté
     */
    List<Appointment> findByStatusAndNurseIsNullOrderByCreatedAtAsc(AppointmentStatus status);

    /**
     * Compte les rendez-vous actifs d'un infirmier
     */
    Long countByNurseAndStatusIn(User nurse, List<AppointmentStatus> statuses);

    /**
     * Trouve les rendez-vous par infirmier et statuts
     */
    List<Appointment> findByNurseAndStatusIn(User nurse, List<AppointmentStatus> statuses);

    /**
     * Trouve les rendez-vous non affectés (sans infirmier)
     */
    @Query("SELECT a FROM Appointment a WHERE a.nurse IS NULL AND a.status = 'PENDING' ORDER BY a.isUrgent DESC, a.createdAt ASC")
    List<Appointment> findUnassignedAppointments();

    /**
     * Trouve les rendez-vous par zone géographique (approximative)
     */
    @Query("SELECT a FROM Appointment a WHERE a.homeAddress LIKE %:region% ORDER BY a.createdAt ASC")
    List<Appointment> findByRegion(@Param("region") String region);

    /**
     * Trouve tous les rendez-vous avec leurs patients et infirmiers pour la carte admin
     */
    @Query("SELECT DISTINCT a FROM Appointment a " +
           "LEFT JOIN FETCH a.patient p " +
           "LEFT JOIN FETCH a.nurse n " +
           "WHERE a.latitude IS NOT NULL AND a.longitude IS NOT NULL " +
           "ORDER BY a.scheduledDate DESC")
    List<Appointment> findAllWithPatientsAndNurses();
}
