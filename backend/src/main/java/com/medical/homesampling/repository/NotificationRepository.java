package com.medical.homesampling.repository;

import com.medical.homesampling.entity.Notification;
import com.medical.homesampling.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long> {

    @Query("SELECT n FROM Notification n JOIN FETCH n.user WHERE n.user = :user ORDER BY n.createdAt DESC")
    List<Notification> findByUserOrderByCreatedAtDesc(@Param("user") User user);

    @Query("SELECT n FROM Notification n JOIN FETCH n.user WHERE n.user = :user AND n.isRead = false ORDER BY n.createdAt DESC")
    List<Notification> findByUserAndIsReadFalseOrderByCreatedAtDesc(@Param("user") User user);

    @Query("SELECT COUNT(n) FROM Notification n WHERE n.user = :user AND n.isRead = false")
    Long countUnreadByUser(@Param("user") User user);

    List<Notification> findByIsUrgentTrueAndIsReadFalse();
}
