package com.medical.homesampling.repository;

import com.medical.homesampling.entity.AnalysisType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AnalysisTypeRepository extends JpaRepository<AnalysisType, Long> {
    
    List<AnalysisType> findByIsActiveTrue();

    List<AnalysisType> findByNameContainingIgnoreCase(String name);

    List<AnalysisType> findByIsActiveTrueOrderByNameAsc();

    boolean existsByNameIgnoreCase(String name);
}
