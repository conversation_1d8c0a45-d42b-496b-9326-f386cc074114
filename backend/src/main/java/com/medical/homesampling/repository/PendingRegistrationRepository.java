package com.medical.homesampling.repository;

import com.medical.homesampling.entity.PendingRegistration;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Repository
public interface PendingRegistrationRepository extends JpaRepository<PendingRegistration, Long> {
    
    Optional<PendingRegistration> findByEmail(String email);
    
    Optional<PendingRegistration> findByUsername(String username);
    
    boolean existsByEmail(String email);
    
    boolean existsByUsername(String username);
    
    @Modifying
    @Transactional
    @Query("DELETE FROM PendingRegistration p WHERE p.expiresAt < :now")
    void deleteExpiredRegistrations(@Param("now") LocalDateTime now);
    
    @Modifying
    @Transactional
    void deleteByEmail(String email);
}
