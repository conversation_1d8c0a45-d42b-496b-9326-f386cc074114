package com.medical.homesampling.repository;

import com.medical.homesampling.entity.LocationSharing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface LocationSharingRepository extends JpaRepository<LocationSharing, Long> {
    
    // Trouver le partage de localisation actif pour un rendez-vous
    @Query("SELECT ls FROM LocationSharing ls WHERE ls.appointmentId = :appointmentId AND ls.isActive = true")
    Optional<LocationSharing> findActiveByAppointmentId(@Param("appointmentId") Long appointmentId);
    
    // Trouver tous les partages actifs pour une infirmière
    @Query("SELECT ls FROM LocationSharing ls WHERE ls.nurseId = :nurseId AND ls.isActive = true")
    List<LocationSharing> findActiveByNurseId(@Param("nurseId") Long nurseId);
    
    // Trouver le dernier partage pour un rendez-vous (actif ou non)
    @Query("SELECT ls FROM LocationSharing ls WHERE ls.appointmentId = :appointmentId ORDER BY ls.lastUpdated DESC")
    Optional<LocationSharing> findLatestByAppointmentId(@Param("appointmentId") Long appointmentId);
    
    // Trouver tous les partages pour une infirmière
    List<LocationSharing> findByNurseIdOrderByStartedAtDesc(Long nurseId);
    
    // Trouver tous les partages pour un rendez-vous
    List<LocationSharing> findByAppointmentIdOrderByStartedAtDesc(Long appointmentId);
    
    // Désactiver tous les partages actifs pour une infirmière
    @Query("UPDATE LocationSharing ls SET ls.isActive = false, ls.stoppedAt = :stoppedAt, ls.lastUpdated = :lastUpdated WHERE ls.nurseId = :nurseId AND ls.isActive = true")
    void deactivateAllByNurseId(@Param("nurseId") Long nurseId, @Param("stoppedAt") LocalDateTime stoppedAt, @Param("lastUpdated") LocalDateTime lastUpdated);
    
    // Désactiver le partage pour un rendez-vous spécifique
    @Query("UPDATE LocationSharing ls SET ls.isActive = false, ls.stoppedAt = :stoppedAt, ls.lastUpdated = :lastUpdated WHERE ls.appointmentId = :appointmentId AND ls.isActive = true")
    void deactivateByAppointmentId(@Param("appointmentId") Long appointmentId, @Param("stoppedAt") LocalDateTime stoppedAt, @Param("lastUpdated") LocalDateTime lastUpdated);
    
    // Trouver les partages expirés (plus de X minutes sans mise à jour)
    @Query("SELECT ls FROM LocationSharing ls WHERE ls.isActive = true AND ls.lastUpdated < :expiredBefore")
    List<LocationSharing> findExpiredActiveSessions(@Param("expiredBefore") LocalDateTime expiredBefore);
    
    // Compter les partages actifs pour une infirmière
    @Query("SELECT COUNT(ls) FROM LocationSharing ls WHERE ls.nurseId = :nurseId AND ls.isActive = true")
    long countActiveByNurseId(@Param("nurseId") Long nurseId);
    
    // Vérifier si un rendez-vous a un partage actif
    @Query("SELECT CASE WHEN COUNT(ls) > 0 THEN true ELSE false END FROM LocationSharing ls WHERE ls.appointmentId = :appointmentId AND ls.isActive = true")
    boolean hasActiveLocationSharing(@Param("appointmentId") Long appointmentId);
}
