package com.medical.homesampling.service;

import com.medical.homesampling.entity.Appointment;
import com.medical.homesampling.entity.AnalysisType;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.time.format.DateTimeFormatter;
import java.util.Random;

@Service
public class PdfService {

    /**
     * Génère un PDF avec les résultats d'analyses
     */
    public byte[] generateResultsPdf(Appointment appointment, String comments) {
        try {
            // Pour cette démonstration, nous générons un PDF simple en texte
            // Dans un vrai projet, utilisez une bibliothèque comme iText ou Apache PDFBox
            
            String pdfContent = generatePdfContent(appointment, comments);
            return createSimplePdf(pdfContent);
            
        } catch (Exception e) {
            System.err.println("Erreur lors de la génération du PDF: " + e.getMessage());
            throw new RuntimeException("Impossible de générer le PDF des résultats", e);
        }
    }

    /**
     * Gén<PERSON> le contenu du PDF
     */
    private String generatePdfContent(Appointment appointment, String comments) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy à HH:mm");
        StringBuilder content = new StringBuilder();

        content.append("RÉSULTATS D'ANALYSES MÉDICALES\n");
        content.append("=====================================\n\n");

        // Informations patient
        content.append("INFORMATIONS PATIENT\n");
        content.append("--------------------\n");
        content.append("Nom: ").append(appointment.getPatient().getLastName()).append("\n");
        content.append("Prénom: ").append(appointment.getPatient().getFirstName()).append("\n");
        content.append("Email: ").append(appointment.getPatient().getEmail()).append("\n");
        content.append("Date du prélèvement: ").append(appointment.getScheduledDate().format(formatter)).append("\n");
        content.append("Infirmier(e): ").append(appointment.getNurse() != null ? appointment.getNurse().getFullName() : "Non assigné").append("\n\n");

        // Analyses effectuées
        content.append("ANALYSES EFFECTUÉES\n");
        content.append("-------------------\n");
        if (appointment.getAnalysisTypes() != null && !appointment.getAnalysisTypes().isEmpty()) {
            for (AnalysisType analysisType : appointment.getAnalysisTypes()) {
                content.append("• ").append(analysisType.getName());
                if (analysisType.getDescription() != null) {
                    content.append(" - ").append(analysisType.getDescription());
                }
                content.append("\n");
            }
        } else {
            content.append("Aucune analyse spécifiée\n");
        }
        content.append("\n");

        // Résultats simulés
        content.append("RÉSULTATS\n");
        content.append("---------\n");
        content.append(generateMockResults(appointment));
        content.append("\n");

        // Commentaires
        content.append("COMMENTAIRES DE L'INFIRMIER(E)\n");
        content.append("------------------------------\n");
        content.append(comments != null ? comments : "Aucun commentaire particulier.");
        content.append("\n\n");

        // Informations légales
        content.append("INFORMATIONS IMPORTANTES\n");
        content.append("------------------------\n");
        content.append("Ces résultats sont à interpréter par votre médecin traitant.\n");
        content.append("En cas de questions, consultez un professionnel de santé.\n");
        content.append("Document généré automatiquement le ").append(java.time.LocalDateTime.now().format(formatter)).append("\n");

        return content.toString();
    }

    /**
     * Génère des résultats d'analyses simulés
     */
    private String generateMockResults(Appointment appointment) {
        StringBuilder results = new StringBuilder();
        Random random = new Random();

        if (appointment.getAnalysisTypes() != null) {
            for (AnalysisType analysisType : appointment.getAnalysisTypes()) {
                results.append("=== ").append(analysisType.getName().toUpperCase()).append(" ===\n");
                
                switch (analysisType.getName().toLowerCase()) {
                    case "analyse sanguine":
                    case "prise de sang":
                        results.append("Globules rouges: ").append(4.2 + random.nextDouble() * 0.8).append(" M/µL (Normal: 4.0-5.2)\n");
                        results.append("Globules blancs: ").append(5000 + random.nextInt(3000)).append(" /µL (Normal: 4000-10000)\n");
                        results.append("Plaquettes: ").append(200000 + random.nextInt(100000)).append(" /µL (Normal: 150000-400000)\n");
                        results.append("Hémoglobine: ").append(12.5 + random.nextDouble() * 3).append(" g/dL (Normal: 12-16)\n");
                        break;
                        
                    case "cholestérol":
                        results.append("Cholestérol total: ").append(180 + random.nextInt(60)).append(" mg/dL (Normal: <200)\n");
                        results.append("HDL: ").append(40 + random.nextInt(20)).append(" mg/dL (Normal: >40)\n");
                        results.append("LDL: ").append(100 + random.nextInt(40)).append(" mg/dL (Normal: <130)\n");
                        results.append("Triglycérides: ").append(80 + random.nextInt(70)).append(" mg/dL (Normal: <150)\n");
                        break;
                        
                    case "glycémie":
                        results.append("Glucose à jeun: ").append(85 + random.nextInt(25)).append(" mg/dL (Normal: 70-100)\n");
                        results.append("HbA1c: ").append(5.0 + random.nextDouble() * 1.5).append("% (Normal: <5.7)\n");
                        break;
                        
                    case "analyse d'urine":
                        results.append("Protéines: Négatives (Normal)\n");
                        results.append("Glucose: Négatif (Normal)\n");
                        results.append("Leucocytes: ").append(random.nextInt(5)).append(" /champ (Normal: <5)\n");
                        results.append("Hématies: ").append(random.nextInt(3)).append(" /champ (Normal: <3)\n");
                        break;
                        
                    default:
                        results.append("Résultats dans les valeurs normales\n");
                        results.append("Aucune anomalie détectée\n");
                        break;
                }
                
                results.append("Statut: ").append(random.nextBoolean() ? "NORMAL" : "NORMAL").append("\n\n");
            }
        }

        if (results.length() == 0) {
            results.append("Tous les paramètres analysés sont dans les valeurs de référence normales.\n");
            results.append("Aucune anomalie significative détectée.\n");
        }

        return results.toString();
    }

    /**
     * Crée un PDF simple (simulation)
     * Dans un vrai projet, utilisez iText ou Apache PDFBox
     */
    private byte[] createSimplePdf(String content) {
        try {
            // Simulation d'un PDF - en réalité, utilisez une vraie bibliothèque PDF
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            
            // En-tête PDF minimal
            String pdfHeader = "%PDF-1.4\n";
            String pdfContent = String.format("""
                1 0 obj
                <<
                /Type /Catalog
                /Pages 2 0 R
                >>
                endobj
                
                2 0 obj
                <<
                /Type /Pages
                /Kids [3 0 R]
                /Count 1
                >>
                endobj
                
                3 0 obj
                <<
                /Type /Page
                /Parent 2 0 R
                /MediaBox [0 0 612 792]
                /Contents 4 0 R
                /Resources <<
                  /Font <<
                    /F1 <<
                      /Type /Font
                      /Subtype /Type1
                      /BaseFont /Helvetica
                    >>
                  >>
                >>
                >>
                endobj
                
                4 0 obj
                <<
                /Length %d
                >>
                stream
                BT
                /F1 12 Tf
                50 750 Td
                (%s) Tj
                ET
                endstream
                endobj
                
                xref
                0 5
                0000000000 65535 f 
                0000000009 00000 n 
                0000000058 00000 n 
                0000000115 00000 n 
                0000000300 00000 n 
                trailer
                <<
                /Size 5
                /Root 1 0 R
                >>
                startxref
                %d
                %%%%EOF
                """, content.length(), content.replace("\n", "\\n"), 400 + content.length());
            
            baos.write(pdfHeader.getBytes());
            baos.write(pdfContent.getBytes());
            
            return baos.toByteArray();
            
        } catch (Exception e) {
            throw new RuntimeException("Erreur lors de la création du PDF", e);
        }
    }
}
