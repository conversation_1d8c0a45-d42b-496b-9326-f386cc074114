package com.medical.homesampling.service;

import com.medical.homesampling.entity.*;
import com.medical.homesampling.repository.NotificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class NotificationService {

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private UserService userService;

    public Notification createNotification(User user, String title, String message, 
                                         NotificationType type, boolean isUrgent) {
        Notification notification = new Notification();
        notification.setUser(user);
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setType(type);
        notification.setIsUrgent(isUrgent);

        Notification savedNotification = notificationRepository.save(notification);

        // Send real-time notification via WebSocket
        sendRealTimeNotification(user.getId(), savedNotification);

        return savedNotification;
    }

    public void notifyNurseAssigned(Appointment appointment) {
        // Notify patient
        Notification patientNotification = createNotification(
            appointment.getPatient(),
            "Infirmier assigné",
            "Un infirmier a été assigné à votre rendez-vous du " + appointment.getScheduledDate(),
            NotificationType.NURSE_ASSIGNED,
            false
        );
        patientNotification.setRelatedAppointmentId(appointment.getId());
        patientNotification.setActionUrl("/dashboard/appointments");
        notificationRepository.save(patientNotification);

        // Notify nurse
        if (appointment.getNurse() != null) {
            Notification nurseNotification = createNotification(
                appointment.getNurse(),
                "Nouveau rendez-vous assigné",
                "Vous avez été assigné à un nouveau rendez-vous le " + appointment.getScheduledDate(),
                NotificationType.NURSE_ASSIGNED,
                appointment.getIsUrgent()
            );
            nurseNotification.setRelatedAppointmentId(appointment.getId());
            nurseNotification.setActionUrl("/nurse-dashboard");
            notificationRepository.save(nurseNotification);
        }
    }

    public void notifyStatusChange(Appointment appointment, AppointmentStatus oldStatus,
                                 AppointmentStatus newStatus) {
        String title = "Mise à jour de votre rendez-vous";
        String message = getStatusChangeMessage(newStatus);
        NotificationType type = getNotificationTypeForStatus(newStatus);

        Notification notification = createNotification(appointment.getPatient(), title, message, type, false);
        notification.setRelatedAppointmentId(appointment.getId());
        notification.setActionUrl("/dashboard/appointments");
        notificationRepository.save(notification);
    }

    public void notifyUrgentRequest(Appointment appointment) {
        // Notify all available nurses about urgent request
        List<User> availableNurses = userService.getAvailableNurses();
        
        for (User nurse : availableNurses) {
            createNotification(
                nurse,
                "🚨 DEMANDE URGENTE",
                "Nouvelle demande urgente de prélèvement à " + appointment.getHomeAddress(),
                NotificationType.URGENT_REQUEST,
                true
            );
        }
    }

    public void notifyResultsAvailable(Appointment appointment) {
        createNotification(
            appointment.getPatient(),
            "Résultats disponibles",
            "Les résultats de vos analyses sont maintenant disponibles",
            NotificationType.RESULTS_AVAILABLE,
            false
        );
    }

    public void notifyAppointmentCancelled(Appointment appointment, String reason) {
        createNotification(
            appointment.getPatient(),
            "Rendez-vous annulé",
            "Votre rendez-vous du " + appointment.getScheduledDate() + " a été annulé. Raison: " + reason,
            NotificationType.SYSTEM_ALERT,
            false
        );

        if (appointment.getNurse() != null) {
            createNotification(
                appointment.getNurse(),
                "Rendez-vous annulé",
                "Le rendez-vous du " + appointment.getScheduledDate() + " a été annulé",
                NotificationType.SYSTEM_ALERT,
                false
            );
        }
    }

    public List<Notification> getUserNotifications(User user) {
        return notificationRepository.findByUserOrderByCreatedAtDesc(user);
    }

    public List<Notification> getUnreadNotifications(User user) {
        return notificationRepository.findByUserAndIsReadFalseOrderByCreatedAtDesc(user);
    }

    public Long getUnreadCount(User user) {
        return notificationRepository.countUnreadByUser(user);
    }

    public Notification markAsRead(Long notificationId) {
        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new RuntimeException("Notification not found"));
        
        notification.setIsRead(true);
        return notificationRepository.save(notification);
    }

    private void sendRealTimeNotification(Long userId, Notification notification) {
        try {
            messagingTemplate.convertAndSend("/topic/notifications/" + userId, notification);
        } catch (Exception e) {
            // Log error but don't fail the notification creation
            System.err.println("Failed to send real-time notification: " + e.getMessage());
        }
    }

    private String getStatusChangeMessage(AppointmentStatus status) {
        switch (status) {
            case CONFIRMED:
                return "Votre rendez-vous a été confirmé";
            case NURSE_ASSIGNED:
                return "Un infirmier a été assigné à votre rendez-vous";
            case NURSE_ON_WAY:
                return "L'infirmier est en route vers votre domicile";
            case IN_PROGRESS:
                return "Le prélèvement est en cours";
            case SAMPLING_DONE:
                return "Le prélèvement a été effectué avec succès";
            case ANALYSIS_IN_PROGRESS:
                return "Vos échantillons sont en cours d'analyse";
            case RESULTS_AVAILABLE:
                return "Vos résultats sont maintenant disponibles";
            case COMPLETED:
                return "Votre rendez-vous est terminé";
            default:
                return "Statut de votre rendez-vous mis à jour";
        }
    }

    private NotificationType getNotificationTypeForStatus(AppointmentStatus status) {
        switch (status) {
            case CONFIRMED:
                return NotificationType.APPOINTMENT_CONFIRMED;
            case NURSE_ASSIGNED:
                return NotificationType.NURSE_ASSIGNED;
            case NURSE_ON_WAY:
                return NotificationType.NURSE_ON_WAY;
            case IN_PROGRESS:
                return NotificationType.NURSE_ON_WAY;
            case SAMPLING_DONE:
                return NotificationType.SAMPLING_COMPLETED;
            case RESULTS_AVAILABLE:
                return NotificationType.RESULTS_AVAILABLE;
            default:
                return NotificationType.SYSTEM_ALERT;
        }
    }

    // ========== NOUVELLES MÉTHODES ADMIN ==========

    /**
     * Notifie l'affectation d'un infirmier à un rendez-vous
     */
    public void notifyAppointmentAssigned(Appointment appointment) {
        // Notifier le patient
        createNotification(
            appointment.getPatient(),
            "Infirmier affecté",
            String.format("L'infirmier %s %s a été affecté à votre rendez-vous du %s",
                appointment.getNurse().getFirstName(),
                appointment.getNurse().getLastName(),
                appointment.getScheduledDate().toLocalDate()),
            NotificationType.ASSIGNED,
            false
        );

        // Notifier l'infirmier
        createNotification(
            appointment.getNurse(),
            "Nouveau rendez-vous assigné",
            String.format("Vous avez été affecté au rendez-vous de %s %s le %s à %s",
                appointment.getPatient().getFirstName(),
                appointment.getPatient().getLastName(),
                appointment.getScheduledDate().toLocalDate(),
                appointment.getHomeAddress()),
            NotificationType.ASSIGNED,
            appointment.getIsUrgent()
        );
    }

    /**
     * Notifie le changement de statut d'un rendez-vous (Admin)
     */
    public void notifyAdminStatusChange(Appointment appointment, AppointmentStatus oldStatus, AppointmentStatus newStatus) {
        String message = String.format("Le statut de votre rendez-vous du %s est passé de %s à %s",
            appointment.getScheduledDate().toLocalDate(),
            getStatusLabel(oldStatus),
            getStatusLabel(newStatus));

        // Notifier le patient
        createNotification(
            appointment.getPatient(),
            "Statut de rendez-vous modifié",
            message,
            NotificationType.STATUS_CHANGED,
            false
        );

        // Notifier l'infirmier si assigné
        if (appointment.getNurse() != null) {
            createNotification(
                appointment.getNurse(),
                "Statut de rendez-vous modifié",
                String.format("Le statut du rendez-vous de %s %s est passé à %s",
                    appointment.getPatient().getFirstName(),
                    appointment.getPatient().getLastName(),
                    getStatusLabel(newStatus)),
                NotificationType.STATUS_CHANGED,
                false
            );
        }
    }

    /**
     * Convertit le statut en libellé lisible
     */
    private String getStatusLabel(AppointmentStatus status) {
        switch (status) {
            case PENDING: return "En attente";
            case CONFIRMED: return "Confirmé";
            case NURSE_ASSIGNED: return "Infirmier assigné";
            case NURSE_ON_WAY: return "Infirmier en route";
            case IN_PROGRESS: return "En cours";
            case SAMPLING_DONE: return "Prélèvement effectué";
            case ANALYSIS_IN_PROGRESS: return "Analyse en cours";
            case RESULTS_AVAILABLE: return "Résultats disponibles";
            case COMPLETED: return "Terminé";
            case CANCELLED: return "Annulé";
            default: return status.toString();
        }
    }

    /**
     * Notifie le patient que l'infirmier est en route
     */
    public void notifyPatientNurseOnWay(Appointment appointment) {
        if (appointment.getPatient() != null) {
            String nurseName = appointment.getNurse() != null ?
                appointment.getNurse().getFirstName() + " " + appointment.getNurse().getLastName() :
                "Votre infirmier";

            String message = String.format("%s est en route pour votre rendez-vous. ", nurseName);

            if (appointment.getLocationSharingEnabled() != null && appointment.getLocationSharingEnabled()) {
                message += "Vous pouvez suivre sa position en temps réel.";
            } else {
                message += "Il vous contactera si nécessaire.";
            }

            if (appointment.getEstimatedArrivalTime() != null) {
                message += String.format(" Arrivée estimée : %s",
                    appointment.getEstimatedArrivalTime().toLocalTime());
            }

            createNotification(
                appointment.getPatient(),
                "Infirmier en route",
                message,
                NotificationType.NURSE_ON_WAY,
                appointment.getIsUrgent() != null ? appointment.getIsUrgent() : false
            );
        }
    }
}
