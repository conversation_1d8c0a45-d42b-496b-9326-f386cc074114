package com.medical.homesampling.service;

import com.medical.homesampling.entity.OtpVerification;
import com.medical.homesampling.entity.OtpType;
import com.medical.homesampling.repository.OtpVerificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.List;

@Service
@Transactional
public class OtpService {
    
    @Autowired
    private OtpVerificationRepository otpRepository;
    
    @Autowired
    private EmailService emailService;
    
    private static final int OTP_LENGTH = 6;
    private static final int OTP_EXPIRATION_MINUTES = 10;
    private static final int MAX_OTP_ATTEMPTS_PER_HOUR = 5;
    
    private final SecureRandom random = new SecureRandom();
    
    /**
     * Génère et envoie un code OTP par email
     */
    public String generateAndSendOtp(String email, OtpType type) {
        // Vérifier le nombre de tentatives dans la dernière heure
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        long recentAttempts = otpRepository.countByEmailAndTypeAndCreatedAtAfter(email, type, oneHourAgo);
        
        if (recentAttempts >= MAX_OTP_ATTEMPTS_PER_HOUR) {
            throw new RuntimeException("Trop de tentatives. Veuillez réessayer dans une heure.");
        }
        
        // Invalider tous les OTP précédents pour cet email et ce type
        otpRepository.markAllAsUsedByEmailAndType(email, type);
        
        // Générer un nouveau code OTP
        String otpCode = generateOtpCode();
        
        // Sauvegarder l'OTP en base
        OtpVerification otp = new OtpVerification(email, otpCode, type, OTP_EXPIRATION_MINUTES);
        otpRepository.save(otp);
        
        // Envoyer l'email
        try {
            sendOtpEmail(email, otpCode, type);

            // Affichage spécial pour le développement
            System.out.println("\n" + "🔐".repeat(20));
            System.out.println("🔐 CODE OTP GÉNÉRÉ POUR : " + email);
            System.out.println("🔐 CODE : " + otpCode);
            System.out.println("🔐 TYPE : " + type);
            System.out.println("🔐 EXPIRE DANS : " + OTP_EXPIRATION_MINUTES + " minutes");
            System.out.println("🔐".repeat(20) + "\n");

        } catch (Exception e) {
            // Si l'envoi d'email échoue, marquer l'OTP comme utilisé
            otp.setIsUsed(true);
            otpRepository.save(otp);

            // Même en cas d'erreur d'email, afficher le code pour les tests
            System.out.println("\n" + "⚠️".repeat(20));
            System.out.println("⚠️ ERREUR EMAIL - MAIS CODE OTP DISPONIBLE");
            System.out.println("⚠️ EMAIL : " + email);
            System.out.println("⚠️ CODE OTP : " + otpCode);
            System.out.println("⚠️ UTILISEZ CE CODE POUR TESTER");
            System.out.println("⚠️".repeat(20) + "\n");

            // Ne pas lever d'exception pour permettre les tests
            System.err.println("Email non envoyé, mais code OTP disponible pour test : " + e.getMessage());
        }
        
        return otpCode; // En production, ne pas retourner le code
    }
    
    /**
     * Vérifie un code OTP
     */
    public boolean verifyOtp(String email, String otpCode, OtpType type) {
        var otpOpt = otpRepository.findByEmailAndOtpCodeAndTypeAndIsUsedFalse(email, otpCode, type);
        
        if (otpOpt.isEmpty()) {
            return false;
        }
        
        OtpVerification otp = otpOpt.get();
        
        if (!otp.isValid()) {
            return false;
        }
        
        // Marquer l'OTP comme utilisé
        otp.setIsUsed(true);
        otpRepository.save(otp);
        
        return true;
    }
    
    /**
     * Génère un code OTP aléatoire (numérique uniquement)
     */
    private String generateOtpCode() {
        StringBuilder otp = new StringBuilder();
        for (int i = 0; i < OTP_LENGTH; i++) {
            otp.append(random.nextInt(10)); // Chiffres 0-9
        }
        return otp.toString();
    }

    /**
     * Génère un code OTP alphanumérique (alternative)
     */
    private String generateAlphanumericOtpCode() {
        String chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        StringBuilder otp = new StringBuilder();
        for (int i = 0; i < OTP_LENGTH; i++) {
            otp.append(chars.charAt(random.nextInt(chars.length())));
        }
        return otp.toString();
    }
    
    /**
     * Envoie l'email avec le code OTP
     */
   private void sendOtpEmail(String email, String otpCode, OtpType type) {
    String subject;
    String body;

    switch (type) {
        case EMAIL_VERIFICATION:
            subject = "🔒 Vérification de votre adresse email";
            body = String.format(
                "Bonjour,\n\n" +
                "Vous avez récemment demandé à vérifier votre adresse email.\n\n" +
                "🔐 **Code de vérification** : %s\n" +
                "⏳ **Valide pendant** : %d minutes\n\n" +
                "Si vous n'avez pas initié cette demande, vous pouvez ignorer ce message.\n\n" +
                "Merci de votre confiance.\n\n" +
                "Cordialement,\n" +
                "L’équipe Medical Home Sampling",
                otpCode, OTP_EXPIRATION_MINUTES
            );
            break;

        case PASSWORD_RESET:
            subject = "🔑 Réinitialisation de votre mot de passe";
            body = String.format(
                "Bonjour,\n\n" +
                "Vous avez demandé à réinitialiser votre mot de passe.\n\n" +
                "🔐 **Code de réinitialisation** : %s\n" +
                "⏳ **Valide pendant** : %d minutes\n\n" +
                "Si vous n'avez pas fait cette demande, vous pouvez ignorer ce message.\n\n" +
                "Prenez soin de la sécurité de votre compte.\n\n" +
                "Cordialement,\n" +
                "L’équipe Medical Home Sampling",
                otpCode, OTP_EXPIRATION_MINUTES
            );
            break;

        default:
            subject = "🔐 Votre code de vérification";
            body = String.format(
                "Bonjour,\n\n" +
                "Voici votre code de vérification : %s\n\n" +
                "Merci,\nL’équipe Medical Home Sampling", otpCode
            );
    }

    emailService.sendSimpleEmail(email, subject, body);
}

    
    /**
     * Nettoie les OTP expirés
     */
    @Transactional
    public void cleanupExpiredOtps() {
        otpRepository.deleteExpiredOtps(LocalDateTime.now());
    }
    
    /**
     * Vérifie si un email a des OTP valides en attente
     */
    public boolean hasPendingOtp(String email, OtpType type) {
        List<OtpVerification> pendingOtps = otpRepository.findByEmailAndTypeAndIsUsedFalse(email, type);
        return pendingOtps.stream().anyMatch(OtpVerification::isValid);
    }
}
