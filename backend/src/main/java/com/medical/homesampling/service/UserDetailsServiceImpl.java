package com.medical.homesampling.service;

import com.medical.homesampling.entity.User;
import com.medical.homesampling.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Override
    @Transactional
    public UserDetails loadUserByUsername(String usernameOrEmail) throws UsernameNotFoundException {
        System.out.println("🔍 RECHERCHE UTILISATEUR: " + usernameOrEmail);

        // Essayer d'abord par username
        User user = userRepository.findByUsername(usernameOrEmail).orElse(null);

        // Si pas trouvé par username, essayer par email
        if (user == null) {
            System.out.println("   ❌ Pas trouvé par username, essai par email...");
            user = userRepository.findByEmail(usernameOrEmail).orElse(null);
        }

        if (user == null) {
            System.out.println("   ❌ Utilisateur non trouvé ni par username ni par email");
            throw new UsernameNotFoundException("User not found with username or email: " + usernameOrEmail);
        }

        System.out.println("   ✅ Utilisateur trouvé:");
        System.out.println("      - ID: " + user.getId());
        System.out.println("      - Username: " + user.getUsername());
        System.out.println("      - Email: " + user.getEmail());
        System.out.println("      - Rôle: " + user.getRole());
        System.out.println("      - Activé: " + user.getEnabled());
        System.out.println("      - Email vérifié: " + user.getEmailVerified());

        return user;
    }
}
