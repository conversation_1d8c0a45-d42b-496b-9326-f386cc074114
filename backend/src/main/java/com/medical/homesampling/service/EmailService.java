package com.medical.homesampling.service;

import com.medical.homesampling.entity.Appointment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.time.format.DateTimeFormatter;

@Service
public class EmailService {

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private PdfService pdfService;

    @Value("${app.mail.from:<EMAIL>}")
    private String fromEmail;

    @Value("${app.name:MediSample}")
    private String appName;

    /**
     * Envoie les résultats par email avec PDF en pièce jointe
     */
    public void sendResultsEmail(Appointment appointment, String comments) {
        try {
            // Générer le PDF des résultats
            byte[] pdfBytes = pdfService.generateResultsPdf(appointment, comments);
            
            // Créer le message email
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(fromEmail);
            helper.setTo(appointment.getPatient().getEmail());
            helper.setSubject("Résultats de vos analyses - " + appName);

            // Corps du message
            String emailBody = buildEmailBody(appointment, comments);
            helper.setText(emailBody, true);

            // Ajouter le PDF en pièce jointe
            String fileName = "Resultats_Analyses_" + appointment.getId() + "_" + 
                            appointment.getScheduledDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".pdf";
            helper.addAttachment(fileName, new ByteArrayResource(pdfBytes));

            // Envoyer l'email
            mailSender.send(message);
            
            System.out.println("Email avec résultats envoyé à: " + appointment.getPatient().getEmail());
            
        } catch (MessagingException e) {
            System.err.println("Erreur lors de l'envoi de l'email: " + e.getMessage());
            throw new RuntimeException("Impossible d'envoyer l'email avec les résultats", e);
        }
    }

    /**
     * Envoie une notification simple par email
     */
    public void sendSimpleEmail(String to, String subject, String text) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(text);

            mailSender.send(message);
            System.out.println("✅ Email envoyé avec succès à : " + to);
            System.out.println("📧 Expéditeur : " + fromEmail);
            System.out.println("📝 Sujet : " + subject);

        } catch (Exception e) {
            System.err.println("❌ Erreur lors de l'envoi de l'email à " + to + " : " + e.getMessage());

            // En mode développement, afficher le contenu de l'email dans la console
            System.out.println("\n" + "=".repeat(60));
            System.out.println("📧 EMAIL SIMULÉ (Mode Développement)");
            System.out.println("=".repeat(60));
            System.out.println("De : " + fromEmail);
            System.out.println("À : " + to);
            System.out.println("Sujet : " + subject);
            System.out.println("-".repeat(40));
            System.out.println("Contenu :");
            System.out.println(text);
            System.out.println("=".repeat(60) + "\n");
        }
    }

    /**
     * Construit le corps de l'email avec les résultats
     */
    private String buildEmailBody(Appointment appointment, String comments) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy à HH:mm");
        
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; }
                    .info-box { background-color: #f8f9fa; border-left: 4px solid #2563eb; padding: 15px; margin: 15px 0; }
                    .footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>%s</h1>
                    <p>Résultats de vos analyses médicales</p>
                </div>
                
                <div class="content">
                    <h2>Bonjour %s %s,</h2>
                    
                    <p>Nous avons le plaisir de vous informer que les résultats de vos analyses sont maintenant disponibles.</p>
                    
                    <div class="info-box">
                        <h3>Informations du rendez-vous</h3>
                        <p><strong>Date du prélèvement :</strong> %s</p>
                        <p><strong>Infirmier(e) :</strong> %s</p>
                        <p><strong>Analyses effectuées :</strong> %s</p>
                    </div>
                    
                    <div class="info-box">
                        <h3>Commentaires de l'infirmier(e)</h3>
                        <p>%s</p>
                    </div>
                    
                    <p><strong>Vous trouverez vos résultats détaillés en pièce jointe de cet email.</strong></p>
                    
                    <p>Si vous avez des questions concernant vos résultats, n'hésitez pas à consulter votre médecin traitant.</p>
                    
                    <p>Cordialement,<br>L'équipe %s</p>
                </div>
                
                <div class="footer">
                    <p>Cet email a été envoyé automatiquement. Merci de ne pas y répondre.</p>
                    <p>%s - Service de prélèvements médicaux à domicile</p>
                </div>
            </body>
            </html>
            """,
            appName,
            appointment.getPatient().getFirstName(),
            appointment.getPatient().getLastName(),
            appointment.getScheduledDate().format(formatter),
            appointment.getNurse() != null ? appointment.getNurse().getFullName() : "Non assigné",
            getAnalysisTypesList(appointment),
            comments != null ? comments : "Aucun commentaire particulier.",
            appName,
            appName
        );
    }

    /**
     * Récupère la liste des types d'analyses
     */
    private String getAnalysisTypesList(Appointment appointment) {
        if (appointment.getAnalysisTypes() == null || appointment.getAnalysisTypes().isEmpty()) {
            return "Non spécifié";
        }
        
        return appointment.getAnalysisTypes().stream()
                .map(at -> at.getName())
                .reduce((a, b) -> a + ", " + b)
                .orElse("Non spécifié");
    }
}
