package com.medical.homesampling.service;

import com.medical.homesampling.entity.Appointment;
import com.medical.homesampling.entity.AppointmentStatus;
import com.medical.homesampling.repository.AppointmentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class LocationSharingService {

    @Autowired
    private AppointmentRepository appointmentRepository;

    /**
     * Démarre le partage de position pour un rendez-vous
     */
    public Appointment startLocationSharing(Long appointmentId) {
        Optional<Appointment> appointmentOpt = appointmentRepository.findById(appointmentId);
        
        if (appointmentOpt.isPresent()) {
            Appointment appointment = appointmentOpt.get();
            
            // Activer le partage de position
            appointment.setLocationSharingEnabled(true);
            appointment.setStatus(AppointmentStatus.NURSE_ON_WAY);
            appointment.setLocationLastUpdated(LocalDateTime.now());
            
            System.out.println("🚀 Starting location sharing for appointment: " + appointmentId);
            
            return appointmentRepository.save(appointment);
        } else {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + appointmentId);
        }
    }

    /**
     * Arrête le partage de position pour un rendez-vous
     */
    public Appointment stopLocationSharing(Long appointmentId) {
        Optional<Appointment> appointmentOpt = appointmentRepository.findById(appointmentId);
        
        if (appointmentOpt.isPresent()) {
            Appointment appointment = appointmentOpt.get();
            
            // Désactiver le partage de position
            appointment.setLocationSharingEnabled(false);
            appointment.setLocationLastUpdated(LocalDateTime.now());
            
            System.out.println("🛑 Stopping location sharing for appointment: " + appointmentId);
            
            return appointmentRepository.save(appointment);
        } else {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + appointmentId);
        }
    }

    /**
     * Met à jour la position de l'infirmier
     */
    public Appointment updateNurseLocation(Long appointmentId, Double latitude, Double longitude) {
        Optional<Appointment> appointmentOpt = appointmentRepository.findById(appointmentId);
        
        if (appointmentOpt.isPresent()) {
            Appointment appointment = appointmentOpt.get();
            
            // Vérifier que le partage de position est activé
            if (appointment.getLocationSharingEnabled() != null && appointment.getLocationSharingEnabled()) {
                appointment.setNurseCurrentLatitude(latitude);
                appointment.setNurseCurrentLongitude(longitude);
                appointment.setLocationLastUpdated(LocalDateTime.now());
                
                // Calculer le temps d'arrivée estimé (optionnel)
                // appointment.setEstimatedArrivalTime(calculateEstimatedArrival(appointment));
                
                System.out.println("📍 Updated nurse location for appointment: " + appointmentId + 
                                 " - Lat: " + latitude + ", Lng: " + longitude);
                
                return appointmentRepository.save(appointment);
            } else {
                throw new RuntimeException("Le partage de position n'est pas activé pour ce rendez-vous");
            }
        } else {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + appointmentId);
        }
    }

    /**
     * Marque le prélèvement comme effectué et arrête automatiquement le partage
     */
    public Appointment markSamplingCompleted(Long appointmentId) {
        Optional<Appointment> appointmentOpt = appointmentRepository.findById(appointmentId);
        
        if (appointmentOpt.isPresent()) {
            Appointment appointment = appointmentOpt.get();
            
            // Marquer le prélèvement comme effectué
            appointment.setStatus(AppointmentStatus.SAMPLING_DONE);
            appointment.setSamplingCompleted(true);
            
            // Arrêter automatiquement le partage de position
            appointment.setLocationSharingEnabled(false);
            appointment.setLocationLastUpdated(LocalDateTime.now());
            
            System.out.println("✅ Sampling completed and location sharing stopped for appointment: " + appointmentId);
            
            return appointmentRepository.save(appointment);
        } else {
            throw new RuntimeException("Rendez-vous non trouvé avec l'ID: " + appointmentId);
        }
    }

    /**
     * Vérifie si le partage de position est actif pour un rendez-vous
     */
    public boolean isLocationSharingActive(Long appointmentId) {
        Optional<Appointment> appointmentOpt = appointmentRepository.findById(appointmentId);
        
        if (appointmentOpt.isPresent()) {
            Appointment appointment = appointmentOpt.get();
            return appointment.getLocationSharingEnabled() != null && appointment.getLocationSharingEnabled();
        }
        
        return false;
    }

    /**
     * Calcule le temps d'arrivée estimé (à implémenter avec une API de géolocalisation)
     */
    private LocalDateTime calculateEstimatedArrival(Appointment appointment) {
        // TODO: Implémenter le calcul avec Google Maps API ou similaire
        // Pour l'instant, retourner une estimation simple
        return LocalDateTime.now().plusMinutes(15);
    }
}
