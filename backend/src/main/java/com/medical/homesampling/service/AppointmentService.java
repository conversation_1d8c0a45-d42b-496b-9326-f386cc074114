package com.medical.homesampling.service;

import com.medical.homesampling.dto.AppointmentCreateDto;
import com.medical.homesampling.dto.AppointmentUpdateDto;
import com.medical.homesampling.entity.*;
import com.medical.homesampling.exception.ResourceNotFoundException;
import com.medical.homesampling.repository.AppointmentRepository;
import com.medical.homesampling.repository.AnalysisTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class AppointmentService {

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private AnalysisTypeRepository analysisTypeRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private NotificationService notificationService;

    public Appointment createAppointment(AppointmentCreateDto createDto, User patient) {
        Appointment appointment = new Appointment();
        appointment.setPatient(patient);
        appointment.setScheduledDate(createDto.getScheduledDate());
        appointment.setHomeAddress(createDto.getHomeAddress());
        appointment.setLatitude(createDto.getLatitude());
        appointment.setLongitude(createDto.getLongitude());
        appointment.setSymptoms(createDto.getSymptoms());
        appointment.setSpecialInstructions(createDto.getSpecialInstructions());
        appointment.setIsUrgent(createDto.getIsUrgent() != null ? createDto.getIsUrgent() : false);

        // Set analysis types
        List<AnalysisType> analysisTypes = analysisTypeRepository.findAllById(createDto.getAnalysisTypeIds());
        appointment.setAnalysisTypes(analysisTypes);

        // Calculate total price and duration
        BigDecimal totalPrice = analysisTypes.stream()
                .map(AnalysisType::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        appointment.setTotalPrice(totalPrice);

        Integer totalDuration = analysisTypes.stream()
                .mapToInt(at -> at.getDurationMinutes() != null ? at.getDurationMinutes() : 15)
                .sum();
        appointment.setEstimatedDurationMinutes(totalDuration);

        appointment.setStatus(AppointmentStatus.PENDING);

        Appointment savedAppointment = appointmentRepository.save(appointment);

        // Auto-assign nurse if not urgent, or notify for urgent cases
        if (appointment.getIsUrgent()) {
            notificationService.notifyUrgentRequest(savedAppointment);
        } else {
            assignNurseAutomatically(savedAppointment);
        }

        return savedAppointment;
    }

    public Appointment updateAppointment(Long appointmentId, AppointmentUpdateDto updateDto) {
        Appointment appointment = getAppointmentById(appointmentId);

        if (updateDto.getScheduledDate() != null) {
            appointment.setScheduledDate(updateDto.getScheduledDate());
        }
        if (updateDto.getStatus() != null) {
            updateAppointmentStatus(appointment, updateDto.getStatus());
        }
        if (updateDto.getNurseNotes() != null) {
            appointment.setNurseNotes(updateDto.getNurseNotes());
        }

        return appointmentRepository.save(appointment);
    }

    public void assignNurseAutomatically(Appointment appointment) {
        if (appointment.getLatitude() != null && appointment.getLongitude() != null) {
            List<User> nearestNurses = userService.getNearestAvailableNurses(
                    appointment.getLatitude(), appointment.getLongitude());
            
            if (!nearestNurses.isEmpty()) {
                User assignedNurse = nearestNurses.get(0);
                appointment.setNurse(assignedNurse);
                appointment.setStatus(AppointmentStatus.NURSE_ASSIGNED);
                appointmentRepository.save(appointment);

                // Notify patient and nurse
                notificationService.notifyNurseAssigned(appointment);
            }
        }
    }

    public Appointment assignNurseManually(Long appointmentId, Long nurseId) {
        Appointment appointment = getAppointmentById(appointmentId);
        User nurse = userService.getUserById(nurseId);

        if (nurse.getRole() != Role.NURSE) {
            throw new IllegalArgumentException("User is not a nurse");
        }

        appointment.setNurse(nurse);
        appointment.setStatus(AppointmentStatus.NURSE_ASSIGNED);
        Appointment savedAppointment = appointmentRepository.save(appointment);

        notificationService.notifyNurseAssigned(savedAppointment);
        return savedAppointment;
    }

    public Appointment updateAppointmentStatus(Appointment appointment, AppointmentStatus newStatus) {
        AppointmentStatus oldStatus = appointment.getStatus();
        appointment.setStatus(newStatus);

        // Handle status-specific logic
        switch (newStatus) {
            case IN_PROGRESS:
                appointment.setActualStartTime(LocalDateTime.now());
                break;
            case SAMPLING_DONE:
                appointment.setActualEndTime(LocalDateTime.now());
                break;
        }

        Appointment savedAppointment = appointmentRepository.save(appointment);
        notificationService.notifyStatusChange(savedAppointment, oldStatus, newStatus);
        
        return savedAppointment;
    }

    public Appointment getAppointmentById(Long id) {
        return appointmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Appointment not found with id: " + id));
    }

    public List<Appointment> getAppointmentsByPatient(User patient) {
        return appointmentRepository.findByPatientOrderByScheduledDateDesc(patient);
    }

    public List<Appointment> getAppointmentsByNurse(User nurse) {
        return appointmentRepository.findByNurseAndDateRange(nurse,
                LocalDateTime.now().minusDays(30), LocalDateTime.now().plusDays(30));
    }

    public List<Appointment> getUpcomingAppointmentsByNurse(User nurse) {
        return appointmentRepository.findUpcomingAppointmentsByNurse(nurse, LocalDateTime.now());
    }

    public Appointment saveAppointment(Appointment appointment) {
        appointment.setUpdatedAt(LocalDateTime.now());
        return appointmentRepository.save(appointment);
    }

    public List<Appointment> getActiveAppointmentsWithLocation() {
        // Récupérer tous les rendez-vous actifs avec une position d'infirmier
        return appointmentRepository.findAll().stream()
            .filter(appointment ->
                (appointment.getStatus() == AppointmentStatus.NURSE_ON_WAY ||
                 appointment.getStatus() == AppointmentStatus.IN_PROGRESS) &&
                appointment.getNurseCurrentLatitude() != null &&
                appointment.getNurseCurrentLongitude() != null &&
                appointment.getNurse() != null
            )
            .collect(Collectors.toList());
    }

    public List<Appointment> getUrgentPendingAppointments() {
        return appointmentRepository.findUrgentPendingAppointments();
    }

    public void cancelAppointment(Long appointmentId, String reason) {
        Appointment appointment = getAppointmentById(appointmentId);
        appointment.setStatus(AppointmentStatus.CANCELLED);
        appointment.setNurseNotes(appointment.getNurseNotes() + "\nCancellation reason: " + reason);

        Appointment savedAppointment = appointmentRepository.save(appointment);
        notificationService.notifyAppointmentCancelled(savedAppointment, reason);
    }

    // ========== MÉTHODES ADMIN ==========

    /**
     * Récupère tous les rendez-vous pour l'admin
     */
    public List<Appointment> getAllAppointments() {
        try {
            return appointmentRepository.findAll();
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération de tous les rendez-vous: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Récupère tous les rendez-vous en attente d'affectation
     */
    public List<Appointment> getPendingAppointments() {
        try {
            return appointmentRepository.findByStatus(AppointmentStatus.PENDING);
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération des rendez-vous en attente: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Affecte manuellement un infirmier à un rendez-vous
     */
    public Appointment assignNurseToAppointment(Long appointmentId, Long nurseId) {
        try {
            System.out.println("=== Affectation manuelle ===");
            System.out.println("AppointmentId: " + appointmentId + ", NurseId: " + nurseId);

            Appointment appointment = getAppointmentById(appointmentId);
            System.out.println("Rendez-vous trouvé: " + appointment.getId());

            User nurse = userService.getUserById(nurseId);
            System.out.println("Infirmier trouvé: " + nurse.getUsername() + " (Role: " + nurse.getRole() + ")");

            if (nurse.getRole() != Role.NURSE) {
                throw new IllegalArgumentException("L'utilisateur spécifié n'est pas un infirmier");
            }

            appointment.setNurse(nurse);
            appointment.setStatus(AppointmentStatus.NURSE_ASSIGNED);
            appointment.setUpdatedAt(LocalDateTime.now());

            Appointment savedAppointment = appointmentRepository.save(appointment);
            System.out.println("Rendez-vous sauvegardé avec infirmier: " + savedAppointment.getId());

            // Notifier le patient et l'infirmier
            try {
                notificationService.notifyAppointmentAssigned(savedAppointment);
                System.out.println("Notification envoyée");
            } catch (Exception e) {
                System.err.println("Erreur notification: " + e.getMessage());
                // Ne pas faire échouer l'affectation pour une erreur de notification
            }

            return savedAppointment;
        } catch (Exception e) {
            System.err.println("Erreur dans assignNurseToAppointment: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * Affecte automatiquement l'infirmier le plus proche
     */
    public Appointment autoAssignNearestNurse(Long appointmentId) {
        try {
            System.out.println("=== Affectation automatique ===");
            System.out.println("AppointmentId: " + appointmentId);

            Appointment appointment = getAppointmentById(appointmentId);
            System.out.println("Rendez-vous trouvé: " + appointment.getId());

            if (appointment.getNurse() != null) {
                throw new IllegalStateException("Ce rendez-vous a déjà un infirmier affecté");
            }

            User nearestNurse = findNearestAvailableNurse(appointment);
            System.out.println("Infirmier le plus proche: " + (nearestNurse != null ? nearestNurse.getUsername() : "aucun"));

            if (nearestNurse == null) {
                throw new RuntimeException("Aucun infirmier disponible trouvé");
            }

            return assignNurseToAppointment(appointmentId, nearestNurse.getId());
        } catch (Exception e) {
            System.err.println("Erreur dans autoAssignNearestNurse: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * Affecte automatiquement tous les rendez-vous en attente
     */
    public Map<String, Object> autoAssignAllPendingAppointments() {
        List<Appointment> pendingAppointments = getPendingAppointments();

        int totalPending = pendingAppointments.size();
        int assigned = 0;
        int failed = 0;
        List<String> errors = new ArrayList<>();

        for (Appointment appointment : pendingAppointments) {
            try {
                autoAssignNearestNurse(appointment.getId());
                assigned++;
            } catch (Exception e) {
                failed++;
                errors.add("Rendez-vous #" + appointment.getId() + ": " + e.getMessage());
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("totalPending", totalPending);
        result.put("assigned", assigned);
        result.put("failed", failed);
        result.put("errors", errors);

        return result;
    }

    /**
     * Met à jour le statut d'un rendez-vous
     */
    public Appointment updateAppointmentStatus(Long appointmentId, AppointmentStatus status) {
        Appointment appointment = getAppointmentById(appointmentId);
        AppointmentStatus oldStatus = appointment.getStatus();

        appointment.setStatus(status);
        appointment.setUpdatedAt(LocalDateTime.now());

        Appointment savedAppointment = appointmentRepository.save(appointment);

        // Notifier le changement de statut
        notificationService.notifyAdminStatusChange(savedAppointment, oldStatus, status);

        return savedAppointment;
    }

    /**
     * Récupère la liste des infirmiers disponibles
     */
    public List<User> getAvailableNurses() {
        try {
            return userService.getAllUsers().stream()
                    .filter(user -> user.getRole() == Role.NURSE)
                    .filter(User::getEnabled)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération des infirmiers disponibles: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Trouve l'infirmier disponible le plus proche du patient
     */
    private User findNearestAvailableNurse(Appointment appointment) {
        List<User> availableNurses = getAvailableNurses();

        if (availableNurses.isEmpty()) {
            return null;
        }

        // Extraire les coordonnées du patient depuis l'adresse
        double[] patientCoords = extractCoordinatesFromAddress(appointment.getHomeAddress());

        if (patientCoords == null) {
            // Si on ne peut pas extraire les coordonnées, retourner le premier infirmier disponible
            return availableNurses.get(0);
        }

        User nearestNurse = null;
        double minDistance = Double.MAX_VALUE;

        for (User nurse : availableNurses) {
            // Vérifier si l'infirmier n'est pas déjà surchargé
            if (isNurseAvailable(nurse)) {
                double[] nurseCoords = extractCoordinatesFromAddress(nurse.getAddress());

                if (nurseCoords != null) {
                    double distance = calculateDistance(patientCoords[0], patientCoords[1],
                                                      nurseCoords[0], nurseCoords[1]);

                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestNurse = nurse;
                    }
                }
            }
        }

        // Si aucun infirmier avec coordonnées n'est trouvé, retourner le premier disponible
        return nearestNurse != null ? nearestNurse :
               availableNurses.stream().filter(this::isNurseAvailable).findFirst().orElse(null);
    }

    /**
     * Vérifie si un infirmier est disponible (pas trop de rendez-vous actifs)
     */
    private boolean isNurseAvailable(User nurse) {
        long activeAppointments = appointmentRepository.countByNurseAndStatusIn(
            nurse,
            Arrays.asList(AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS)
        );

        // Limite arbitraire : maximum 5 rendez-vous actifs par infirmier
        return activeAppointments < 5;
    }

    /**
     * Extrait les coordonnées latitude/longitude d'une adresse
     * Format attendu : "latitude,longitude" ou recherche dans le texte
     */
    private double[] extractCoordinatesFromAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return null;
        }

        // Chercher un pattern de coordonnées dans l'adresse
        String[] parts = address.split(",");
        if (parts.length >= 2) {
            try {
                double lat = Double.parseDouble(parts[0].trim());
                double lon = Double.parseDouble(parts[1].trim());

                // Vérifier que les coordonnées sont dans des plages valides
                if (lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180) {
                    return new double[]{lat, lon};
                }
            } catch (NumberFormatException e) {
                // Ignorer et continuer
            }
        }

        // Si pas de coordonnées trouvées, retourner des coordonnées par défaut (Tunis)
        return new double[]{36.8065, 10.1815};
    }

    /**
     * Calcule la distance entre deux points géographiques (formule de Haversine)
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // Rayon de la Terre en kilomètres

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);

        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c; // Distance en kilomètres
    }

    /**
     * Démarre le partage de position pour un rendez-vous
     */
    public Appointment startLocationTracking(Long appointmentId, User nurse) {
        try {
            System.out.println("Démarrage du partage de position pour le rendez-vous: " + appointmentId);

            Appointment appointment = appointmentRepository.findById(appointmentId)
                    .orElseThrow(() -> new RuntimeException("Rendez-vous non trouvé avec l'ID: " + appointmentId));

            // Vérifier que l'infirmier est bien assigné à ce rendez-vous
            if (!appointment.getNurse().getId().equals(nurse.getId())) {
                throw new RuntimeException("Cet infirmier n'est pas assigné à ce rendez-vous");
            }

            // Activer le partage de position et changer le statut
            appointment.setLocationSharingEnabled(true);
            appointment.setStatus(AppointmentStatus.NURSE_ON_WAY);
            appointment.setLocationLastUpdated(LocalDateTime.now());
            appointment.setUpdatedAt(LocalDateTime.now());

            // Calculer l'heure d'arrivée estimée (exemple: 30 minutes)
            appointment.setEstimatedArrivalTime(LocalDateTime.now().plusMinutes(30));

            Appointment savedAppointment = appointmentRepository.save(appointment);

            // Notifier le patient que l'infirmier est en route
            if (notificationService != null) {
                notificationService.notifyPatientNurseOnWay(savedAppointment);
            }

            System.out.println("Partage de position activé avec succès");
            return savedAppointment;

        } catch (Exception e) {
            System.err.println("Erreur lors du démarrage du partage de position: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Erreur lors du démarrage du partage de position", e);
        }
    }

    /**
     * Met à jour la position actuelle de l'infirmier
     */
    public Appointment updateNurseLocation(Long appointmentId, User nurse, Double latitude, Double longitude) {
        try {
            System.out.println("Mise à jour de la position de l'infirmier pour le rendez-vous: " + appointmentId);

            Appointment appointment = appointmentRepository.findById(appointmentId)
                    .orElseThrow(() -> new RuntimeException("Rendez-vous non trouvé avec l'ID: " + appointmentId));

            // Vérifier que l'infirmier est bien assigné et que le partage est activé
            if (!appointment.getNurse().getId().equals(nurse.getId())) {
                throw new RuntimeException("Cet infirmier n'est pas assigné à ce rendez-vous");
            }

            if (!appointment.getLocationSharingEnabled()) {
                throw new RuntimeException("Le partage de position n'est pas activé pour ce rendez-vous");
            }

            // Mettre à jour la position
            appointment.setNurseCurrentLatitude(latitude);
            appointment.setNurseCurrentLongitude(longitude);
            appointment.setLocationLastUpdated(LocalDateTime.now());
            appointment.setUpdatedAt(LocalDateTime.now());

            // Recalculer l'heure d'arrivée estimée basée sur la distance
            if (appointment.getLatitude() != null && appointment.getLongitude() != null) {
                double distance = calculateDistance(latitude, longitude,
                                                  appointment.getLatitude(), appointment.getLongitude());
                // Estimation: 30 km/h en moyenne en ville
                int estimatedMinutes = (int) Math.ceil(distance / 30.0 * 60);
                appointment.setEstimatedArrivalTime(LocalDateTime.now().plusMinutes(estimatedMinutes));
            }

            Appointment savedAppointment = appointmentRepository.save(appointment);

            System.out.println("Position mise à jour avec succès");
            return savedAppointment;

        } catch (Exception e) {
            System.err.println("Erreur lors de la mise à jour de la position: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Erreur lors de la mise à jour de la position", e);
        }
    }

    /**
     * Arrête le partage de position après le prélèvement
     */
    public Appointment stopLocationTracking(Long appointmentId, User nurse) {
        try {
            System.out.println("Arrêt du partage de position pour le rendez-vous: " + appointmentId);

            Appointment appointment = appointmentRepository.findById(appointmentId)
                    .orElseThrow(() -> new RuntimeException("Rendez-vous non trouvé avec l'ID: " + appointmentId));

            // Vérifier que l'infirmier est bien assigné
            if (!appointment.getNurse().getId().equals(nurse.getId())) {
                throw new RuntimeException("Cet infirmier n'est pas assigné à ce rendez-vous");
            }

            // Désactiver le partage de position
            appointment.setLocationSharingEnabled(false);
            appointment.setNurseCurrentLatitude(null);
            appointment.setNurseCurrentLongitude(null);
            appointment.setLocationLastUpdated(null);
            appointment.setEstimatedArrivalTime(null);
            appointment.setUpdatedAt(LocalDateTime.now());

            Appointment savedAppointment = appointmentRepository.save(appointment);

            System.out.println("Partage de position désactivé avec succès");
            return savedAppointment;

        } catch (Exception e) {
            System.err.println("Erreur lors de l'arrêt du partage de position: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Erreur lors de l'arrêt du partage de position", e);
        }
    }

    /**
     * Récupère tous les rendez-vous assignés à un infirmier
     */
    public List<Appointment> getNurseAppointments(User nurse) {
        try {
            System.out.println("Récupération des rendez-vous pour l'infirmier: " + nurse.getUsername());
            List<Appointment> appointments = appointmentRepository.findByNurse(nurse);
            System.out.println("Rendez-vous trouvés: " + appointments.size());
            return appointments;
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération des rendez-vous de l'infirmier: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }


}
