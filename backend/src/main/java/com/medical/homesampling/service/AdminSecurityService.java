package com.medical.homesampling.service;

import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.Map;

@Service
public class AdminSecurityService {

    /**
     * Enregistre un événement de sécurité (version simplifiée)
     * Pour l'instant, on log juste dans la console
     */
    public void logSecurityEvent(String action, String resource, Map<String, Object> details) {
        try {
            LocalDateTime timestamp = LocalDateTime.now();
            
            System.out.println("=== SECURITY EVENT ===");
            System.out.println("Timestamp: " + timestamp);
            System.out.println("Action: " + action);
            System.out.println("Resource: " + resource);
            
            if (details != null && !details.isEmpty()) {
                System.out.println("Details:");
                details.forEach((key, value) -> 
                    System.out.println("  " + key + ": " + value)
                );
            }
            
            System.out.println("=====================");
            
        } catch (Exception e) {
            // En cas d'erreur lors du logging, on ne veut pas faire échouer l'opération principale
            System.err.println("Erreur lors de l'enregistrement de l'événement de sécurité: " + e.getMessage());
        }
    }

    /**
     * Enregistre un événement de sécurité avec échec
     */
    public void logSecurityEventFailure(String action, String resource, String errorMessage, Map<String, Object> details) {
        try {
            LocalDateTime timestamp = LocalDateTime.now();
            
            System.out.println("=== SECURITY EVENT (FAILURE) ===");
            System.out.println("Timestamp: " + timestamp);
            System.out.println("Action: " + action);
            System.out.println("Resource: " + resource);
            System.out.println("Error: " + errorMessage);
            
            if (details != null && !details.isEmpty()) {
                System.out.println("Details:");
                details.forEach((key, value) -> 
                    System.out.println("  " + key + ": " + value)
                );
            }
            
            System.out.println("================================");
            
        } catch (Exception e) {
            System.err.println("Erreur lors de l'enregistrement de l'événement de sécurité (échec): " + e.getMessage());
        }
    }
}
