# Configuration pour l'environnement de développement

# Email configuration - Mode simulation
spring.mail.host=localhost
spring.mail.port=1025
spring.mail.username=
spring.mail.password=
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# Configuration de l'application
app.name=MediSample (DEV)
app.mail.from=<EMAIL>

# Logs plus détaillés en développement
logging.level.com.medical.homesampling.service.EmailService=DEBUG
logging.level.org.springframework.mail=DEBUG

# Désactiver la vérification de connexion email
spring.mail.test-connection=false
