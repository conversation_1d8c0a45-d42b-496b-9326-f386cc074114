# Medical Home Sampling Application - Docker Configuration
spring.application.name=medical-home-sampling

# Server Configuration
server.port=8080
server.servlet.context-path=/api

# Timeout Configuration
server.tomcat.connection-timeout=60000
server.tomcat.keep-alive-timeout=60000
spring.mvc.async.request-timeout=60000

# PostgreSQL Database Configuration for Docker
spring.datasource.url=*******************************/${DB_NAME:medical_home_sampling}
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:postgres}
spring.datasource.driver-class-name=org.postgresql.Driver

# Connection pool settings
spring.datasource.hikari.maximum-pool-size=${DB_POOL_SIZE:20}
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=${DDL_AUTO:update}
spring.jpa.show-sql=${SHOW_SQL:false}
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# JWT Configuration
spring.security.jwt.secret=${JWT_SECRET:mySecretKey123456789012345678901234567890}
spring.security.jwt.expiration=${JWT_EXPIRATION:86400000}

# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operations-sorter=method

# Logging Configuration
logging.level.com.medical.homesampling=${LOG_LEVEL:INFO}
logging.level.org.springframework.security=${SECURITY_LOG_LEVEL:WARN}
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=logs/medical-home-sampling.log

# CORS Configuration
cors.allowed-origins=${CORS_ORIGINS:http://localhost,http://frontend}
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true

# SQL Initialization
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=${INIT_MODE:always}
spring.sql.init.data-locations=classpath:data.sql

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
management.health.db.enabled=true

# Production optimizations
spring.jpa.open-in-view=false
spring.datasource.hikari.leak-detection-threshold=60000
