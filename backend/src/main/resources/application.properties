# Medical Home Sampling Application Configuration
# PostgreSQL Database Configuration
spring.application.name=medical-home-sampling

# Server Configuration
server.address=0.0.0.0
server.port=8080
server.servlet.context-path=/api



# PostgreSQL Database Configuration (utilise les variables d'environnement Docker)
spring.datasource.url=jdbc:postgresql://${DB_HOST:localhost}:5432/${DB_NAME:medical_home_sampling}
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:admin}
spring.datasource.driver-class-name=org.postgresql.Driver

# Connection pool settings
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=20000

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# JWT Configuration
spring.security.jwt.secret=mySecretKey123456789012345678901234567890
spring.security.jwt.expiration=86400000

# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operations-sorter=method

# Logging Configuration
logging.level.com.medical.homesampling=DEBUG
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=logs/medical-home-sampling.log

# CORS Configuration géré dans SecurityConfig.java

# SQL Initialization
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=always
spring.sql.init.data-locations=classpath:data.sql

# Application configuration
app.name=MediSample
app.mail.from=<EMAIL>

# Email configuration (Gmail SMTP pour test)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=yfhc uraa ycku fvrv
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

# En mode développement, simuler l'envoi d'emails
spring.mail.test-connection=false
