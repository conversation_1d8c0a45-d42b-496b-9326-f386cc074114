-- Initial data for Medical Home Sampling Application
-- PostgreSQL compatible SQL

-- Insert default admin user (password: admin123)
-- BCrypt encoded password for 'admin123'
INSERT INTO users (username, email, password, first_name, last_name, phone, role, enabled, created_at)
SELECT 'admin', '<EMAIL>', '$2a$10$DowJoayNM.ING8.F8Mz9FeKJV2jIuOdkuuxm07EwjEAEOtZQQRqIu', 'Admin', 'System', '+33123456789', 'ADMIN', true, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'admin');

-- Insert sample analysis types
INSERT INTO analysis_types (name, description, price, duration_minutes, preparation_required, created_at)
SELECT * FROM (VALUES
    ('Prise de sang complète', 'Analyse sanguine complète avec numération formule sanguine', 45.00, 15, false, CURRENT_TIMESTAMP),
    ('Test COVID-19', 'Test PCR pour détection du COVID-19', 65.00, 10, false, CURRENT_TIMESTAMP),
    ('Glycémie', 'Mesure du taux de glucose dans le sang', 25.00, 5, true, CURRENT_TIMESTAMP),
    ('Cholestérol', 'Analyse du taux de cholestérol', 35.00, 10, true, CURRENT_TIMESTAMP),
    ('Analyse d''urine', 'Examen cytobactériologique des urines', 30.00, 5, false, CURRENT_TIMESTAMP)
) AS v(name, description, price, duration_minutes, preparation_required, created_at)
WHERE NOT EXISTS (SELECT 1 FROM analysis_types WHERE analysis_types.name = v.name);

-- Insert sample symptoms for AI chatbot suggestions
INSERT INTO symptoms (name, description, suggested_analyses, created_at)
SELECT * FROM (VALUES
    ('Fatigue persistante', 'Sensation de fatigue qui dure plusieurs semaines', '1,3,4', CURRENT_TIMESTAMP),
    ('Fièvre', 'Température corporelle élevée', '2,1', CURRENT_TIMESTAMP),
    ('Maux de tête fréquents', 'Céphalées récurrentes', '1,3', CURRENT_TIMESTAMP),
    ('Troubles digestifs', 'Problèmes de digestion, nausées', '1,5', CURRENT_TIMESTAMP),
    ('Essoufflement', 'Difficultés respiratoires', '1,2', CURRENT_TIMESTAMP)
) AS v(name, description, suggested_analyses, created_at)
WHERE NOT EXISTS (SELECT 1 FROM symptoms WHERE symptoms.name = v.name);

-- Insert sample nurse (password: nurse123)
INSERT INTO users (username, email, password, first_name, last_name, phone, role, enabled, created_at, address, latitude, longitude)
SELECT 'nurse1', '<EMAIL>', '$2a$10$DowJoayNM.ING8.F8Mz9FeKJV2jIuOdkuuxm07EwjEAEOtZQQRqIu', 'Marie', 'Dubois', '+33123456790', 'NURSE', true, CURRENT_TIMESTAMP, '123 Rue de la Santé, 75001 Paris', 48.8566, 2.3522
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'nurse1');

-- Insert sample patient (password: patient123)
INSERT INTO users (username, email, password, first_name, last_name, phone, role, enabled, created_at, address, latitude, longitude)
SELECT 'patient1', '<EMAIL>', '$2a$10$DowJoayNM.ING8.F8Mz9FeKJV2jIuOdkuuxm07EwjEAEOtZQQRqIu', 'Jean', 'Martin', '+33123456791', 'PATIENT', true, CURRENT_TIMESTAMP, '456 Avenue des Patients, 75002 Paris', 48.8606, 2.3376
WHERE NOT EXISTS (SELECT 1 FROM users WHERE username = 'patient1');
