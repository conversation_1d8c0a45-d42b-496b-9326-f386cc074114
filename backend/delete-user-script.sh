#!/bin/bash

# Script pour supprimer l'utilisateur avec l'email <EMAIL>
# Ce script utilise l'API REST existante

echo "🗑️ Script de suppression d'utilisateur"
echo "Email cible: <EMAIL>"
echo "=================================="

# Configuration
API_BASE_URL="http://localhost:8080/api"
EMAIL="<EMAIL>"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Étape 1: Connexion en tant qu'admin...${NC}"

# 1. Se connecter en tant qu'admin pour obtenir un token
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE_URL/auth/signin" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

# Extraire le token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo -e "${RED}❌ Erreur: Impossible de se connecter en tant qu'admin${NC}"
    echo "Réponse: $LOGIN_RESPONSE"
    exit 1
fi

echo -e "${GREEN}✅ Connexion admin réussie${NC}"

echo -e "${YELLOW}Étape 2: Recherche de l'utilisateur...${NC}"

# 2. Récupérer tous les utilisateurs pour trouver celui avec l'email
USERS_RESPONSE=$(curl -s -X GET "$API_BASE_URL/admin/patients" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

# Chercher l'ID de l'utilisateur avec cet email
USER_ID=$(echo $USERS_RESPONSE | grep -o '"id":[0-9]*[^}]*"email":"'$EMAIL'"' | grep -o '"id":[0-9]*' | cut -d':' -f2)

if [ -z "$USER_ID" ]; then
    echo -e "${YELLOW}⚠️ Utilisateur non trouvé dans les patients, recherche dans les infirmiers...${NC}"
    
    # Chercher dans les infirmiers
    NURSES_RESPONSE=$(curl -s -X GET "$API_BASE_URL/admin/nurses" \
      -H "Authorization: Bearer $TOKEN" \
      -H "Content-Type: application/json")
    
    USER_ID=$(echo $NURSES_RESPONSE | grep -o '"id":[0-9]*[^}]*"email":"'$EMAIL'"' | grep -o '"id":[0-9]*' | cut -d':' -f2)
fi

if [ -z "$USER_ID" ]; then
    echo -e "${RED}❌ Utilisateur avec l'email $EMAIL non trouvé${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Utilisateur trouvé avec ID: $USER_ID${NC}"

echo -e "${YELLOW}Étape 3: Suppression de l'utilisateur...${NC}"

# 3. Supprimer l'utilisateur
DELETE_RESPONSE=$(curl -s -X DELETE "$API_BASE_URL/users/$USER_ID" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

# Vérifier le succès
if echo $DELETE_RESPONSE | grep -q "successfully"; then
    echo -e "${GREEN}✅ Utilisateur supprimé avec succès !${NC}"
    echo "Réponse: $DELETE_RESPONSE"
else
    echo -e "${RED}❌ Erreur lors de la suppression${NC}"
    echo "Réponse: $DELETE_RESPONSE"
    exit 1
fi

echo -e "${GREEN}🎉 Suppression terminée avec succès !${NC}"
echo "L'utilisateur avec l'email $EMAIL a été désactivé dans la base de données."
