# 🌐 Déploiement sur serveur distant

## 📋 Guide pour déployer l'application sur un serveur avec IP publique



## 🔧 Configuration pour serveur distant

### 1. **<PERSON><PERSON><PERSON> et configurer le projet**

```bash
# Sur le serveur distant
git clone <votre-repo-gitlab>
cd *********************

# Copier la configuration
cp .env.example .env
```

### 2. **Modifier le fichier .env pour le serveur distant**

```bash
# Éditer le fichier .env
nano .env
```

**Configuration recommandée pour serveur distant :**

```env
# Database Configuration
DB_NAME=medical_home_sampling
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_POOL_SIZE=20

# Application Configuration
SPRING_PROFILES_ACTIVE=dev
DDL_AUTO=update
SHOW_SQL=false
LOG_LEVEL=INFO
SECURITY_LOG_LEVEL=WARN
INIT_MODE=always

# Security Configuration
JWT_SECRET=mySecretKey123456789012345678901234567890
JWT_EXPIRATION=86400000

# CORS Configuration - IMPORTANT pour serveur distant
CORS_ORIGINS=http://localhost:3000,http://***************:3000,http://***************:*

# Frontend Configuration
FRONTEND_PORT=3000
```

**⚠️ IMPORTANT :** Remplacez `***************` par l'IP réelle de votre serveur.

### 3. **Déploiement**

```bash
# Démarrer l'application
./deploy.sh dev

# Ou manuellement
docker-compose up -d --build
```

### 4. **Vérification**

```bash
# Vérifier l'état des services
docker-compose ps

# Tester l'API
curl -I http://localhost:3000/api/auth/login

# Vérifier les logs
docker-compose logs -f
```

## 🌐 Accès à l'application

### URLs d'accès :
- **Interface web** : `http://***************:3000`
- **API** : `http://***************:3000/api/*`

### 🔍 Vérification du bon fonctionnement

1. **Ouvrir l'application** dans le navigateur
2. **Ouvrir les outils de développement** (F12)
3. **Aller dans l'onglet Network**
4. **Essayer de se connecter**
5. **Vérifier que les requêtes API** pointent vers `/api/auth/login` (URL relative)


## 🛠️ Résolution des problèmes

### Problème : Requêtes vers IP locale
**Solution :** L'application utilise maintenant des URLs relatives (`/api`) qui passent par le proxy Nginx.

### Problème : Erreur CORS
**Solution :** Ajoutez l'IP de votre serveur dans `CORS_ORIGINS` du fichier `.env`.

### Problème : Port 3000 occupé
**Solution :** Changez `FRONTEND_PORT=3001` dans le fichier `.env`.

## 🔒 Sécurité pour production

Pour un déploiement en production, modifiez :

```env
# Sécurité renforcée
SPRING_PROFILES_ACTIVE=prod
DDL_AUTO=validate
SHOW_SQL=false
LOG_LEVEL=WARN

# Clé JWT sécurisée (générez une nouvelle clé)
JWT_SECRET=VotreCleSuperSecuriseeDe32CaracteresMinimum

# CORS restrictif (seulement votre domaine)
CORS_ORIGINS=https://votre-domaine.com
```

## 📝 Notes importantes

1. **Proxy Nginx** : Toutes les requêtes API passent par Nginx qui les redirige vers le backend
2. **URLs relatives** : L'application utilise `/api` au lieu d'URLs absolues
3. **CORS** : Configuré pour accepter les requêtes depuis l'IP du serveur
4. **Sécurité** : Headers de sécurité configurés dans Nginx
