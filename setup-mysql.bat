@echo off
echo Setting up MySQL for Medical Home Sampling Application...
echo.

echo Creating database and user...
echo Please make sure MySQL is running on localhost:3306
echo.

echo You can use the following MySQL commands:
echo.
echo CREATE DATABASE IF NOT EXISTS medical_home_sampling;
echo CREATE USER IF NOT EXISTS 'medical_user'@'localhost' IDENTIFIED BY 'medical_password';
echo GRANT ALL PRIVILEGES ON medical_home_sampling.* TO 'medical_user'@'localhost';
echo FLUSH PRIVILEGES;
echo.

echo Or use root user with password 'root' as configured in application.yml
echo.

echo Starting MySQL service (if not already running)...
net start mysql80

echo.
echo MySQL setup complete!
echo You can now start the Spring Boot application.
echo.
pause
