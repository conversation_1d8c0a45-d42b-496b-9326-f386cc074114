# 📋 Suivi des prélèvements d'analyses médicales à domicile

## 📐 Architecture technique
- **Backend**: Spring Boot + Spring Security + JPA (MySQL)
- **Frontend**: Angular (formulaires, tableaux, notifications, responsive)
- **Communication**: REST API (JSON)
- **Sécurité**: JWT + rôle/permission

## 🎭 Les acteurs du système

| Rôle | Description |
|------|-------------|
| 👤 **Patient** | Demande un prélèvement à domicile, suit l'état d'avancement de la demande, consulte les résultats |
| 🧑‍⚕️ **Infirmier** | Reçoit les demandes, effectue les prélèvements, met à jour le statut des demandes |
| 🧑‍💼 **Administrateur** | Gère les utilisateurs, les analyses, les rendez-vous, supervise les statistiques globales |

## 🚀 Fonctionnalités principales

### Pour les Patients
- Inscription et connexion
- Demande de rendez-vous pour prélèvement à domicile
- Saisie d'adresse avec visualisation sur carte
- Choix du type d'analyse
- Chatbot IA pour suggestion de tests selon symptômes
- Suivi en temps réel de l'infirmier
- Consultation des résultats

### Pour les Infirmiers
- Tableau de bord des missions quotidiennes
- Confirmation des prélèvements effectués
- Mise à jour du statut des demandes
- Gestion de la disponibilité

### Pour les Administrateurs
- Gestion des utilisateurs
- Affectation des infirmiers
- Tableaux de bord et statistiques
- Supervision du système

## 🔴 Module "urgence"
- Signalement de besoins urgents par les patients
- Alertes en temps réel aux infirmiers les plus proches

## 📊 États des prélèvements
En attente → Prélevé → En cours d'analyse → Résultats disponibles

## � Déploiement rapide

### Prérequis
- Docker et Docker Compose installés
- Git pour cloner le projet

### Étapes de déploiement

1. **Cloner le projet**
```bash
git clone <votre-repo-gitlab>
cd *********************
```

2. **Configuration de l'environnement**
```bash
# Copier le fichier d'exemple
cp .env.example .env

# Ajuster les variables selon votre environnement
# Notamment CORS_ORIGINS avec votre IP réseau
```

3. **Démarrage de l'application**
```bash
# Mode développement (recommandé pour le premier démarrage)
docker-compose up -d --build

# Mode production
docker-compose -f docker-compose.prod.yml up -d --build
```

4. **Accès à l'application**
- **Interface web** : http://localhost:3000
- **Depuis le réseau** : http://[VOTRE-IP]:3000

### Résolution des problèmes courants

- **Erreur 502 Bad Gateway** : Attendez que le backend termine de démarrer (1-2 minutes)
- **Problème de port** : Modifiez `FRONTEND_PORT` dans le fichier `.env`
- **Accès réseau** : Ajustez `CORS_ORIGINS` avec votre plage IP réseau

## �🛠️ Structure du projet

```
*********************/
├── backend/                 # Spring Boot application
│   ├── src/main/java/
│   ├── src/main/resources/
│   └── pom.xml
├── frontend/               # Angular application
│   ├── src/
│   ├── angular.json
│   └── package.json
└── README.md
```

## 🚀 Démarrage rapide

### 1. Configuration de MySQL

Assurez-vous que MySQL est installé et en cours d'exécution sur le port 3306.

#### Option A: Utilisateur root sans mot de passe (configuration par défaut)
```sql
-- Se connecter à MySQL en tant que root
mysql -u root

-- Créer la base de données
CREATE DATABASE IF NOT EXISTS medical_home_sampling;
```

#### Option B: Créer un utilisateur dédié
```sql
-- Se connecter à MySQL en tant que root
mysql -u root -p

-- Créer la base de données et l'utilisateur
CREATE DATABASE IF NOT EXISTS medical_home_sampling;
CREATE USER IF NOT EXISTS 'medical_user'@'localhost' IDENTIFIED BY 'medical_password';
GRANT ALL PRIVILEGES ON medical_home_sampling.* TO 'medical_user'@'localhost';
FLUSH PRIVILEGES;
```

Si vous utilisez l'Option B, modifiez `backend/src/main/resources/application.yml` :
```yaml
spring:
  datasource:
    username: medical_user
    password: medical_password
```

### 2. Démarrage automatique (Windows)
```bash
# Exécuter le script de démarrage
start-dev.bat
```

### 3. Démarrage manuel

#### Backend (Spring Boot)
```bash
cd backend
mvn spring-boot:run
```

#### Frontend (Angular)
```bash
cd frontend
npm install
ng serve
```

## 🌐 Accès à l'application

- **Frontend**: http://localhost:4200
- **Backend API**: http://localhost:8080/api
- **Documentation Swagger**: http://localhost:8080/api/swagger-ui.html

## 👥 Comptes de test

| Rôle | Nom d'utilisateur | Mot de passe | Description |
|------|------------------|--------------|-------------|
| Admin | admin | admin123 | Administrateur système |
| Infirmière | nurse1 | nurse123 | Marie Dubois |
| Patient | patient1 | patient123 | Jean Martin |

## 🐛 Dépannage

### Problèmes courants

1. **Erreur de connexion MySQL**
   - Vérifiez que MySQL est démarré : `net start mysql80`
   - Vérifiez les identifiants dans `application.yml`
   - Assurez-vous que la base de données existe

2. **Port 8080 déjà utilisé**
   - Changez le port dans `application.yml`: `server.port: 8081`

3. **Erreur de compilation Angular**
   - Supprimez `node_modules` et relancez `npm install`

## 📝 API Documentation
L'API REST est documentée avec Swagger/OpenAPI et accessible à `/swagger-ui.html`

## 🔐 Sécurité
- Authentification JWT
- Autorisation basée sur les rôles
- Chiffrement des données sensibles
- Protection CORS
