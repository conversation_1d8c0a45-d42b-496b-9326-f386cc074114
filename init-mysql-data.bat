@echo off
echo Initializing MySQL data for Medical Home Sampling Application...
echo.

echo Connecting to MySQL and executing initialization script...
echo Please make sure MySQL is running and accessible.
echo.

echo Trying to connect to MySQL...
mysql -u root -e "USE medical_home_sampling; SOURCE init-users.sql;" 2>nul

if %errorlevel% equ 0 (
    echo.
    echo ✅ Data initialization completed successfully!
    echo.
    echo Default users created:
    echo - Admin: admin / admin123
    echo - Nurse: nurse1 / nurse123
    echo - Patient: patient1 / patient123
    echo.
) else (
    echo.
    echo ❌ Failed to connect to MySQL or execute script.
    echo Please check:
    echo 1. MySQL is running
    echo 2. Database 'medical_home_sampling' exists
    echo 3. User 'root' has access
    echo.
    echo You can also try manually:
    echo mysql -u root
    echo USE medical_home_sampling;
    echo SOURCE init-users.sql;
    echo.
)

pause
